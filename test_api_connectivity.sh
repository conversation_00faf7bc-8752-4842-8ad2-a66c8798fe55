#!/bin/bash

echo "=== API连通性测试脚本 ==="
echo ""

# 设置API基础地址
API_BASE="http://114.132.154.140:5000/api"

echo "测试的API地址: $API_BASE"
echo ""

echo "1. 测试调度器健康检查API..."
curl -s -w "HTTP状态码: %{http_code}\n" "$API_BASE/scheduler/health" || echo "请求失败"
echo ""

echo "2. 测试调度器状态API..."
curl -s -w "HTTP状态码: %{http_code}\n" "$API_BASE/scheduler/status" || echo "请求失败"
echo ""

echo "3. 测试认证登录API..."
curl -s -X POST -H "Content-Type: application/json" \
     -d '{"username":"test","password":"test"}' \
     -w "HTTP状态码: %{http_code}\n" \
     "$API_BASE/auth/login" || echo "请求失败"
echo ""

echo "4. 测试基础健康检查..."
curl -s -w "HTTP状态码: %{http_code}\n" "$API_BASE/health" || echo "请求失败"
echo ""

echo "=== 端口监听检查 ==="
echo "检查5000端口是否开放..."
nc -zv 114.132.154.140 5000 2>&1 || echo "端口5000无法连接"
echo ""

echo "=== 本地端口检查 ==="
echo "检查本地5000端口监听状态..."
netstat -tlnp | grep :5000 || echo "本地5000端口未监听" 