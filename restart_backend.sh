#!/bin/bash

echo "=== 重启后端服务脚本 ==="
echo ""

echo "问题: 添加患者时出现ObjectId序列化错误"
echo "修复: 已增强序列化函数和添加调试信息"
echo ""

echo "请在Ubuntu服务器上执行以下步骤："
echo ""

echo "1. 停止当前后端服务"
echo "   在运行后端的终端按 Ctrl+C"
echo ""

echo "2. 重新启动后端服务"
echo "   cd backend"
echo "   python smart_start.py"
echo ""

echo "3. 查看调试信息"
echo "   尝试添加患者时，后端控制台会显示详细的调试信息："
echo "   🔍 开始添加患者处理..."
echo "   📦 接收到的数据: ..."
echo "   💾 正在插入数据..."
echo "   ✅ 插入成功，ID: ..."
echo "   🔄 正在序列化文档..."
echo "   ✅ 序列化完成"
echo "   📤 准备返回响应..."
echo ""

echo "4. 如果仍有错误"
echo "   查看后端控制台的具体错误信息"
echo "   ❌ 添加患者时出错: [具体错误]"
echo "   ❌ 完整错误信息: [堆栈信息]"
echo ""

echo "=== 已修复的问题 ==="
echo "✅ 增强了serialize_doc函数，可以处理所有类型"
echo "✅ 添加了详细的调试日志"
echo "✅ 增加了错误处理和类型检查"
echo "✅ 确保所有不可序列化的对象转换为字符串"
echo ""

echo "修复详情："
echo "- 增强serialize_doc函数，增加了更全面的类型处理"
echo "- 添加json.dumps测试来确保对象可序列化"
echo "- 在add_personnel函数中添加了详细的调试信息"
echo "- 增加了异常捕获和traceback输出" 