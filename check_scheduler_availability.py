#!/usr/bin/env python3
"""
检查调度器模块可用性脚本
"""
import os
import sys

def check_ai_call_path():
    """检查AICall工程目录"""
    ai_call_path = r'/home/<USER>/workspace/suifangmedcall'
    print(f"🔍 检查AICall工程目录: {ai_call_path}")
    
    if not os.path.exists(ai_call_path):
        print(f"❌ 目录不存在: {ai_call_path}")
        return False
    else:
        print(f"✅ 目录存在")
        
        # 检查关键文件
        scheduler_file = os.path.join(ai_call_path, 'scheduler_manager.py')
        if os.path.exists(scheduler_file):
            print(f"✅ 调度器管理器文件存在: {scheduler_file}")
        else:
            print(f"❌ 调度器管理器文件不存在: {scheduler_file}")
            return False
            
        # 列出目录内容
        try:
            files = os.listdir(ai_call_path)
            print(f"📁 目录内容: {files[:10]}...")  # 只显示前10个文件
        except Exception as e:
            print(f"❌ 无法读取目录内容: {e}")
            return False
            
        return True

def test_scheduler_import():
    """测试调度器模块导入"""
    print("\n🤖 测试调度器模块导入...")
    
    ai_call_path = r'/home/<USER>/workspace/suifangmedcall'
    if ai_call_path not in sys.path:
        sys.path.append(ai_call_path)
        print(f"✅ 已添加到Python路径")
    
    try:
        from scheduler_manager import (
            start_scheduler,
            stop_scheduler,
            restart_scheduler,
            get_scheduler_status,
            refresh_personnel,
            make_immediate_call
        )
        print("✅ 调度器模块导入成功")
        print("✅ 所有调度器API函数可用")
        
        # 测试获取状态函数
        try:
            status = get_scheduler_status()
            print(f"📊 调度器状态: {status}")
        except Exception as e:
            print(f"⚠️  获取状态时出错: {e}")
            
        return True
    except ImportError as e:
        print(f"❌ 调度器模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_api_endpoint():
    """测试API端点是否可用"""
    print("\n🌐 测试API端点...")
    
    try:
        import requests
        
        # 测试健康检查API
        url = "http://114.132.154.140:5000/api/scheduler/health"
        print(f"📡 测试URL: {url}")
        
        response = requests.get(url, timeout=10)
        print(f"📊 状态码: {response.status_code}")
        print(f"📊 响应头: {response.headers.get('content-type')}")
        print(f"📊 响应内容: {response.text[:200]}...")
        
        if response.status_code == 200:
            print("✅ API端点可用")
            return True
        else:
            print("❌ API端点返回错误状态")
            return False
            
    except Exception as e:
        print(f"❌ API请求失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 调度器可用性检查工具")
    print("=" * 50)
    
    # 1. 检查目录和文件
    path_ok = check_ai_call_path()
    
    # 2. 测试模块导入
    import_ok = test_scheduler_import()
    
    # 3. 测试API端点
    api_ok = test_api_endpoint()
    
    print("\n" + "=" * 50)
    print("📋 检查结果总结:")
    print(f"  📁 AICall目录: {'✅ 正常' if path_ok else '❌ 异常'}")
    print(f"  🐍 模块导入: {'✅ 正常' if import_ok else '❌ 异常'}")
    print(f"  🌐 API端点: {'✅ 正常' if api_ok else '❌ 异常'}")
    
    if path_ok and import_ok and api_ok:
        print("\n🎉 所有检查都通过！调度器功能应该可以正常使用")
    else:
        print("\n⚠️  存在问题，需要修复后才能正常使用调度器功能")
        
        if not path_ok:
            print("   - 请检查AICall工程目录是否正确")
        if not import_ok:
            print("   - 请检查scheduler_manager.py文件是否存在")
        if not api_ok:
            print("   - 请检查后端服务是否正常运行")

if __name__ == "__main__":
    main() 