{"root": ["./src/App.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/AddPatientModal.tsx", "./src/components/EditPatientModal.tsx", "./src/components/PatientDetailModal.tsx", "./src/components/SchedulerControl.tsx", "./src/components/Sidebar.tsx", "./src/components/StatCard.tsx", "./src/components/StatusBadge.tsx", "./src/components/SystemStatusIndicator.tsx", "./src/components/UserManagement.tsx", "./src/components/ui/alert.tsx", "./src/components/ui/avatar.tsx", "./src/components/ui/badge.tsx", "./src/components/ui/button.tsx", "./src/components/ui/card.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/dropdown-menu.tsx", "./src/components/ui/form.tsx", "./src/components/ui/input.tsx", "./src/components/ui/label.tsx", "./src/components/ui/progress.tsx", "./src/components/ui/scroll-area.tsx", "./src/components/ui/select.tsx", "./src/components/ui/slider.tsx", "./src/components/ui/switch.tsx", "./src/components/ui/table.tsx", "./src/components/ui/tabs.tsx", "./src/components/ui/toast.tsx", "./src/components/ui/toaster.tsx", "./src/contexts/AuthContext.tsx", "./src/hooks/use-toast.ts", "./src/hooks/useSchedulerStatus.ts", "./src/lib/utils.ts", "./src/pages/DashboardPage.tsx", "./src/pages/LoginPage.tsx", "./src/pages/PatientsPage.tsx", "./src/pages/ProgressPage.tsx", "./src/pages/RecordingsPage.tsx", "./src/pages/SettingsPage.tsx", "./src/pages/TrainingDataPage.tsx", "./src/services/api.ts", "./src/services/schedulerService.ts", "./src/types/index.ts"], "errors": true, "version": "5.6.2"}