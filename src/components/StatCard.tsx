import React from 'react';
import { ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/24/outline';

interface StatCardProps {
  title: string;
  value: number | string;
  trend?: string;
  trendValue: string;
  isUp: boolean;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, trend, trendValue, isUp }) => {
  return (
    <div className="bg-white rounded-lg p-5 shadow-sm">
      <h3 className="text-gray-500 text-sm mb-4">{title}</h3>
      <div className="text-3xl font-bold text-slate-800 mb-3">{value}</div>
      <div className={`flex items-center text-sm ${isUp ? 'text-green-500' : 'text-red-500'}`}>
        {isUp ? <ArrowUpIcon className="w-4 h-4 mr-1" /> : <ArrowDownIcon className="w-4 h-4 mr-1" />}
        <span>{trendValue}</span>
      </div>
    </div>
  );
};

export default StatCard; 