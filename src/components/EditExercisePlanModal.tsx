import React, { useState, useEffect } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { exercisePlanAPI } from '../services/api';
import { ExercisePlan } from '../types';

interface EditExercisePlanModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  plan: ExercisePlan | null;
}

const EditExercisePlanModal: React.FC<EditExercisePlanModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  plan
}) => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    plan_name: '',
    description: '',
    status: 'active' as 'active' | 'completed' | 'paused' | 'cancelled',
    total_weeks: 4,
    initial_daily_count: 200,
    weekly_increment: 100,
    max_rest_days_per_week: 2,
    start_date: new Date().toISOString().split('T')[0]
  });

  // 当计划数据变化时，更新表单数据
  useEffect(() => {
    if (plan && isOpen) {
      setFormData({
        plan_name: plan.plan_name,
        description: plan.description || '',
        status: plan.status,
        total_weeks: plan.total_weeks,
        initial_daily_count: plan.initial_daily_count,
        weekly_increment: plan.weekly_increment,
        max_rest_days_per_week: plan.max_rest_days_per_week,
        start_date: plan.start_date
      });
    }
  }, [plan, isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!plan) return;
    
    if (!formData.plan_name.trim()) {
      alert('请输入计划名称');
      return;
    }

    try {
      setLoading(true);
      
      // 只发送可以修改的字段
      const updateData = {
        plan_name: formData.plan_name,
        description: formData.description,
        status: formData.status
      };
      
      await exercisePlanAPI.update(plan._id, updateData);
      onSuccess();
      onClose();
    } catch (error: any) {
      console.error('更新锻炼计划失败:', error);
      alert(error.message || '更新失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name.includes('count') || name.includes('weeks') || name.includes('increment') || name.includes('days')
        ? parseInt(value) || 0
        : value
    }));
  };

  const calculateEndDate = (startDate: string, totalWeeks: number) => {
    const start = new Date(startDate);
    const end = new Date(start);
    end.setDate(start.getDate() + totalWeeks * 7);
    return end.toISOString().split('T')[0];
  };

  if (!isOpen || !plan) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-3xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">编辑锻炼计划模板</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 基本信息 */}
          <div className="bg-gray-50 p-4 rounded-md">
            <h3 className="font-medium text-gray-900 mb-3">基本信息</h3>
            <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
              <div>
                <span className="font-medium">计划编号：</span>{plan.plan_id}
              </div>
              <div>
                <span className="font-medium">创建时间：</span>{plan.created_at.split('T')[0]}
              </div>
              <div>
                <span className="font-medium">创建者：</span>{plan.doctor_name || '未知'}
              </div>
              <div>
                <span className="font-medium">最后更新：</span>{plan.updated_at.split('T')[0]}
              </div>
            </div>
          </div>

          {/* 可编辑字段 */}
          <div className="grid grid-cols-1 gap-6">
            {/* 计划名称 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                计划模板名称 *
              </label>
              <input
                type="text"
                name="plan_name"
                value={formData.plan_name}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* 计划描述 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                模板描述
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* 状态 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                模板状态 *
              </label>
              <select
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="active">进行中</option>
                <option value="completed">已完成</option>
                <option value="paused">暂停</option>
                <option value="cancelled">已取消</option>
              </select>
            </div>
          </div>

          {/* 锻炼参数（只读显示） */}
          <div className="bg-gray-50 p-4 rounded-md">
            <h3 className="font-medium text-gray-900 mb-3">锻炼参数（只读）</h3>
            <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
              <div>
                <span className="font-medium">总锻炼周数：</span>{plan.total_weeks} 周
              </div>
              <div>
                <span className="font-medium">初始每日次数：</span>{plan.initial_daily_count} 次
              </div>
              <div>
                <span className="font-medium">每周递增次数：</span>{plan.weekly_increment} 次
              </div>
              <div>
                <span className="font-medium">每周最多休息：</span>{plan.max_rest_days_per_week} 天
              </div>
              <div>
                <span className="font-medium">开始日期：</span>{plan.start_date}
              </div>
              <div>
                <span className="font-medium">结束日期：</span>{plan.end_date}
              </div>
            </div>
          </div>

          {/* 计划预览 */}
          <div className="bg-blue-50 p-4 rounded-md">
            <h4 className="font-medium text-gray-900 mb-2">锻炼强度预览</h4>
            <div className="text-sm text-gray-600 space-y-1">
              <p>• 第1周：每天 {plan.initial_daily_count} 次</p>
              <p>• 第2周：每天 {plan.initial_daily_count + plan.weekly_increment} 次</p>
              <p>• 第3周：每天 {plan.initial_daily_count + plan.weekly_increment * 2} 次</p>
              {plan.total_weeks > 3 && (
                <p>• 第{plan.total_weeks}周：每天 {plan.initial_daily_count + plan.weekly_increment * (plan.total_weeks - 1)} 次</p>
              )}
              <p>• 每周最多可休息 {plan.max_rest_days_per_week} 天</p>
            </div>
          </div>

          {/* 执行统计（如果有数据） */}
          {plan.total_actual_count > 0 && (
            <div className="bg-green-50 p-4 rounded-md">
              <h4 className="font-medium text-gray-900 mb-2">执行统计</h4>
              <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                <div>
                  <span className="font-medium">总目标次数：</span>{plan.total_target_count} 次
                </div>
                <div>
                  <span className="font-medium">总完成次数：</span>{plan.total_actual_count} 次
                </div>
                <div>
                  <span className="font-medium">完成率：</span>{(plan.overall_completion_rate * 100).toFixed(1)}%
                </div>
                <div>
                  <span className="font-medium">连续锻炼：</span>{plan.consecutive_days} 天
                </div>
              </div>
            </div>
          )}

          {/* 按钮 */}
          <div className="flex justify-end space-x-4 pt-4 border-t">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
            >
              {loading ? '保存中...' : '保存修改'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditExercisePlanModal;
