import React from 'react';
import { Activity, AlertTriangle } from 'lucide-react';
import { useSchedulerStatus } from '../hooks/useSchedulerStatus';

const SystemStatusIndicator: React.FC = () => {
  const { status, error, loading, isRunning } = useSchedulerStatus({
    autoRefresh: true,
    refreshInterval: 30000 // 30秒刷新一次
  });

  if (error) {
    return (
      <div className="px-4 py-3 border-t border-white/10">
        <div className="flex items-center space-x-2 text-yellow-400">
          <AlertTriangle className="h-4 w-4" />
          <div className="flex-1 min-w-0">
            <p className="text-xs font-medium">AI机器人</p>
            <p className="text-xs text-yellow-300 truncate">连接异常</p>
          </div>
        </div>
      </div>
    );
  }

  if (loading || !status) {
    return (
      <div className="px-4 py-3 border-t border-white/10">
        <div className="flex items-center space-x-2 text-gray-400">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"></div>
          <div className="flex-1 min-w-0">
            <p className="text-xs font-medium">AI机器人</p>
            <p className="text-xs text-gray-300">检查中...</p>
          </div>
        </div>
      </div>
    );
  }

  const statusColor = isRunning ? 'text-green-400' : 'text-red-400';
  const statusText = isRunning ? '运行中' : '已停止';

  return (
    <div className="px-4 py-3 border-t border-white/10">
      <div className={`flex items-center space-x-2 ${statusColor}`}>
        <Activity className="h-4 w-4" />
        <div className="flex-1 min-w-0">
          <p className="text-xs font-medium">AI机器人</p>
          <p className="text-xs truncate">{statusText}</p>
        </div>
        {isRunning && (
          <div className="flex-shrink-0">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SystemStatusIndicator; 