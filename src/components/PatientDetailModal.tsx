import React, { useState, useEffect } from 'react';
import { 
  XMarkIcon, 
  PhoneIcon, 
  PlayIcon, 
  ChevronLeftIcon, 
  ChevronRightIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';
import { personnelAPI, progressAPI } from '../services/api';
import StatusBadge from './StatusBadge';
import { Patient, ProgressData, CallRecord } from '../types';

interface PatientDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  patient: Patient | null;
}

const PatientDetailModal: React.FC<PatientDetailModalProps> = ({ isOpen, onClose, patient }) => {
  const [progress, setProgress] = useState<ProgressData>({
    currentDay: 0,
    totalDays: 30,
    compliance: 0,
    todayTraining: 0,
    avgTraining: 0
  });
  const [patientRecords, setPatientRecords] = useState<CallRecord[]>([]);
  const [allRecords, setAllRecords] = useState<CallRecord[]>([]);
  const [expandedDialogs, setExpandedDialogs] = useState<Record<string, boolean>>({});
  
  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [pageSize] = useState(5); // 模态框中每页显示5条记录

  useEffect(() => {
    if (patient) {
      // 获取患者详细信息
      const fetchPatientDetails = async () => {
        try {
          const data = await personnelAPI.getDetail(patient._id);
          const records = data.call_records || [];
          
          // 按记录日期降序排列，最新的记录在前面
          const sortedRecords = records.sort((a: CallRecord, b: CallRecord) => {
            const dateA = new Date(a['记录日期'] + ' ' + (a['通话时间'] || '00:00'));
            const dateB = new Date(b['记录日期'] + ' ' + (b['通话时间'] || '00:00'));
            return dateB.getTime() - dateA.getTime();
          });
          
          setAllRecords(sortedRecords);
          setTotalRecords(sortedRecords.length);
          setTotalPages(Math.ceil(sortedRecords.length / pageSize));
          
          // 计算电话拨打天数和有训练天数
          const callDates = new Set<string>();
          const trainingDates = new Set<string>();
          
          records.forEach((record: CallRecord) => {
            if (record['记录日期']) {
              callDates.add(record['记录日期']);
              
              // 如果有训练次数且大于0，则算作有训练的一天
              const trainingCount = parseInt(record['训练次数'] || '0') || 0;
              if (trainingCount > 0) {
                trainingDates.add(record['记录日期']);
              }
            }
          });
          
          const totalCallDays = callDates.size;
          const totalTrainingDays = trainingDates.size;
          const newCompliance = totalCallDays > 0 ? Math.round((totalTrainingDays / totalCallDays) * 100) : 0;
          
          // 获取进度数据
          const progressData = await progressAPI.get();
          const originalProgress = progressData.progress[patient._id] || {};
          
          setProgress({
            ...originalProgress,
            currentDay: originalProgress.currentDay || 0,
            totalDays: originalProgress.totalDays || 30,
            compliance: newCompliance,
            todayTraining: originalProgress.todayTraining || 0,
            avgTraining: originalProgress.avgTraining || 0,
            totalCallDays: totalCallDays,
            totalTrainingDays: totalTrainingDays
          });
        } catch (error) {
          console.error('获取患者详情失败:', error);
        }
      };
      
      fetchPatientDetails();
    }
  }, [patient, pageSize]);

  // 计算当前页的记录
  useEffect(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    setPatientRecords(allRecords.slice(startIndex, endIndex));
  }, [allRecords, currentPage, pageSize]);

  // 分页组件
  const PaginationComponent = () => {
    const pageNumbers = [];
    const maxVisiblePages = 3; // 模态框中显示更少的页码
    
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }

    return (
      <div className="flex items-center justify-between px-4 py-3 border-t border-gray-200">
        <div className="flex items-center text-sm text-gray-500">
          <span>
            显示 {((currentPage - 1) * pageSize) + 1} 到 {Math.min(currentPage * pageSize, totalRecords)} 条，
            共 {totalRecords} 条记录
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className={`px-3 py-1 rounded text-sm ${
              currentPage === 1 
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            上一页
          </button>
          
          {startPage > 1 && (
            <>
              <button
                onClick={() => setCurrentPage(1)}
                className="px-3 py-1 rounded text-sm bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                1
              </button>
              {startPage > 2 && <span className="text-gray-500">...</span>}
            </>
          )}
          
          {pageNumbers.map(pageNum => (
            <button
              key={pageNum}
              onClick={() => setCurrentPage(pageNum)}
              className={`px-3 py-1 rounded text-sm ${
                currentPage === pageNum
                  ? 'bg-blue-500 text-white'
                  : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
              }`}
            >
              {pageNum}
            </button>
          ))}
          
          {endPage < totalPages && (
            <>
              {endPage < totalPages - 1 && <span className="text-gray-500">...</span>}
              <button
                onClick={() => setCurrentPage(totalPages)}
                className="px-3 py-1 rounded text-sm bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                {totalPages}
              </button>
            </>
          )}
          
          <button
            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className={`px-3 py-1 rounded text-sm ${
              currentPage === totalPages 
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            下一页
          </button>
        </div>
      </div>
    );
  };

  if (!isOpen || !patient) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-11/12 max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <div className="p-5 border-b flex justify-between items-center bg-white">
          <h2 className="text-2xl font-semibold">患者详情 - {patient.name}</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        <div className="flex-1 overflow-y-auto">
          <div className="p-5">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-5 mb-8">
              <div>
                <label className="text-sm text-gray-500 block mb-1">患者ID</label>
                <p className="font-medium">{patient._id.slice(-6).toUpperCase()}</p>
              </div>
              <div>
                <label className="text-sm text-gray-500 block mb-1">姓名</label>
                <p className="font-medium">{patient.name}</p>
              </div>
              <div>
                <label className="text-sm text-gray-500 block mb-1">性别</label>
                <p className="font-medium">{patient.gender}</p>
              </div>
              <div>
                <label className="text-sm text-gray-500 block mb-1">年龄</label>
                <p className="font-medium">{patient.age}岁</p>
              </div>
              <div>
                <label className="text-sm text-gray-500 block mb-1">手机号码</label>
                <p className="font-medium">{patient.phone}</p>
              </div>
              <div>
                <label className="text-sm text-gray-500 block mb-1">入组日期</label>
                <p className="font-medium">{patient.enrollment_date}</p>
              </div>
              <div>
                <label className="text-sm text-gray-500 block mb-1">当前天数</label>
                <p className="font-medium">{progress.currentDay || 0}/{progress.totalDays || 30}</p>
              </div>
              <div>
                <label className="text-sm text-gray-500 block mb-1">电话拨打天数</label>
                <p className="font-medium">{progress.totalCallDays || 0}天</p>
              </div>
              <div>
                <label className="text-sm text-gray-500 block mb-1">有训练天数</label>
                <p className="font-medium">{progress.totalTrainingDays || 0}天</p>
              </div>
              <div>
                <label className="text-sm text-gray-500 block mb-1">训练依从性</label>
                <p className="font-medium">
                  {progress.compliance || 0}%
                  <span className="text-xs text-gray-400 ml-1">
                    ({progress.totalTrainingDays || 0}/{progress.totalCallDays || 0})
                  </span>
                </p>
              </div>
              <div>
                <label className="text-sm text-gray-500 block mb-1">昨日训练次数</label>
                <p className="font-medium">{progress.todayTraining || 0}次</p>
              </div>
              <div>
                <label className="text-sm text-gray-500 block mb-1">平均训练次数</label>
                <p className="font-medium">{Math.round(progress.avgTraining || 0)}次</p>
              </div>
            </div>

            {progress.records && (
              <div className="mb-8">
                <h3 className="text-lg font-semibold mb-4">摆腿数据趋势</h3>
                <div className="bg-gray-50 p-4 rounded">
                  <div className="grid grid-cols-7 gap-2">
                    {progress.records.map((record, index) => (
                      <div key={index} className="text-center">
                        <div className="text-xs text-gray-500 mb-1">{record.date.slice(5)}</div>
                        <div className={`h-20 flex items-end justify-center ${record.completed ? 'bg-blue-100' : 'bg-gray-100'} rounded`}>
                          <div 
                            className={`w-full ${record.completed ? 'bg-blue-500' : 'bg-gray-300'} rounded-t`}
                            style={{ height: `${(record.training / 40) * 100}%` }}
                          ></div>
                        </div>
                        <div className="text-xs mt-1">{record.training}次</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">通话记录</h3>
              {totalRecords > 0 && (
                <span className="text-sm text-gray-500">
                  共 {totalRecords} 条记录
                </span>
              )}
            </div>
            
            <div className="space-y-6">
              {(() => {
                // 按日期分组当前页的通话记录
                const recordsByDate: Record<string, CallRecord[]> = {};
                patientRecords.forEach(record => {
                  const date = record['记录日期'] || '';
                  if (!recordsByDate[date]) {
                    recordsByDate[date] = [];
                  }
                  recordsByDate[date].push(record);
                });

                // 按日期倒序排列
                const sortedDates = Object.keys(recordsByDate).sort((a, b) => b.localeCompare(a));

                return sortedDates.map((date) => (
                  <div key={date} className="border rounded-lg overflow-hidden">
                    {/* 日期标题栏 */}
                    <div className="bg-gradient-to-r from-blue-50 to-blue-100 px-4 py-3 border-b">
                      <div className="flex justify-between items-center">
                        <h4 className="font-semibold text-blue-800">{date}</h4>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-blue-600">
                            {recordsByDate[date].length} 次通话
                          </span>
                          {recordsByDate[date].some(r => r['训练完成情况'] === '完成') && (
                            <span className="px-2 py-1 bg-green-100 text-green-600 rounded text-xs">
                              已完成训练
                            </span>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* 该日期下的所有通话记录 */}
                    <div className="divide-y divide-gray-200">
                      {recordsByDate[date]
                        .sort((a, b) => (b['通话时间'] || '').localeCompare(a['通话时间'] || ''))
                        .map((record, index) => {
                          // 计算全局通话次数序号：当前记录在所有记录中的位置
                          const globalIndex = allRecords.findIndex(r => r._id === record._id);
                          const callNumber = globalIndex + 1;
                          
                          return (
                        <div key={record._id} className="p-4 bg-white hover:bg-gray-50 transition-colors">
                          {/* 通话时间和状态 */}
                          <div className="flex justify-between items-start mb-3">
                            <div className="flex items-center gap-3">
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                <span className="font-medium text-gray-900">
                                  第{callNumber}次通话
                                </span>
                              </div>
                              <span className="text-sm text-gray-500">
                                {record['通话时间']}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              {/* 拨号状态标签 - 始终显示 */}
                              <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                                拨号状态: {record['拨号状态'] || record['训练完成情况'] || '未知'}
                              </span>
                              {record['训练完成情况'] && (
                                <StatusBadge 
                                  status={record['训练完成情况'] === '完成' ? '通话成功' : 
                                          record['训练完成情况'] === '未接通' ? '未接通' : 
                                          record['训练完成情况']} 
                                  variant={record['训练完成情况'] === '完成' ? 'completed' : 
                                          record['训练完成情况'] === '未接通' ? 'pending' : 'inactive'} 
                                />
                              )}
                            </div>
                          </div>

                          {/* 训练数据标签 - 统一放在左侧 */}
                          <div className="flex flex-wrap gap-2 mb-3">
                            {/* 训练相关信息 */}
                            {record['训练次数'] && (
                              <span className="px-2 py-1 bg-blue-100 text-blue-600 rounded text-sm">
                                <strong>训练:</strong> {record['训练次数']}
                              </span>
                            )}
                            {record['训练时长'] && (
                              <span className="px-2 py-1 bg-purple-100 text-purple-600 rounded text-sm">
                                <strong>时长:</strong> {record['训练时长']}
                              </span>
                            )}
                            {record['是否有不适感'] === '否' && (
                              <span className="px-2 py-1 bg-green-100 text-green-600 rounded text-sm">
                                无不适感
                              </span>
                            )}
                            {record['是否有不适感'] === '是' && (
                              <span className="px-2 py-1 bg-red-100 text-red-600 rounded text-sm">
                                <strong>不适:</strong> {record['不适感内容']}
                              </span>
                            )}
                            {record['锻炼辅助仪器是否有问题'] === '是' && (
                              <span className="px-2 py-1 bg-yellow-100 text-yellow-600 rounded text-sm">
                                <strong>设备问题:</strong> {record['锻炼辅助仪器问题内容']}
                              </span>
                            )}
                            {record['是否需要医生人工和患者联系'] === '是' && (
                              <span className="px-2 py-1 bg-orange-100 text-orange-600 rounded text-sm">
                                需要医生联系
                              </span>
                            )}
                            {/* 如果没有任何训练信息，显示训练状态 */}
                            {record['训练完成情况'] === '未接通' && (
                              <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-sm">
                                训练未完成
                              </span>
                            )}
                            {record['训练完成情况'] && record['训练完成情况'] !== '完成' && record['训练完成情况'] !== '未接通' && (
                              <span className="px-2 py-1 bg-yellow-100 text-yellow-600 rounded text-sm">
                                训练未完成
                              </span>
                            )}
                          </div>

                          {/* 通话内容 */}
                          {record['对话历史记录'] && record['对话历史记录'].length > 0 && (
                            <div className="mt-3">
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-sm font-medium text-gray-700">通话录音与对话</span>
                                <button className="flex items-center gap-1 text-blue-500 hover:text-blue-600 text-sm">
                                  <PlayIcon className="w-4 h-4" />
                                  播放录音
                                </button>
                              </div>
                              <div className="bg-gray-50 border rounded-lg p-3">
                                <div className={`${expandedDialogs[record._id] ? 'max-h-96' : 'max-h-32'} overflow-y-auto space-y-2 text-sm transition-all duration-300`}>
                                  {(expandedDialogs[record._id] 
                                    ? record['对话历史记录'] 
                                    : record['对话历史记录'].slice(0, 4)
                                  ).map((chat, chatIndex) => (
                                    <div key={chatIndex} className={`flex ${chat.role === 'assistant' ? 'justify-start' : 'justify-end'}`}>
                                      <div className={`max-w-[80%] p-2 rounded-lg ${
                                        chat.role === 'assistant' 
                                          ? 'bg-blue-100 text-blue-900' 
                                          : 'bg-green-100 text-green-900'
                                      }`}>
                                        <div className="text-xs font-medium mb-1">
                                          {chat.role === 'assistant' ? '智能助手' : patient.name}
                                        </div>
                                        <div>{chat.content}</div>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                                {record['对话历史记录'].length > 4 && (
                                  <div className="text-center mt-2 pt-2 border-t border-gray-200">
                                    <button 
                                      onClick={() => {
                                        setExpandedDialogs(prev => ({
                                          ...prev,
                                          [record._id]: !prev[record._id]
                                        }));
                                      }}
                                      className="text-blue-500 text-xs hover:text-blue-600 flex items-center gap-1 mx-auto"
                                    >
                                      {expandedDialogs[record._id] ? (
                                        <>
                                          <ChevronDownIcon className="w-3 h-3" />
                                          收起对话
                                        </>
                                      ) : (
                                        <>
                                          <ChevronRightIcon className="w-3 h-3 transform rotate-90" />
                                          查看完整对话 ({record['对话历史记录'].length} 条消息)
                                        </>
                                      )}
                                    </button>
                                  </div>
                                )}
                              </div>
                            </div>
                          )}

                          {/* 未接通的情况 */}
                          {record['训练完成情况'] === '未接通' && (
                            <div className="mt-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
                              <div className="flex items-center gap-2 text-gray-600">
                                <PhoneIcon className="w-4 h-4" />
                                <span className="text-sm">此次通话未接通，系统将自动安排重试</span>
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                    </div>
                  </div>
                ));
              })()}
              
              {patientRecords.length === 0 && allRecords.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <PhoneIcon className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                  <p>暂无通话记录</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 分页组件 */}
        {totalRecords > pageSize && <PaginationComponent />}

        {/* <div className="p-5 border-t flex justify-end gap-3 bg-white">
          <button className="px-4 py-2 bg-gray-100 text-slate-700 rounded hover:bg-gray-200">导出记录</button>
          <button className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">安排随访</button>
        </div> */}
      </div>
    </div>
  );
};

export default PatientDetailModal; 