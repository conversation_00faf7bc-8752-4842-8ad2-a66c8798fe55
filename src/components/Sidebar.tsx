import React from 'react';
import {
  HomeIcon,
  UserGroupIcon,
  ChartPieIcon,
  MicrophoneIcon,
  LightBulbIcon,
  CogIcon,
  ClipboardDocumentListIcon
} from '@heroicons/react/24/outline';
import { LogOut, User } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import SystemStatusIndicator from './SystemStatusIndicator';

interface SidebarProps {
  activeMenu: string;
  setActiveMenu: (menu: string) => void;
  onMenuChange?: (menuId: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ activeMenu, setActiveMenu, onMenuChange }) => {
  const { user, logout, hasPermission } = useAuth();

  const menuItems = [
    { id: 'dashboard', icon: HomeIcon, label: '控制面板', permission: null },
    { id: 'patients', icon: UserGroupIcon, label: '患者管理', permission: null },
    { id: 'exercise-plans', icon: ClipboardDocumentListIcon, label: '锻炼计划', permission: null },
    { id: 'progress', icon: ChartPieIcon, label: '康复进度', permission: null },
    { id: 'recordings', icon: MicrophoneIcon, label: '通话记录', permission: null },
    { id: 'training-data', icon: LightBulbIcon, label: '训练数据', permission: null },
    { id: 'settings', icon: CogIcon, label: '系统设置', permission: 'settings' },
  ];

  // 根据权限过滤菜单项
  const filteredMenuItems = menuItems.filter(item => 
    !item.permission || hasPermission(item.permission)
  );

  const handleMenuClick = (menuId: string) => {
    setActiveMenu(menuId);
    // 当切换菜单时，通知父组件清除患者历史查看状态
    if (onMenuChange) {
      onMenuChange(menuId);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('登出失败:', error);
    }
  };

  return (
    <div className="w-64 bg-slate-800 text-white flex-shrink-0 flex flex-col">
      <div className="p-5 border-b border-white/10 text-center">
        <h2 className="text-2xl font-bold mb-1">膝盖康复助手</h2>
        <p className="text-sm opacity-70">智能随访管理系统</p>
      </div>
      
      {/* 用户信息 */}
      <div className="p-4 border-b border-white/10">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
            <User className="w-5 h-5" />
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate">{user?.full_name}</p>
            <p className="text-xs text-gray-300 truncate">{user?.role}</p>
          </div>
        </div>
      </div>

      {/* 菜单项 */}
      <div className="py-5 flex-1">
        {filteredMenuItems.map(item => {
          const Icon = item.icon;
          return (
            <div
              key={item.id}
              onClick={() => handleMenuClick(item.id)}
              className={`px-5 py-3 flex items-center cursor-pointer transition-colors ${
                activeMenu === item.id 
                  ? 'bg-blue-500' 
                  : 'hover:bg-white/10'
              }`}
            >
              <Icon className="w-6 h-6 mr-3" />
              <span>{item.label}</span>
            </div>
          );
        })}
      </div>

      {/* AI机器人状态指示器 */}
      <SystemStatusIndicator />

      {/* 登出按钮 */}
      <div className="p-4 border-t border-white/10">
        <button
          onClick={handleLogout}
          className="w-full flex items-center px-4 py-2 text-sm text-gray-300 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
        >
          <LogOut className="w-4 h-4 mr-3" />
          退出登录
        </button>
      </div>
    </div>
  );
};

export default Sidebar; 