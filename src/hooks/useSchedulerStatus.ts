import { useState, useEffect, useCallback, useRef } from 'react';
import { schedulerService, SchedulerStatus, handleSchedulerError } from '../services/schedulerService';

interface UseSchedulerStatusOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  onStatusChange?: (status: SchedulerStatus | null) => void;
  onError?: (error: string) => void;
}

interface UseSchedulerStatusReturn {
  status: SchedulerStatus | null;
  loading: boolean;
  error: string | null;
  lastUpdateTime: Date | null;
  initialCheckComplete: boolean;
  fetchStatus: () => Promise<void>;
  isRunning: boolean;
  isStopped: boolean;
}

// 全局状态管理
let globalStatus: SchedulerStatus | null = null;
let globalError: string | null = null;
let globalLastUpdateTime: Date | null = null;
let globalInitialCheckComplete = false;
const subscribers = new Set<() => void>();

// 通知所有订阅者状态变化
const notifySubscribers = () => {
  subscribers.forEach(callback => callback());
};

// 更新全局状态
const updateGlobalStatus = (
  status: SchedulerStatus | null, 
  error: string | null, 
  lastUpdateTime: Date | null
) => {
  globalStatus = status;
  globalError = error;
  globalLastUpdateTime = lastUpdateTime;
  if (!globalInitialCheckComplete) {
    globalInitialCheckComplete = true;
  }
  notifySubscribers();
};

// 全局获取状态的函数
const fetchGlobalStatus = async (): Promise<void> => {
  try {
    const data = await schedulerService.getStatus();
    updateGlobalStatus(data, null, new Date());
  } catch (err) {
    const errorMessage = handleSchedulerError(err);
    updateGlobalStatus(null, errorMessage, null);
  }
};

// 全局刷新定时器
let globalRefreshTimer: NodeJS.Timeout | null = null;

// 启动全局自动刷新
const startGlobalAutoRefresh = (interval: number = 30000) => {
  if (globalRefreshTimer) {
    clearInterval(globalRefreshTimer);
  }
  
  globalRefreshTimer = setInterval(() => {
    if (globalInitialCheckComplete) {
      fetchGlobalStatus();
    }
  }, interval);
};

// 停止全局自动刷新
const stopGlobalAutoRefresh = () => {
  if (globalRefreshTimer) {
    clearInterval(globalRefreshTimer);
    globalRefreshTimer = null;
  }
};

export const useSchedulerStatus = (options: UseSchedulerStatusOptions = {}): UseSchedulerStatusReturn => {
  const {
    autoRefresh = true,
    refreshInterval = 30000,
    onStatusChange,
    onError
  } = options;

  const [localStatus, setLocalStatus] = useState<SchedulerStatus | null>(globalStatus);
  const [localError, setLocalError] = useState<string | null>(globalError);
  const [localLastUpdateTime, setLocalLastUpdateTime] = useState<Date | null>(globalLastUpdateTime);
  const [localInitialCheckComplete, setLocalInitialCheckComplete] = useState(globalInitialCheckComplete);
  const [loading, setLoading] = useState(!globalInitialCheckComplete);

  const onStatusChangeRef = useRef(onStatusChange);
  const onErrorRef = useRef(onError);

  // 更新回调引用
  useEffect(() => {
    onStatusChangeRef.current = onStatusChange;
    onErrorRef.current = onError;
  }, [onStatusChange, onError]);

  // 订阅全局状态变化
  useEffect(() => {
    const updateLocalState = () => {
      setLocalStatus(globalStatus);
      setLocalError(globalError);
      setLocalLastUpdateTime(globalLastUpdateTime);
      setLocalInitialCheckComplete(globalInitialCheckComplete);
      setLoading(!globalInitialCheckComplete);

      // 触发回调
      if (onStatusChangeRef.current) {
        onStatusChangeRef.current(globalStatus);
      }
      if (onErrorRef.current && globalError) {
        onErrorRef.current(globalError);
      }
    };

    subscribers.add(updateLocalState);

    return () => {
      subscribers.delete(updateLocalState);
    };
  }, []);

  // 手动获取状态
  const fetchStatus = useCallback(async () => {
    if (!globalInitialCheckComplete) {
      setLoading(true);
    }

    try {
      await fetchGlobalStatus();
    } finally {
      if (!globalInitialCheckComplete) {
        setLoading(false);
      }
    }
  }, []);

  // 初始化和自动刷新
  useEffect(() => {
    // 如果还没有进行过初始检查，立即检查
    if (!globalInitialCheckComplete) {
      fetchStatus();
    }

    // 设置自动刷新
    if (autoRefresh) {
      startGlobalAutoRefresh(refreshInterval);
    }

    return () => {
      // 如果没有其他订阅者，停止自动刷新
      if (subscribers.size <= 1) {
        stopGlobalAutoRefresh();
      }
    };
  }, [autoRefresh, refreshInterval, fetchStatus]);

  return {
    status: localStatus,
    loading,
    error: localError,
    lastUpdateTime: localLastUpdateTime,
    initialCheckComplete: localInitialCheckComplete,
    fetchStatus,
    isRunning: localStatus?.status === 'running',
    isStopped: localStatus?.status === 'stopped'
  };
};

// 导出一些工具函数
export const getGlobalSchedulerStatus = () => globalStatus;
export const isGlobalSchedulerRunning = () => globalStatus?.status === 'running';
export const forceRefreshGlobalStatus = () => fetchGlobalStatus(); 