import React, { useState, useEffect } from 'react';
import { 
  PlusIcon,
  EyeIcon,
  PhoneIcon,
  PencilIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  FunnelIcon,
  MagnifyingGlassCircleIcon,
  XMarkIcon,
  PlayIcon,
  StopIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  ClockIcon,
  ChartBarIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline';

import { personnelAPI, callRecordsAPI, followUpStatsAPI } from '../services/api';
import StatCard from '../components/StatCard';
import StatusBadge from '../components/StatusBadge';
import { Patient, StatsData, CallRecord, FollowUpStat } from '../types';
import { <PERSON><PERSON><PERSON>, Bar, LineChart, Line, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

interface DashboardPageProps {
  setShowPatientModal: (show: boolean) => void;
  setShowAddPatientModal: (show: boolean) => void;
  setSelectedPatient: (patient: Patient) => void;
  onEditPatient: (patient: Patient) => void;
  refreshKey?: number; // 用于触发数据刷新
}

// 通话状态枚举
enum CallStatus {
  PREPARING = 'preparing',
  DIALING = '拨号中',
  CONNECTED = '已接听',
  NOT_ANSWERED = '未接听',
  HUNG_UP = '直接挂断',
  UNREACHABLE = '无法接通',
  CALL_ENDED = '通话结束',
  ANALYSIS_COMPLETE = '通话结束-信息解析完成'
}

// 详情模态框组件
const DetailModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  data: any[];
  loading: boolean;
  timeRange: string;
}> = ({ isOpen, onClose, data, loading, timeRange }) => {
  // 筛选状态
  const [filters, setFilters] = useState({
    doctorIntervention: '', // 医生干预筛选
    discomfort: '', // 不适感筛选
    deviceStatus: '', // 设备状态筛选
    trainingCount: '', // 训练次数值
    trainingCountOperator: 'gte' // 训练次数比较运算符：gte(大于等于), lte(小于等于)
  });

  // 重置筛选条件
  const resetFilters = () => {
    setFilters({
      doctorIntervention: '',
      discomfort: '',
      deviceStatus: '',
      trainingCount: '',
      trainingCountOperator: 'gte'
    });
  };

  // 筛选数据
  const getFilteredData = () => {
    return data.filter(record => {
      // 医生干预筛选
      if (filters.doctorIntervention && record['是否需要医生人工和患者联系'] !== filters.doctorIntervention) {
        return false;
      }

      // 不适感筛选
      if (filters.discomfort && record['是否有不适感'] !== filters.discomfort) {
        return false;
      }

      // 设备状态筛选
      if (filters.deviceStatus && record['锻炼辅助仪器是否有问题'] !== filters.deviceStatus) {
        return false;
      }

      // 训练次数筛选
      if (filters.trainingCount) {
        const trainingCountStr = record['训练次数'] || '';
        const trainingNum = parseInt(trainingCountStr.replace(/[^\d]/g, '')) || 0;
        const filterNum = parseInt(filters.trainingCount) || 0;

        if (filters.trainingCountOperator === 'gte' && trainingNum < filterNum) {
          return false;
        }
        if (filters.trainingCountOperator === 'lte' && trainingNum > filterNum) {
          return false;
        }
      }

      return true;
    });
  };

  const filteredData = getFilteredData();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-[90vw] h-[80vh] flex flex-col">
        <div className="flex justify-between items-center p-6 border-b">
          <h3 className="text-xl font-semibold">随访详情 - {timeRange}</h3>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        <div className="flex-1 overflow-hidden p-6">
          {loading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-gray-500">加载中...</div>
            </div>
          ) : data.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-gray-500">该时间段内暂无数据</div>
            </div>
          ) : (
            <>
              {/* 筛选控件区域 */}
              <div className="mb-4 p-4 bg-gray-50 rounded-lg border">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-sm font-medium text-gray-700">数据筛选</h4>
                  <button
                    onClick={resetFilters}
                    className="text-sm text-blue-500 hover:text-blue-700"
                  >
                    重置筛选
                  </button>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {/* 医生干预筛选 */}
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">医生干预</label>
                    <select
                      value={filters.doctorIntervention}
                      onChange={(e) => setFilters({...filters, doctorIntervention: e.target.value})}
                      className="w-full px-2 py-1 text-sm border rounded focus:outline-none focus:border-blue-500"
                    >
                      <option value="">全部</option>
                      <option value="是">需要干预</option>
                      <option value="否">无需干预</option>
                    </select>
                  </div>

                  {/* 不适感筛选 */}
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">不适感</label>
                                         <select
                       value={filters.discomfort}
                       onChange={(e) => setFilters({...filters, discomfort: e.target.value})}
                       className="w-full px-2 py-1 text-sm border rounded focus:outline-none focus:border-blue-500"
                     >
                       <option value="">全部</option>
                       <option value="是">有不适感</option>
                       <option value="否">无不适感</option>
                     </select>
                  </div>

                  {/* 设备状态筛选 */}
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">设备状态</label>
                                         <select
                       value={filters.deviceStatus}
                       onChange={(e) => setFilters({...filters, deviceStatus: e.target.value})}
                       className="w-full px-2 py-1 text-sm border rounded focus:outline-none focus:border-blue-500"
                     >
                       <option value="">全部</option>
                       <option value="否">设备正常</option>
                       <option value="是">设备异常</option>
                     </select>
                  </div>

                  {/* 训练次数筛选 */}
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">训练次数</label>
                    <div className="flex gap-1">
                      <select
                        value={filters.trainingCountOperator}
                        onChange={(e) => setFilters({...filters, trainingCountOperator: e.target.value})}
                        className="w-16 px-1 py-1 text-xs border rounded focus:outline-none focus:border-blue-500"
                      >
                        <option value="gte">≥</option>
                        <option value="lte">≤</option>
                      </select>
                      <input
                        type="number"
                        placeholder="次数"
                        value={filters.trainingCount}
                        onChange={(e) => setFilters({...filters, trainingCount: e.target.value})}
                        className="flex-1 px-2 py-1 text-sm border rounded focus:outline-none focus:border-blue-500"
                      />
                    </div>
                  </div>
                </div>
                
                {/* 筛选结果统计 */}
                <div className="mt-3 text-xs text-gray-600">
                  筛选结果：{filteredData.length} / {data.length} 条记录
                </div>
              </div>
            </>
          )}

                    {/* 表格区域 */}
          {!loading && data.length > 0 && (
            <div className="h-full overflow-auto">
              {filteredData.length === 0 ? (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <div className="text-gray-500 mb-2">暂无符合筛选条件的数据</div>
                    <button
                      onClick={resetFilters}
                      className="text-sm text-blue-500 hover:text-blue-700"
                    >
                      清除筛选条件
                    </button>
                  </div>
                </div>
              ) : (
                <table className="w-full border-collapse">
                  <thead className="sticky top-0 bg-white border-b-2">
                    <tr>
                      <th className="text-left py-3 px-4 font-semibold text-gray-600 border-b">患者编号</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-600 border-b">患者姓名</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-600 border-b">联系方式</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-600 border-b">通话日期</th>
                      <th className="text-center py-3 px-4 font-semibold text-gray-600 border-b">训练次数</th>
                      <th className="text-center py-3 px-4 font-semibold text-gray-600 border-b">训练时长</th>
                      <th className="text-center py-3 px-4 font-semibold text-gray-600 border-b">不适感</th>
                      <th className="text-center py-3 px-4 font-semibold text-gray-600 border-b">设备状态</th>
                      <th className="text-center py-3 px-4 font-semibold text-gray-600 border-b">医生干预</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredData.map((record, index) => (
                                         <tr key={index} className="border-b hover:bg-gray-50">
                       <td className="py-3 px-4 font-mono text-sm font-medium">{record['患者编号'] || '--'}</td>
                       <td className="py-3 px-4">{record['患者名字'] || '--'}</td>
                       <td className="py-3 px-4">{record['手机号'] || '--'}</td>
                       <td className="py-3 px-4">{record['记录日期'] || '--'}</td>
                       <td className="py-3 px-4 text-center">
                         <div className="flex flex-col items-center gap-1">
                           <span className={`px-2 py-1 rounded text-sm ${
                             record['训练次数'] && record['训练次数'] !== '--' 
                               ? 'bg-green-100 text-green-600' 
                               : 'bg-gray-100 text-gray-500'
                           }`}>
                             {record['训练次数'] || '--'}
                           </span>
                           {(!record['训练次数'] || record['训练次数'] === '--') && record['训练未完成原因'] && (
                             <div 
                               className="text-xs text-gray-600 max-w-32 break-words text-center bg-yellow-50 px-2 py-1 rounded border border-yellow-200"
                               title={record['训练未完成原因']}
                             >
                               {record['训练未完成原因'].length > 20 
                                 ? `${record['训练未完成原因'].substring(0, 20)}...` 
                                 : record['训练未完成原因']
                               }
                             </div>
                           )}
                         </div>
                       </td>
                       <td className="py-3 px-4 text-center">
                         <span className={`px-2 py-1 rounded text-sm ${
                           record['训练时长'] && record['训练时长'] !== '--' 
                             ? 'bg-blue-100 text-blue-600' 
                             : 'bg-gray-100 text-gray-500'
                         }`}>
                           {record['训练时长'] || '--'}
                         </span>
                       </td>
                       <td className="py-3 px-4 text-center">
                         <div className="flex flex-col items-center gap-1">
                           <span className={`px-2 py-1 rounded text-sm ${
                             record['是否有不适感'] === '是'
                               ? 'bg-red-100 text-red-600' 
                               : record['是否有不适感'] === '否'
                               ? 'bg-green-100 text-green-600'
                               : 'bg-gray-100 text-gray-500'
                           }`}>
                             {record['是否有不适感'] || '--'}
                           </span>
                           {record['是否有不适感'] === '是' && record['不适感内容'] && (
                             <div 
                               className="text-xs text-gray-600 max-w-32 break-words text-center bg-red-50 px-2 py-1 rounded border border-red-200"
                               title={record['不适感内容']}
                             >
                               {record['不适感内容'].length > 20 
                                 ? `${record['不适感内容'].substring(0, 20)}...` 
                                 : record['不适感内容']
                               }
                             </div>
                           )}
                           {record['不适感是否恢复'] && (
                             <div 
                               className="text-xs text-gray-600 max-w-32 break-words text-center bg-blue-50 px-2 py-1 rounded border border-blue-200"
                               title={`恢复情况：${record['不适感是否恢复']}`}
                             >
                               恢复：{record['不适感是否恢复'].length > 15 
                                 ? `${record['不适感是否恢复'].substring(0, 15)}...` 
                                 : record['不适感是否恢复']
                               }
                             </div>
                           )}
                         </div>
                       </td>
                       <td className="py-3 px-4 text-center">
                         <div className="flex flex-col items-center gap-1">
                           <span className={`px-2 py-1 rounded text-sm ${
                             record['锻炼辅助仪器是否有问题'] === '否'
                               ? 'bg-green-100 text-green-600' 
                               : record['锻炼辅助仪器是否有问题'] === '是'
                               ? 'bg-red-100 text-red-600'
                               : 'bg-gray-100 text-gray-500'
                           }`}>
                             {record['锻炼辅助仪器是否有问题'] === '否' ? '正常' : 
                              record['锻炼辅助仪器是否有问题'] === '是' ? '异常' : '--'}
                           </span>
                           {record['锻炼辅助仪器是否有问题'] === '是' && record['锻炼辅助仪器问题内容'] && (
                             <div 
                               className="text-xs text-gray-600 max-w-32 break-words text-center bg-orange-50 px-2 py-1 rounded border border-orange-200"
                               title={record['锻炼辅助仪器问题内容']}
                             >
                               {record['锻炼辅助仪器问题内容'].length > 20 
                                 ? `${record['锻炼辅助仪器问题内容'].substring(0, 20)}...` 
                                 : record['锻炼辅助仪器问题内容']
                               }
                             </div>
                           )}
                         </div>
                       </td>
                       <td className="py-3 px-4 text-center">
                         <span className={`px-2 py-1 rounded text-sm ${
                           record['是否需要医生人工和患者联系'] === '是'
                             ? 'bg-orange-100 text-orange-600' 
                             : record['是否需要医生人工和患者联系'] === '否'
                             ? 'bg-gray-100 text-gray-500'
                             : 'bg-gray-100 text-gray-500'
                         }`}>
                           {record['是否需要医生人工和患者联系'] === '是' ? '需要干预' : 
                            record['是否需要医生人工和患者联系'] === '否' ? '无需干预' : '--'}
                         </span>
                       </td>
                     </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          )}
        </div>

        <div className="flex justify-between items-center p-6 border-t bg-gray-50">
          <div className="text-sm text-gray-600">
            {filteredData.length < data.length 
              ? `显示 ${filteredData.length} 条记录（共 ${data.length} 条）`
              : `共 ${data.length} 条记录`
            }
          </div>
          <button 
            onClick={onClose}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
};

// 拨打电话模态框组件
const CallModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  patient: Patient | null;
}> = ({ isOpen, onClose, patient }) => {
  const [callStatus, setCallStatus] = useState<string>(CallStatus.PREPARING);
  const [callObjId, setCallObjId] = useState<string>('');
  const [isStarting, setIsStarting] = useState(false);
  const [callRecord, setCallRecord] = useState<any>(null);
  const [statusPolling, setStatusPolling] = useState<NodeJS.Timeout | null>(null);

  // 重置状态
  const resetCallState = () => {
    setCallStatus(CallStatus.PREPARING);
    setCallObjId('');
    setIsStarting(false);
    setCallRecord(null);
    if (statusPolling) {
      clearInterval(statusPolling);
      setStatusPolling(null);
    }
  };

  // 关闭模态框
  const handleClose = () => {
    resetCallState();
    onClose();
  };

  // 开始拨打电话
  const startCall = async () => {
    if (!patient) return;

    try {
      setIsStarting(true);
      // 生成符合MongoDB ObjectId格式的24字符十六进制字符串
      const generateObjectId = () => {
        // MongoDB ObjectId格式：4字节时间戳 + 5字节随机 + 3字节计数器
        const timestamp = Math.floor(Date.now() / 1000).toString(16).padStart(8, '0');
        
        // 生成16个字符的随机十六进制字符串
        let random = '';
        for (let i = 0; i < 16; i++) {
          random += Math.floor(Math.random() * 16).toString(16);
        }
        
        return timestamp + random;
      };
      
      const callId = generateObjectId();
      setCallObjId(callId);
      
      // 调用后端API开始拨打电话
      await callRecordsAPI.makeImmediateCall(patient.phone, callId);
      
      setCallStatus(CallStatus.DIALING);
      
      // 开始轮询通话状态
      const polling = setInterval(async () => {
        try {
          const statusData = await callRecordsAPI.getCallStatus(callId);
          const currentStatus = statusData['拨号状态'];
          
          setCallStatus(currentStatus);
          
                     // 如果通话结束或者失败状态，停止轮询
           if ([
             CallStatus.NOT_ANSWERED,
             CallStatus.HUNG_UP,
             CallStatus.UNREACHABLE,
             CallStatus.ANALYSIS_COMPLETE
           ].includes(currentStatus as CallStatus)) {
            clearInterval(polling);
            setStatusPolling(null);
            
            // 如果是成功完成，获取详细记录
            if (currentStatus === CallStatus.ANALYSIS_COMPLETE) {
              const detailData = await callRecordsAPI.getCallDetail(callId);
              setCallRecord(detailData);
            }
          }
        } catch (error) {
          console.error('轮询通话状态失败:', error);
        }
      }, 1000); // 每秒轮询一次
      
      setStatusPolling(polling);
    } catch (error) {
      console.error('开始拨打电话失败:', error);
      alert('拨打电话失败，请重试');
    } finally {
      setIsStarting(false);
    }
  };

  // 清理轮询
  useEffect(() => {
    return () => {
      if (statusPolling) {
        clearInterval(statusPolling);
      }
    };
  }, [statusPolling]);

  if (!isOpen || !patient) return null;

  // 获取状态显示信息
  const getStatusDisplay = () => {
    switch (callStatus) {
      case CallStatus.PREPARING:
        return { icon: ClockIcon, text: '准备拨打', color: 'text-gray-500' };
      case CallStatus.DIALING:
        return { icon: PlayIcon, text: '正在拨号...', color: 'text-blue-500' };
      case CallStatus.CONNECTED:
        return { icon: PhoneIcon, text: '通话中...', color: 'text-green-500' };
      case CallStatus.CALL_ENDED:
        return { icon: ClockIcon, text: '通话结束，正在分析...', color: 'text-yellow-500' };
      case CallStatus.ANALYSIS_COMPLETE:
        return { icon: CheckCircleIcon, text: '通话完成', color: 'text-green-500' };
      case CallStatus.NOT_ANSWERED:
      case CallStatus.HUNG_UP:
      case CallStatus.UNREACHABLE:
        return { icon: ExclamationCircleIcon, text: '通话未接通', color: 'text-red-500' };
      default:
        return { icon: ClockIcon, text: callStatus, color: 'text-gray-500' };
    }
  };

  const statusDisplay = getStatusDisplay();
  const StatusIcon = statusDisplay.icon;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-[600px] max-h-[80vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-semibold">拨打电话</h3>
          <button 
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* 患者信息 */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <h4 className="font-medium mb-3">患者信息</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">姓名：</span>
              <span className="font-medium">{patient.name}</span>
            </div>
            <div>
              <span className="text-gray-600">年龄：</span>
              <span>{patient.age}岁</span>
            </div>
            <div>
              <span className="text-gray-600">性别：</span>
              <span>{patient.gender}</span>
            </div>
            <div>
              <span className="text-gray-600">联系方式：</span>
              <span className="font-medium">{patient.phone}</span>
            </div>
            <div>
              <span className="text-gray-600">入组日期：</span>
              <span>{patient.enrollment_date}</span>
            </div>
            <div>
              <span className="text-gray-600">训练状态：</span>
              <StatusBadge 
                status={patient.training_status} 
                variant={
                  patient.training_status === '训练中' ? 'active' :
                  patient.training_status === '暂停' ? 'pending' :
                  patient.training_status === '休息' ? 'pending' : 'inactive'
                } 
              />
            </div>
            <div>
              <span className="text-gray-600">昨天训练次数：</span>
              <span>{patient.yesterdayTraining || '--'}</span>
            </div>
            <div>
              <span className="text-gray-600">今日通话次数：</span>
              <span className="inline-flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-600 rounded-full text-sm font-medium">
                {patient.todayCallCount || 0}
              </span>
            </div>
          </div>
        </div>

        {/* 通话状态 */}
        <div className="border rounded-lg p-4 mb-6">
          <div className="flex items-center gap-3 mb-4">
            <StatusIcon className={`w-6 h-6 ${statusDisplay.color}`} />
            <span className={`font-medium ${statusDisplay.color}`}>
              {statusDisplay.text}
            </span>
          </div>

          {/* 开始拨打按钮 */}
          {callStatus === CallStatus.PREPARING && (
            <button
              onClick={startCall}
              disabled={isStarting}
              className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:bg-gray-300"
            >
              <PlayIcon className="w-5 h-5" />
              <span>{isStarting ? '正在开始...' : '开始拨打'}</span>
            </button>
          )}

                     {/* 通话进行中的状态显示 */}
           {[CallStatus.DIALING, CallStatus.CONNECTED, CallStatus.CALL_ENDED].includes(callStatus as CallStatus) && (
            <div className="text-center">
              <div className="animate-pulse text-gray-600">
                {callStatus === CallStatus.DIALING && '正在拨号，请等待患者接听...'}
                {callStatus === CallStatus.CONNECTED && '患者已接听，通话进行中...'}
                {callStatus === CallStatus.CALL_ENDED && '通话已结束，正在分析通话内容...'}
              </div>
            </div>
          )}

                     {/* 通话失败状态 */}
           {[CallStatus.NOT_ANSWERED, CallStatus.HUNG_UP, CallStatus.UNREACHABLE].includes(callStatus as CallStatus) && (
            <div className="text-center">
              <div className="text-red-600 mb-4">
                {callStatus === CallStatus.NOT_ANSWERED && '患者未接听电话'}
                {callStatus === CallStatus.HUNG_UP && '患者直接挂断电话'}
                {callStatus === CallStatus.UNREACHABLE && '无法接通患者电话'}
              </div>
              <button
                onClick={resetCallState}
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                重新拨打
              </button>
            </div>
          )}
        </div>

        {/* 通话记录详情 */}
        {callStatus === CallStatus.ANALYSIS_COMPLETE && callRecord && (
          <div className="border rounded-lg p-4">
            <h4 className="font-medium mb-4">通话记录详情</h4>
            <div className="space-y-3 text-sm">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="text-gray-600">训练完成情况：</span>
                  <span className={`font-medium ${
                    callRecord['训练完成情况'] === '完成' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {callRecord['训练完成情况']}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">训练次数：</span>
                  <span className="font-medium">{callRecord['训练次数'] || '--'}</span>
                </div>
                <div>
                  <span className="text-gray-600">训练时长：</span>
                  <span>{callRecord['训练时长'] || '--'}</span>
                </div>
                <div>
                  <span className="text-gray-600">是否有不适感：</span>
                  <span className={`${
                    callRecord['是否有不适感'] === '是' ? 'text-red-600' : 'text-green-600'
                  }`}>
                    {callRecord['是否有不适感'] || '否'}
                  </span>
                </div>
              </div>

              {callRecord['不适感内容'] && (
                <div>
                  <span className="text-gray-600">不适感内容：</span>
                  <div className="mt-1 p-2 bg-red-50 border border-red-200 rounded text-red-700">
                    {callRecord['不适感内容']}
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 gap-3">
                <div>
                  <span className="text-gray-600">是否需要医生人工联系：</span>
                  <span className={`font-medium ${
                    callRecord['是否需要医生人工和患者联系'] === '是' ? 'text-red-600' : 'text-green-600'
                  }`}>
                    {callRecord['是否需要医生人工和患者联系'] || '否'}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">锻炼辅助仪器是否有问题：</span>
                  <span className={`${
                    callRecord['锻炼辅助仪器是否有问题'] === '是' ? 'text-red-600' : 'text-green-600'
                  }`}>
                    {callRecord['锻炼辅助仪器是否有问题'] || '否'}
                  </span>
                </div>
              </div>

              {callRecord['锻炼辅助仪器问题内容'] && (
                <div>
                  <span className="text-gray-600">仪器问题详情：</span>
                  <div className="mt-1 p-2 bg-yellow-50 border border-yellow-200 rounded text-yellow-700">
                    {callRecord['锻炼辅助仪器问题内容']}
                  </div>
                </div>
              )}

              {callRecord['对话历史记录'] && callRecord['对话历史记录'].length > 0 && (
                <div>
                  <span className="text-gray-600">对话历史记录：</span>
                  <div className="mt-2 max-h-40 overflow-y-auto border rounded p-2 bg-gray-50">
                    {callRecord['对话历史记录'].map((dialog: any, index: number) => (
                      <div key={index} className="mb-2 last:mb-0">
                        <div className="text-xs text-gray-500">
                          {dialog.role === 'assistant' ? '机器人' : '患者'}
                        </div>
                        <div className="text-sm">{dialog.content}</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 底部按钮 */}
        <div className="flex justify-end gap-3 mt-6">
          <button 
            onClick={handleClose}
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
};

const DashboardPage: React.FC<DashboardPageProps> = ({ 
  setShowPatientModal, 
  setShowAddPatientModal, 
  setSelectedPatient, 
  onEditPatient,
  refreshKey = 0
}) => {
  // 选项卡状态
  const [activeTab, setActiveTab] = useState<'today' | 'stats'>('today');
  
  // 今日随访相关状态
  const [searchTerm, setSearchTerm] = useState('');
  const [todayTasks, setTodayTasks] = useState<Patient[]>([]);
  const [allTasks, setAllTasks] = useState<Patient[]>([]); // 存储所有任务用于分页
  const [stats, setStats] = useState<StatsData>({
    totalPersonnel: 0,
    pendingToday: 0,
    completedToday: 0,
    avgTraining: 0,
    completionRate: 0
  });
  const [loading, setLoading] = useState(true);
  
  // 随访统计相关状态
  const [followUpStats, setFollowUpStats] = useState<FollowUpStat[]>([]);
  const [statsLoading, setStatsLoading] = useState(false);
  const [timeRange, setTimeRange] = useState<'day' | 'week' | 'month' | 'year'>('day');
  const [statsDateRange, setStatsDateRange] = useState(() => {
    // 默认设置为最近30天
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);
    
    return {
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0]
    };
  });
  
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [pageSize, setPageSize] = useState(10); // 每页显示10条记录
  
  // 拨打电话模态框状态
  const [showCallModal, setShowCallModal] = useState(false);
  const [callPatient, setCallPatient] = useState<Patient | null>(null);
  
  // 图表相关状态
  const [showCharts, setShowCharts] = useState(true);
  const [chartDataType, setChartDataType] = useState<'total_follow_up_people' | 'total_calls' | 'connected_people' | 'training_people_count' | 'doctor_intervention_people'>('total_follow_up_people');
  
  // 详情模态框状态
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [detailData, setDetailData] = useState<any[]>([]);
  const [detailLoading, setDetailLoading] = useState(false);
  const [selectedTimeRange, setSelectedTimeRange] = useState<string>('');

  useEffect(() => {
    // 获取统计数据和患者数据
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // 使用当前日期而不是固定日期
        const today = new Date().toISOString().split('T')[0];
        const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0];
        
        // 获取今天和昨天的数据进行对比
        const [personnelData, todayCallRecordsResp, yesterdayCallRecordsResp] = await Promise.all([
          personnelAPI.getList({ limit: 100 }),
          callRecordsAPI.getList({ 
            start_date: today, 
            end_date: today,
            limit: 100 
          }),
          callRecordsAPI.getList({ 
            start_date: yesterday, 
            end_date: yesterday,
            limit: 100 
          })
        ]);
        
        // 从API响应中提取数据数组
        const todayCallRecords = todayCallRecordsResp?.data || [];
        const yesterdayCallRecords = yesterdayCallRecordsResp?.data || [];
        
        console.log('今日通话记录数据:', todayCallRecords);
        console.log('昨日通话记录数据:', yesterdayCallRecords);
        
        // 计算今日统计数据 - 按患者去重统计
        const activePersonnel = (personnelData || []).filter((p: any) => p.training_status === '训练中');
        
        // 获取今日已拨打过电话的患者（去重）- 这就是已完成随访
        const todayCalledPhones = new Set(
          todayCallRecords
            .map((r: any) => r['手机号'])
            .filter(Boolean)
        );
        const todayCompleted = todayCalledPhones.size;
        
        // 今日待随访人数 = 总训练中患者数 - 今日已拨打过电话的患者数，但不能小于0
        const todayPending = Math.max(0, activePersonnel.length - todayCalledPhones.size);
        const todayCompletionRate = activePersonnel.length > 0 ? Math.round((todayCompleted / activePersonnel.length) * 100) : 0;
        
        // 计算昨日统计数据用于对比 - 按患者去重统计
        const yesterdayCalledPhones = new Set(
          yesterdayCallRecords
            .map((r: any) => r['手机号'])
            .filter(Boolean)
        );
        const yesterdayCompleted = yesterdayCalledPhones.size;
        const yesterdayPending = Math.max(0, activePersonnel.length - yesterdayCalledPhones.size);
        const yesterdayCompletionRate = activePersonnel.length > 0 ? Math.round((yesterdayCompleted / activePersonnel.length) * 100) : 0;
        
        // 计算训练次数统计
        const todayTrainingData = todayCallRecords.filter((r: any) => r['训练次数'] && r['训练次数'] !== '--');
        const todayTrainingCounts = todayTrainingData.map((r: any) => parseInt(r['训练次数']) || 0).filter((count: number) => count > 0);
        const todayAvgTraining = todayTrainingCounts.length > 0 ? Math.round(todayTrainingCounts.reduce((a: number, b: number) => a + b, 0) / todayTrainingCounts.length) : 0;
        
        const yesterdayTrainingData = yesterdayCallRecords.filter((r: any) => r['训练次数'] && r['训练次数'] !== '--');
        const yesterdayTrainingCounts = yesterdayTrainingData.map((r: any) => parseInt(r['训练次数']) || 0).filter((count: number) => count > 0);
        const yesterdayAvgTraining = yesterdayTrainingCounts.length > 0 ? Math.round(yesterdayTrainingCounts.reduce((a: number, b: number) => a + b, 0) / yesterdayTrainingCounts.length) : 0;
        
        // 计算趋势
        const pendingChange = todayPending - yesterdayPending;
        const completedChange = todayCompleted - yesterdayCompleted;
        const completionRateChange = todayCompletionRate - yesterdayCompletionRate;
        const avgTrainingChange = todayAvgTraining - yesterdayAvgTraining;
        
        setStats({
          totalPersonnel: activePersonnel.length,
          pendingToday: todayPending,
          completedToday: todayCompleted,
          avgTraining: todayAvgTraining,
          completionRate: todayCompletionRate,
          // 趋势数据
          pendingTrend: {
            change: pendingChange,
            isUp: pendingChange > 0,
            text: pendingChange === 0 ? '与昨天持平' : `比昨天${pendingChange > 0 ? '增加' : '减少'}${Math.abs(pendingChange)}人`
          },
          completedTrend: {
            change: completedChange,
            isUp: completedChange > 0,
            text: completedChange === 0 ? '与昨天持平' : `比昨天${completedChange > 0 ? '增加' : '减少'}${Math.abs(completedChange)}人`
          },
          completionRateTrend: {
            change: completionRateChange,
            isUp: completionRateChange > 0,
            text: completionRateChange === 0 ? '与昨天持平' : `比昨天${completionRateChange > 0 ? '提升' : '下降'}${Math.abs(completionRateChange)}%`
          },
          avgTrainingTrend: {
            change: avgTrainingChange,
            isUp: avgTrainingChange > 0,
            text: avgTrainingChange === 0 ? '与昨天持平' : `比昨天${avgTrainingChange > 0 ? '增加' : '减少'}${Math.abs(avgTrainingChange)}次`
          }
        });
        
        // 处理今日任务数据  
        const tasks = activePersonnel.map((person: any) => {
          // 获取该患者今天所有的通话记录
          const todayPersonRecords = todayCallRecords.filter((r: any) => 
            r['手机号'] === person.phone && r['记录日期'] === today
          );
          
          // 计算今日通话次数
          const todayCallCount = todayPersonRecords.length;
          
          // 获取最新的通话记录作为主要状态判断
          const latestRecord = todayPersonRecords.length > 0 ? 
            todayPersonRecords.sort((a: any, b: any) => (b['通话时间'] || '').localeCompare(a['通话时间'] || ''))[0] : 
            null;
          
          // 获取今天通话记录中训练次数最高的那个
          const trainingRecords = todayPersonRecords
            .filter((r: any) => r['训练次数'] && r['训练次数'] !== '--' && r['训练次数'] !== '')
            .map((r: any) => {
              // 提取训练次数的数字部分
              const trainingStr = String(r['训练次数']);
              const numMatch = trainingStr.match(/\d+/);
              return {
                record: r,
                count: numMatch ? parseInt(numMatch[0]) : 0
              };
            })
            .filter((item: { record: any; count: number }) => item.count > 0)
            .sort((a: { record: any; count: number }, b: { record: any; count: number }) => b.count - a.count); // 按训练次数降序排列
          
          const highestTrainingRecord = trainingRecords.length > 0 ? trainingRecords[0].record : null;
          
          // 检查是否需要医生人工干预（只要有一条记录显示需要就是需要）
          const needsDoctorIntervention = todayPersonRecords.some((r: any) => 
            r['是否需要医生人工和患者联系'] === '是'
          );
          
          // 根据今日通话次数决定随访状态
          const followUpStatus = todayCallCount > 0 ? '已拨打' : '未拨打';
          const followUpStatusType = todayCallCount > 0 ? 'completed' : 'inactive';
          
          return {
            ...person,
            callStatus: followUpStatus,
            callStatusType: followUpStatusType,
            yesterdayTraining: highestTrainingRecord?.['训练次数'] || '--',
            needsDoctorIntervention: needsDoctorIntervention ? '是' : '否',
            todayCallCount: todayCallCount
          } as Patient;
        });
        
        // 存储所有任务数据
        setAllTasks(tasks);
        
        // 设置分页信息
        setTotalRecords(tasks.length);
        const calculatedPages = Math.ceil(tasks.length / pageSize);
        setTotalPages(Math.max(1, calculatedPages));
        
        // 重置到第一页
        setCurrentPage(1);
      } catch (error) {
        console.error('获取数据失败:', error);
        // 可以在这里显示错误提示
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [refreshKey]); // 添加refreshKey作为依赖

  // 获取随访统计数据
  const fetchFollowUpStats = async () => {
    try {
      setStatsLoading(true);
      
      const params: any = {
        page: currentPage,
        limit: pageSize,
        time_range: timeRange
      };
      
      // 如果设置了日期范围
      if (statsDateRange.startDate) {
        params.start_date = statsDateRange.startDate;
      }
      if (statsDateRange.endDate) {
        params.end_date = statsDateRange.endDate;
      }
      
      const response = await followUpStatsAPI.getStats(params);
      console.log('随访统计数据:', response);
      
      if (response && typeof response === 'object') {
        const statsData = response.data || [];
        const totalCount = response.total || 0;
        
        console.log('处理后的统计数据:', statsData);
        console.log('总记录数:', totalCount);
        console.log('当前followUpStats状态:', followUpStats);
        
        setFollowUpStats(statsData);
        setTotalRecords(totalCount);
        
        const calculatedPages = Math.ceil(totalCount / pageSize);
        setTotalPages(Math.max(1, calculatedPages));
        
        console.log('设置后的状态 - 数据长度:', statsData.length, '总页数:', Math.max(1, calculatedPages));
      } else {
        console.error('API响应格式不正确:', response);
      }
    } catch (error) {
      console.error('获取随访统计数据失败:', error);
      setFollowUpStats([]);
      setTotalRecords(0);
      setTotalPages(1);
    } finally {
      setStatsLoading(false);
    }
  };

  // 当切换到统计选项卡时获取数据
  useEffect(() => {
    if (activeTab === 'stats') {
      fetchFollowUpStats();
    }
  }, [activeTab, currentPage, pageSize, statsDateRange, timeRange]);

  // 过滤任务
  const filteredTasks = allTasks.filter(task => 
    !searchTerm || 
    task.name.includes(searchTerm) || 
    task.phone.includes(searchTerm)
  );

  // 计算当前页的任务数据
  const currentPageTasks = React.useMemo(() => {
    if (activeTab !== 'today') return [];
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredTasks.slice(startIndex, endIndex);
  }, [filteredTasks, currentPage, pageSize, activeTab]);

  // 当搜索条件改变时，重新计算分页（仅今日随访选项卡）
  React.useEffect(() => {
    if (activeTab === 'today') {
      setTotalRecords(filteredTasks.length);
      const calculatedPages = Math.ceil(filteredTasks.length / pageSize);
      setTotalPages(Math.max(1, calculatedPages));
      setCurrentPage(1); // 重置到第一页
    }
  }, [searchTerm, filteredTasks.length, pageSize, activeTab]);

  // 处理分页点击
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      setCurrentPage(page);
    }
  };

  // 处理每页显示条数改变
  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // 重置到第一页
    
    // 重新计算总页数
    const calculatedPages = Math.ceil(filteredTasks.length / newPageSize);
    setTotalPages(Math.max(1, calculatedPages));
  };

  // 处理页数跳转
  const [jumpToPage, setJumpToPage] = useState('');
  
  const handleJumpToPage = () => {
    const pageNum = parseInt(jumpToPage);
    if (!isNaN(pageNum) && pageNum >= 1 && pageNum <= totalPages) {
      setCurrentPage(pageNum);
      setJumpToPage('');
    } else {
      alert(`请输入1-${totalPages}之间的页码`);
    }
  };

  const handleJumpInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleJumpToPage();
    }
  };

  // 生成页码按钮
  const renderPageButtons = () => {
    const buttons = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      buttons.push(
        <button
          key={i}
          onClick={() => handlePageChange(i)}
          className={`w-9 h-9 border rounded flex items-center justify-center ${
            i === currentPage 
              ? 'bg-blue-500 text-white border-blue-500' 
              : 'hover:bg-gray-50 border-gray-300'
          }`}
        >
          {i}
        </button>
      );
    }
    
    return buttons;
  };

  // 处理拨打电话
  const handleMakeCall = (patient: Patient) => {
    setCallPatient(patient);
    setShowCallModal(true);
  };

  // 图表数据类型定义
  type ChartDataItem = {
    时间: string;
    总随访人数: number;
    电话拨打次数: number;
    接通人数: number;
    有训练人数: number;
    需要医生干预人数: number;
  };

  // 图表数据处理函数
  const getChartData = (): ChartDataItem[] => {
    return followUpStats.map((stat) => ({
      时间: stat.time.replace(/ \(第\d+周\)/, ''), // 简化时间显示
      总随访人数: stat.total_follow_up_people,
      电话拨打次数: stat.total_calls,
      接通人数: stat.connected_people,
      有训练人数: stat.training_people_count,
      需要医生干预人数: stat.doctor_intervention_people,
    }));
  };

  // 图表配置
  const getChartMetrics = () => {
    const metrics = {
      total_follow_up_people: { label: '总随访人数', color: '#3B82F6', key: '总随访人数' as keyof ChartDataItem },
      total_calls: { label: '电话拨打次数', color: '#10B981', key: '电话拨打次数' as keyof ChartDataItem },
      connected_people: { label: '接通人数', color: '#8B5CF6', key: '接通人数' as keyof ChartDataItem },
      training_people_count: { label: '有训练人数', color: '#06B6D4', key: '有训练人数' as keyof ChartDataItem },
      doctor_intervention_people: { label: '需要医生干预人数', color: '#EF4444', key: '需要医生干预人数' as keyof ChartDataItem },
    };
    return metrics[chartDataType];
  };

  // 饼图数据处理
  const getPieChartData = () => {
    const chartData = getChartData();
    const metric = getChartMetrics();
    
    // 聚合所有时间段的数据
    const totalValue = chartData.reduce((sum, item) => {
      const val = item[metric.key];
      return sum + (typeof val === 'number' ? val : 0);
    }, 0);
    
    const otherMetrics: (keyof ChartDataItem)[] = ['总随访人数', '电话拨打次数', '接通人数', '有训练人数', '需要医生干预人数']
      .filter(key => key !== metric.key) as (keyof ChartDataItem)[];
    
    const data = [
      { name: metric.label, value: totalValue, color: metric.color }
    ];
    
    // 添加其他指标数据用于对比
    otherMetrics.forEach((key, index) => {
      const value = chartData.reduce((sum, item) => {
        const val = item[key];
        return sum + (typeof val === 'number' ? val : 0);
      }, 0);
      const colors = ['#94A3B8', '#64748B', '#475569', '#334155'];
      data.push({ 
        name: key, 
        value: value, 
        color: colors[index % colors.length] 
      });
    });
    
    return data.filter(item => item.value > 0);
  };

  // 获取详情数据
  const fetchDetailData = async (stat: FollowUpStat) => {
    try {
      setDetailLoading(true);
      setSelectedTimeRange(stat.time);
      
      // 根据统计项的时间范围构建查询参数
      let startDate = '';
      let endDate = '';
      
      if (timeRange === 'day') {
        // 按天：使用该天的日期
        startDate = endDate = stat.time_key;
      } else if (timeRange === 'week') {
        // 按周：获取该周的开始和结束日期
        const monday = new Date(stat.time_key);
        const sunday = new Date(monday);
        sunday.setDate(monday.getDate() + 6);
        startDate = stat.time_key;
        endDate = sunday.toISOString().split('T')[0];
      } else if (timeRange === 'month') {
        // 按月：获取该月的开始和结束日期
        const year = parseInt(stat.time_key.split('-')[0]);
        const month = parseInt(stat.time_key.split('-')[1]);
        startDate = `${year}-${month.toString().padStart(2, '0')}-01`;
        const lastDay = new Date(year, month, 0).getDate();
        endDate = `${year}-${month.toString().padStart(2, '0')}-${lastDay.toString().padStart(2, '0')}`;
      } else if (timeRange === 'year') {
        // 按年：获取该年的开始和结束日期
        const year = stat.time_key;
        startDate = `${year}-01-01`;
        endDate = `${year}-12-31`;
      }
      
      // 调用通话记录API获取详细数据
      const response = await callRecordsAPI.getList({
        start_date: startDate,
        end_date: endDate,
        limit: 1000 // 获取所有记录
      });
      
      console.log('详情数据:', response);
      console.log('详情数据示例记录:', response?.data?.[0]); // 打印第一条记录看字段结构
      
      // 调试：检查不适感和设备问题内容字段
      if (response?.data?.[0]) {
        console.log('不适感相关字段:', {
          '是否有不适感': response.data[0]['是否有不适感'],
          '不适感内容': response.data[0]['不适感内容'],
          '不适感是否恢复': response.data[0]['不适感是否恢复']
        });
        console.log('设备问题相关字段:', {
          '锻炼辅助仪器是否有问题': response.data[0]['锻炼辅助仪器是否有问题'],
          '锻炼辅助仪器问题内容': response.data[0]['锻炼辅助仪器问题内容']
        });
        console.log('训练相关字段:', {
          '训练次数': response.data[0]['训练次数'],
          '训练未完成原因': response.data[0]['训练未完成原因']
        });
      }
      
      if (response && response.data) {
        setDetailData(response.data);
      } else {
        setDetailData([]);
      }
      
      setShowDetailModal(true);
    } catch (error) {
      console.error('获取详情数据失败:', error);
      setDetailData([]);
      setShowDetailModal(true);
    } finally {
      setDetailLoading(false);
    }
  };

  const statsData = [
    { 
      title: '患者总数', 
      value: stats.totalPersonnel, 
      trend: '训练中患者', 
      trendValue: '训练中患者', 
      isUp: true 
    },
    { 
      title: '今日待随访人数', 
      value: stats.pendingToday, 
      trend: stats.pendingTrend?.text || '暂无数据', 
      trendValue: stats.pendingTrend?.text || '暂无数据', 
      isUp: stats.pendingTrend?.isUp !== false 
    },
    { 
      title: '今日已完成随访', 
      value: stats.completedToday, 
      trend: stats.completedTrend?.text || '暂无数据', 
      trendValue: stats.completedTrend?.text || '暂无数据', 
      isUp: stats.completedTrend?.isUp !== false 
    },
    { 
                  title: '平均每日训练次数', 
      value: stats.avgTraining, 
      trend: stats.avgTrainingTrend?.text || '暂无数据', 
      trendValue: stats.avgTrainingTrend?.text || '暂无数据', 
      isUp: stats.avgTrainingTrend?.isUp !== false 
    },
  ];

  return (
    <div>
      <div className="flex justify-between items-center mb-8 pb-5 border-b">
        <h1 className="text-3xl font-bold text-slate-800">控制面板</h1>
        {activeTab === 'today' && (
          <div className="flex w-2/5">
            <input 
              type="text" 
              placeholder="搜索患者编号、姓名或电话..."
              className="flex-1 px-4 py-2 border rounded-l focus:outline-none focus:border-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <button className="px-4 py-2 bg-blue-500 text-white rounded-r hover:bg-blue-600">
              <MagnifyingGlassCircleIcon className="w-5 h-5" />
            </button>
          </div>
        )}
        {activeTab === 'stats' && (
          <div className="flex items-center gap-3 flex-wrap">
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">时间维度：</label>
              <select
                value={timeRange}
                onChange={(e) => {
                  setTimeRange(e.target.value as 'day' | 'week' | 'month' | 'year');
                  setCurrentPage(1);
                }}
                className="px-3 py-2 border rounded focus:outline-none focus:border-blue-500"
              >
                <option value="day">按天</option>
                <option value="week">按周</option>
                <option value="month">按月</option>
                <option value="year">按年</option>
              </select>
            </div>
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">开始日期：</label>
              <input 
                type="date" 
                value={statsDateRange.startDate}
                onChange={(e) => setStatsDateRange(prev => ({...prev, startDate: e.target.value}))}
                className="px-3 py-2 border rounded focus:outline-none focus:border-blue-500"
              />
            </div>
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">结束日期：</label>
              <input 
                type="date" 
                value={statsDateRange.endDate}
                onChange={(e) => setStatsDateRange(prev => ({...prev, endDate: e.target.value}))}
                className="px-3 py-2 border rounded focus:outline-none focus:border-blue-500"
              />
            </div>
            <button 
              onClick={() => {
                setCurrentPage(1);
                fetchFollowUpStats();
              }}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              查询
            </button>
          </div>
        )}
      </div>

      {/* 选项卡导航 */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => {
                setActiveTab('today');
                setCurrentPage(1);
              }}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'today'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              今日随访
            </button>
            <button
              onClick={() => {
                setActiveTab('stats');
                setCurrentPage(1);
              }}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'stats'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              随访统计
            </button>
          </nav>
        </div>
      </div>

      {/* 今日随访选项卡内容 */}
      {activeTab === 'today' && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5 mb-8">
            {statsData.map((stat, index) => (
              <StatCard key={index} {...stat} />
            ))}
          </div>

      <div className="bg-white rounded-lg p-5 shadow-sm">
        <div className="flex justify-between items-center mb-5">
          <h2 className="text-xl font-semibold text-slate-800">今日随访任务</h2>
          <div className="flex gap-3">
            <button className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-slate-700 rounded hover:bg-gray-200">
              <FunnelIcon className="w-4 h-4" />
              <span>筛选</span>
            </button>
            <button
              onClick={() => setShowAddPatientModal(true)}
              className="hidden flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              <PlusIcon className="w-4 h-4" />
              <span>添加患者</span>
            </button>
          </div>
        </div>

        {/* 结果统计信息 */}
        <div className="mb-4 flex justify-between items-center bg-gray-50 p-4 rounded">
          <div className="text-sm text-gray-600">
            共找到 {totalRecords} 位患者，当前第 {currentPage} 页，共 {totalPages} 页
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">每页显示：</span>
            <div className="flex gap-1">
              {[10, 20, 50].map((size) => (
                <button
                  key={size}
                  onClick={() => handlePageSizeChange(size)}
                  className={`px-3 py-1 text-sm border rounded ${
                    pageSize === size
                      ? 'bg-blue-500 text-white border-blue-500'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  {size}
                </button>
              ))}
            </div>
            <span className="text-sm text-gray-600">条</span>
          </div>
        </div>

        <table className="w-full">
          <thead>
            <tr className="border-b">
              <th className="text-left py-3 px-4 font-semibold text-gray-600">患者编号</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">患者信息</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">联系方式</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">入组日期</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">随访状态</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">昨天训练次数</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">今日通话次数</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">是否需要医生人工</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">操作</th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={9} className="py-8 text-center text-gray-500">
                  加载中...
                </td>
              </tr>
            ) : currentPageTasks.length === 0 ? (
              <tr>
                <td colSpan={9} className="py-8 text-center text-gray-500">
                  {searchTerm ? '没有找到匹配的患者' : '暂无随访任务'}
                </td>
              </tr>
            ) : (
              currentPageTasks.map((task) => (
                <tr key={task._id} className="border-b hover:bg-gray-50">
                  <td className="py-3 px-4 font-mono text-sm font-medium">{task.patient_id || '未设置'}</td>
                  <td className="py-3 px-4">{task.name} ({task.age}岁 {task.gender})</td>
                  <td className="py-3 px-4">{task.phone}</td>
                  <td className="py-3 px-4">{task.enrollment_date}</td>
                  <td className="py-3 px-4">
                    <StatusBadge status={task.callStatus || ''} variant={task.callStatusType || 'inactive'} />
                  </td>
                  <td className="py-3 px-4">{task.yesterdayTraining}</td>
                  <td className="py-3 px-4 text-center">
                    <span className="inline-flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-600 rounded-full text-sm font-medium">
                      {task.todayCallCount}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`inline-block px-2 py-1 rounded text-xs ${
                      task.needsDoctorIntervention === '是' 
                        ? 'bg-red-100 text-red-600' 
                        : 'bg-green-100 text-green-600'
                    }`}>
                      {task.needsDoctorIntervention}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <button 
                      onClick={() => {
                        setSelectedPatient(task);
                        setShowPatientModal(true);
                      }}
                      className="text-gray-500 hover:text-blue-500 mr-2"
                    >
                      <EyeIcon className="w-5 h-5" />
                    </button>
                    <button 
                      onClick={() => handleMakeCall(task)}
                      className="text-gray-500 hover:text-green-500 mr-2"
                      title="拨打电话"
                    >
                      <PhoneIcon className="w-5 h-5" />
                    </button>
                    <button 
                      onClick={() => onEditPatient(task)}
                      className="text-gray-500 hover:text-blue-500"
                    >
                      <PencilIcon className="w-5 h-5" />
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>

        {/* 分页控件 */}
        <div className="flex justify-between items-center mt-5 p-4 bg-gray-50 rounded-lg border">
          {/* 左侧页数跳转 */}
          <div className="flex items-center gap-2">
            {totalPages > 1 ? (
              <>
                <span className="text-sm text-gray-600">跳转到</span>
                <input
                  type="number"
                  min="1"
                  max={totalPages}
                  value={jumpToPage}
                  onChange={(e) => setJumpToPage(e.target.value)}
                  onKeyPress={handleJumpInputKeyPress}
                  placeholder="页码"
                  className="w-16 px-2 py-1 text-sm border rounded text-center"
                />
                <span className="text-sm text-gray-600">页</span>
                <button 
                  onClick={handleJumpToPage}
                  className="px-3 py-1 text-sm bg-blue-500 text-white border border-blue-500 rounded hover:bg-blue-600"
                >
                  跳转
                </button>
              </>
            ) : (
              <span className="text-sm text-gray-600">第 {currentPage} / {totalPages} 页</span>
            )}
          </div>
          
          {/* 中间导航按钮 - 始终显示 */}
          <div className="flex items-center gap-2 flex-wrap">
            {/* 首页按钮 */}
            <button 
              onClick={() => handlePageChange(1)}
              disabled={currentPage === 1 || totalPages <= 1}
              className={`px-3 py-2 border rounded text-sm ${
                currentPage === 1 || totalPages <= 1
                  ? 'text-gray-400 border-gray-200 cursor-not-allowed bg-gray-100' 
                  : 'hover:bg-gray-50 border-gray-300 text-gray-700 bg-white'
              }`}
            >
              首页
            </button>
            
            {/* 上一页按钮 */}
            <button 
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1 || totalPages <= 1}
              className={`w-9 h-9 border rounded flex items-center justify-center ${
                currentPage === 1 || totalPages <= 1
                  ? 'text-gray-400 border-gray-200 cursor-not-allowed bg-gray-100' 
                  : 'hover:bg-gray-50 border-gray-300 bg-white'
              }`}
            >
              <ChevronLeftIcon className="w-4 h-4" />
            </button>
            
            {/* 页码按钮 */}
            {totalPages > 1 ? renderPageButtons() : (
              <button className="w-9 h-9 border rounded flex items-center justify-center bg-blue-500 text-white border-blue-500">
                {currentPage}
              </button>
            )}
            
            {/* 下一页按钮 */}
            <button 
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages || totalPages <= 1}
              className={`w-9 h-9 border rounded flex items-center justify-center ${
                currentPage === totalPages || totalPages <= 1
                  ? 'text-gray-400 border-gray-200 cursor-not-allowed bg-gray-100' 
                  : 'hover:bg-gray-50 border-gray-300 bg-white'
              }`}
            >
              <ChevronRightIcon className="w-4 h-4" />
            </button>
            
            {/* 尾页按钮 */}
            <button 
              onClick={() => handlePageChange(totalPages)}
              disabled={currentPage === totalPages || totalPages <= 1}
              className={`px-3 py-2 border rounded text-sm ${
                currentPage === totalPages || totalPages <= 1
                  ? 'text-gray-400 border-gray-200 cursor-not-allowed bg-gray-100' 
                  : 'hover:bg-gray-50 border-gray-300 text-gray-700 bg-white'
              }`}
            >
              尾页
            </button>
          </div>
          
          {/* 右侧分页状态 */}
          <div className="text-sm text-gray-600">
            共 {totalRecords} 位患者
          </div>
        </div>

        {/* 调试信息 - 可通过环境变量控制显示 */}
        {/* {process.env.NODE_ENV === 'development' && (
          <div className="mt-2 p-2 bg-gray-100 text-xs text-gray-600 rounded">
            调试信息: 总患者数={totalRecords}, 当前页={currentPage}, 总页数={totalPages}, 每页条数={pageSize}, 当前页患者数={currentPageTasks.length}
          </div>
        )} */}
      </div>
        </>
      )}

      {/* 随访统计选项卡内容 */}
      {activeTab === 'stats' && (
        <div className="bg-white rounded-lg p-5 shadow-sm">
          <div className="flex justify-between items-center mb-5">
            <h2 className="text-xl font-semibold text-slate-800">历史随访统计</h2>
            <div className="flex items-center gap-3">
              <button
                onClick={() => setShowCharts(!showCharts)}
                className={`flex items-center gap-2 px-3 py-2 rounded text-sm ${
                  showCharts 
                    ? 'bg-blue-100 text-blue-600 border border-blue-200' 
                    : 'bg-gray-100 text-gray-600 border border-gray-200'
                }`}
              >
                {showCharts ? <EyeSlashIcon className="w-4 h-4" /> : <ChartBarIcon className="w-4 h-4" />}
                {showCharts ? '隐藏图表' : '显示图表'}
              </button>
            </div>
          </div>

          {/* 图表控制和显示区域 */}
          {showCharts && followUpStats.length > 0 && (
            <div className="mb-6 p-4 bg-gray-50 rounded-lg border">
              {/* 图表控制器 */}
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-700">数据可视化</h3>
                <div className="flex items-center gap-2">
                  <label className="text-sm font-medium text-gray-600">指标选择：</label>
                  <select
                    value={chartDataType}
                    onChange={(e) => setChartDataType(e.target.value as typeof chartDataType)}
                    className="px-3 py-1 border rounded text-sm focus:outline-none focus:border-blue-500"
                  >
                    <option value="total_follow_up_people">总随访人数</option>
                    <option value="total_calls">电话拨打次数</option>
                    <option value="connected_people">接通人数</option>
                    <option value="training_people_count">有训练人数</option>
                    <option value="doctor_intervention_people">需要医生干预人数</option>
                  </select>
                </div>
              </div>

              {/* 图表区域 */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                {/* 柱状图 */}
                <div className="bg-white p-3 rounded border">
                  <h4 className="text-sm font-medium text-gray-600 mb-2">柱状图</h4>
                  <div className="h-48">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={getChartData()}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis 
                          dataKey="时间" 
                          tick={{ fontSize: 10 }}
                          angle={-45}
                          textAnchor="end"
                          height={60}
                        />
                        <YAxis tick={{ fontSize: 10 }} />
                        <Tooltip />
                        <Bar 
                          dataKey={getChartMetrics().key} 
                          fill={getChartMetrics().color}
                          name={getChartMetrics().label}
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </div>

                {/* 折线图 */}
                <div className="bg-white p-3 rounded border">
                  <h4 className="text-sm font-medium text-gray-600 mb-2">趋势图</h4>
                  <div className="h-48">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={getChartData()}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis 
                          dataKey="时间" 
                          tick={{ fontSize: 10 }}
                          angle={-45}
                          textAnchor="end"
                          height={60}
                        />
                        <YAxis tick={{ fontSize: 10 }} />
                        <Tooltip />
                        <Line 
                          type="monotone" 
                          dataKey={getChartMetrics().key} 
                          stroke={getChartMetrics().color}
                          strokeWidth={2}
                          dot={{ fill: getChartMetrics().color, strokeWidth: 2, r: 3 }}
                          name={getChartMetrics().label}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </div>

                {/* 饼图 */}
                <div className="bg-white p-3 rounded border">
                  <h4 className="text-sm font-medium text-gray-600 mb-2">占比分析</h4>
                  <div className="h-48">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={getPieChartData()}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          outerRadius={60}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {getPieChartData().map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value, name) => [value, name]} />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 结果统计信息 */}
          <div className="mb-4 flex justify-between items-center bg-gray-50 p-4 rounded">
            <div className="text-sm text-gray-600">
              共找到 {totalRecords} 天数据，当前第 {currentPage} 页，共 {totalPages} 页
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">每页显示：</span>
              <div className="flex gap-1">
                {[10, 20, 50].map((size) => (
                  <button
                    key={size}
                    onClick={() => handlePageSizeChange(size)}
                    className={`px-3 py-1 text-sm border rounded ${
                      pageSize === size
                        ? 'bg-blue-500 text-white border-blue-500'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {size}
                  </button>
                ))}
              </div>
              <span className="text-sm text-gray-600">条</span>
            </div>
          </div>

          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="text-left py-3 px-4 font-semibold text-gray-600">时间</th>
                <th className="text-center py-3 px-4 font-semibold text-gray-600">总随访人数</th>
                <th className="text-center py-3 px-4 font-semibold text-gray-600">电话拨打次数</th>
                <th className="text-center py-3 px-4 font-semibold text-gray-600">接通人数</th>
                <th className="text-center py-3 px-4 font-semibold text-gray-600">有训练人数</th>
                <th className="text-center py-3 px-4 font-semibold text-gray-600">需要医生干预人数</th>
                <th className="text-center py-3 px-4 font-semibold text-gray-600">操作</th>
              </tr>
            </thead>
            <tbody>
              {(() => {
                console.log('表格渲染时的状态:', {
                  statsLoading,
                  followUpStatsLength: followUpStats.length,
                  followUpStats: followUpStats,
                  activeTab
                });
                return null;
              })()}
              {statsLoading ? (
                <tr>
                  <td colSpan={7} className="py-8 text-center text-gray-500">
                    加载中...
                  </td>
                </tr>
              ) : followUpStats.length === 0 ? (
                <tr>
                  <td colSpan={7} className="py-8 text-center text-gray-500">
                    暂无统计数据
                  </td>
                </tr>
              ) : (
                followUpStats.map((stat, index) => (
                  <tr key={index} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4 font-medium">{stat.time}</td>
                    <td className="py-3 px-4 text-center">
                      <span className="inline-flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-full text-sm font-medium">
                        {stat.total_follow_up_people}
                      </span>
                    </td>
                    <td className="py-3 px-4 text-center">
                      <span className="inline-flex items-center justify-center w-8 h-8 bg-green-100 text-green-600 rounded-full text-sm font-medium">
                        {stat.total_calls}
                      </span>
                    </td>
                    <td className="py-3 px-4 text-center">
                      <span className="inline-flex items-center justify-center w-8 h-8 bg-purple-100 text-purple-600 rounded-full text-sm font-medium">
                        {stat.connected_people}
                      </span>
                    </td>
                    <td className="py-3 px-4 text-center">
                      <span className={`inline-flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                        stat.training_people_count > 0 
                          ? 'bg-emerald-100 text-emerald-600' 
                          : 'bg-gray-100 text-gray-500'
                      }`}>
                        {stat.training_people_count}
                      </span>
                    </td>
                    <td className="py-3 px-4 text-center">
                      <span className={`inline-flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                        stat.doctor_intervention_people > 0 
                          ? 'bg-red-100 text-red-600' 
                          : 'bg-gray-100 text-gray-500'
                      }`}>
                        {stat.doctor_intervention_people}
                      </span>
                    </td>
                    <td className="py-3 px-4 text-center">
                      <button
                        onClick={() => fetchDetailData(stat)}
                        disabled={detailLoading}
                        className="inline-flex items-center gap-1 px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <EyeIcon className="w-4 h-4" />
                        {detailLoading ? '加载中...' : '查看详情'}
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>

          {/* 分页控件 */}
          <div className="flex justify-between items-center mt-5 p-4 bg-gray-50 rounded-lg border">
            {/* 左侧页数跳转 */}
            <div className="flex items-center gap-2">
              {totalPages > 1 ? (
                <>
                  <span className="text-sm text-gray-600">跳转到</span>
                  <input
                    type="number"
                    min="1"
                    max={totalPages}
                    value={jumpToPage}
                    onChange={(e) => setJumpToPage(e.target.value)}
                    onKeyPress={handleJumpInputKeyPress}
                    placeholder="页码"
                    className="w-16 px-2 py-1 text-sm border rounded text-center"
                  />
                  <span className="text-sm text-gray-600">页</span>
                  <button 
                    onClick={handleJumpToPage}
                    className="px-3 py-1 text-sm bg-blue-500 text-white border border-blue-500 rounded hover:bg-blue-600"
                  >
                    跳转
                  </button>
                </>
              ) : (
                <span className="text-sm text-gray-600">第 {currentPage} / {totalPages} 页</span>
              )}
            </div>
            
            {/* 中间导航按钮 - 始终显示 */}
            <div className="flex items-center gap-2 flex-wrap">
              {/* 首页按钮 */}
              <button 
                onClick={() => handlePageChange(1)}
                disabled={currentPage === 1 || totalPages <= 1}
                className={`px-3 py-2 border rounded text-sm ${
                  currentPage === 1 || totalPages <= 1
                    ? 'text-gray-400 border-gray-200 cursor-not-allowed bg-gray-100' 
                    : 'hover:bg-gray-50 border-gray-300 text-gray-700 bg-white'
                }`}
              >
                首页
              </button>
              
              {/* 上一页按钮 */}
              <button 
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1 || totalPages <= 1}
                className={`w-9 h-9 border rounded flex items-center justify-center ${
                  currentPage === 1 || totalPages <= 1
                    ? 'text-gray-400 border-gray-200 cursor-not-allowed bg-gray-100' 
                    : 'hover:bg-gray-50 border-gray-300 bg-white'
                }`}
              >
                <ChevronLeftIcon className="w-4 h-4" />
              </button>
              
              {/* 页码按钮 */}
              {totalPages > 1 ? renderPageButtons() : (
                <button className="w-9 h-9 border rounded flex items-center justify-center bg-blue-500 text-white border-blue-500">
                  {currentPage}
                </button>
              )}
              
              {/* 下一页按钮 */}
              <button 
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages || totalPages <= 1}
                className={`w-9 h-9 border rounded flex items-center justify-center ${
                  currentPage === totalPages || totalPages <= 1
                    ? 'text-gray-400 border-gray-200 cursor-not-allowed bg-gray-100' 
                    : 'hover:bg-gray-50 border-gray-300 bg-white'
                }`}
              >
                <ChevronRightIcon className="w-4 h-4" />
              </button>
              
              {/* 尾页按钮 */}
              <button 
                onClick={() => handlePageChange(totalPages)}
                disabled={currentPage === totalPages || totalPages <= 1}
                className={`px-3 py-2 border rounded text-sm ${
                  currentPage === totalPages || totalPages <= 1
                    ? 'text-gray-400 border-gray-200 cursor-not-allowed bg-gray-100' 
                    : 'hover:bg-gray-50 border-gray-300 text-gray-700 bg-white'
                }`}
              >
                尾页
              </button>
            </div>
            
            {/* 右侧分页状态 */}
            <div className="text-sm text-gray-600">
              共 {totalRecords} 天数据
            </div>
          </div>
        </div>
      )}

      {/* 拨打电话模态框 */}
      <CallModal 
        isOpen={showCallModal}
        onClose={() => setShowCallModal(false)}
        patient={callPatient}
      />

      {/* 详情模态框 */}
      <DetailModal 
        isOpen={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        data={detailData}
        loading={detailLoading}
        timeRange={selectedTimeRange}
      />
    </div>
  );
};

export default DashboardPage; 