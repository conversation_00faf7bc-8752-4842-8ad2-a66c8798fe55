import React, { useState, useEffect } from 'react';
import { MagnifyingGlassCircleIcon } from '@heroicons/react/24/outline';

import { progressAPI } from '../services/api';
import { Patient, ProgressData } from '../types';

const ProgressPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [patients, setPatients] = useState<Patient[]>([]);
  const [progressData, setProgressData] = useState<Record<string, ProgressData>>({});
  const [loading, setLoading] = useState(true);

  const getProgressColor = (progress: number) => {
    if (progress >= 90) return '#2ecc71';
    if (progress >= 80) return '#3498db';
    if (progress >= 60) return '#f39c12';
    return '#e74c3c';
  };

  // 获取康复进度数据
  const fetchProgressData = async () => {
    try {
      setLoading(true);
      const data = await progressAPI.get({ search: searchTerm });
      setPatients(data.personnel || []);
      setProgressData(data.progress || {});
    } catch (error) {
      console.error('获取康复进度数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProgressData();
  }, [searchTerm]);

  const filteredPatients = patients;

  return (
    <div>
      <div className="flex justify-between items-center mb-8 pb-5 border-b">
        <h1 className="text-3xl font-bold text-slate-800">康复进度</h1>
        <div className="flex w-2/5">
          <input 
            type="text" 
            placeholder="搜索患者姓名..." 
            className="flex-1 px-4 py-2 border rounded-l focus:outline-none focus:border-blue-500"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <button className="px-4 py-2 bg-blue-500 text-white rounded-r hover:bg-blue-600">
            <MagnifyingGlassCircleIcon className="w-5 h-5" />
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
        {filteredPatients.map((patient) => {
          const progress = progressData[patient._id] || {
            currentDay: 0,
            totalDays: 30,
            compliance: 0,
            todayTraining: 0,
            avgTraining: 0
          };
          
          return (
            <div key={patient._id} className="bg-white rounded-lg overflow-hidden shadow-sm">
              <div className="bg-blue-500 text-white p-4 flex justify-between items-center">
                <h3 className="font-semibold">{patient.name}</h3>
                <span>{patient.age}岁 {patient.gender}</span>
              </div>
              <div className="p-4">
                <div className="relative w-36 h-36 mx-auto mb-5">
                  <svg className="w-36 h-36 transform -rotate-90">
                    <circle
                      cx="72"
                      cy="72"
                      r="60"
                      stroke="#e6e6e6"
                      strokeWidth="10"
                      fill="none"
                    />
                    <circle
                      cx="72"
                      cy="72"
                      r="60"
                      stroke={getProgressColor(progress.compliance)}
                      strokeWidth="10"
                      fill="none"
                      strokeDasharray={`${(progress.compliance / 100) * 377} 377`}
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-3xl font-bold">{progress.compliance}%</span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-3 mb-4">
                  <div className="text-center p-3 bg-gray-50 rounded">
                    <p className="text-sm text-gray-500 mb-1">当前天数</p>
                    <p className="text-lg font-semibold">{progress.currentDay}/{progress.totalDays}</p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded">
                    <p className="text-sm text-gray-500 mb-1">昨日训练次数</p>
                    <p className="text-lg font-semibold">{progress.todayTraining}次</p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded">
                    <p className="text-sm text-gray-500 mb-1">平均训练次数</p>
                    <p className="text-lg font-semibold">{progress.avgTraining}次</p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded">
                    <p className="text-sm text-gray-500 mb-1">依从性</p>
                    <p className="text-lg font-semibold">{progress.compliance}%</p>
                  </div>
                </div>

                <button className="w-full py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                  查看详情
                </button>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default ProgressPage; 