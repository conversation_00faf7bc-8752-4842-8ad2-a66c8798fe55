import React, { useState, useEffect } from 'react';
import { 
  MagnifyingGlassCircleIcon,
  PlayIcon,
  XMarkIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import { callRecordsAPI } from '../services/api';
import { CallRecord, FilterOptions } from '../types';
import StatusBadge from '../components/StatusBadge';

const RecordingsPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<FilterOptions>({
    startDate: '',
    endDate: '',
    callType: '全部',
    status: '全部'
  });
  const [records, setRecords] = useState<CallRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [expandedRecords, setExpandedRecords] = useState<Record<string, boolean>>({});
  
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [pageSize, setPageSize] = useState(10); // 每页显示10条记录

  // 批量编辑相关状态
  const [isBatchEditMode, setIsBatchEditMode] = useState(false);
  const [selectedRecords, setSelectedRecords] = useState<Set<string>>(new Set());

  // 获取通话记录数据
  const fetchRecords = async (page: number = 1) => {
    try {
      setLoading(true);
      
      // 构建API参数，只有非空值才传递
      const params: any = {
        page,
        limit: pageSize
      };
      
      if (searchTerm.trim()) {
        params.search = searchTerm.trim();
      }
      
      if (filters.startDate) {
        params.start_date = filters.startDate;
      }
      
      if (filters.endDate) {
        params.end_date = filters.endDate;
      }
      
      if (filters.callType !== '全部') {
        params.call_type = filters.callType;
      }
      
      if (filters.status !== '全部') {
        params.status = filters.status;
      }

      const response = await callRecordsAPI.getList(params);
      
      console.log('API响应:', response); // 添加调试日志
      console.log('API请求参数:', params); // 添加API参数调试
      
      // 处理API返回的分页数据
      if (response && typeof response === 'object' && 'data' in response && 'total' in response) {
        // 处理修复后的API响应格式: {data: [...], total: number, page: number, limit: number, success: true}
        console.log('使用修复后的API格式处理:', response);
        const recordsData = response.data || [];
        const totalCount = response.total || 0;
        const returnedPage = response.page || page;
        const returnedLimit = response.limit || pageSize;
        
        setRecords(recordsData);
        setTotalRecords(totalCount);
        
        // 计算总页数，使用返回的limit而不是前端的pageSize，确保一致性
        const calculatedPages = Math.ceil(totalCount / returnedLimit);
        console.log('修复API格式 - 计算总页数:', calculatedPages, '总记录数:', totalCount, '前端每页条数:', pageSize, 'API返回limit:', returnedLimit, '当前数据条数:', recordsData.length, 'API返回页码:', returnedPage);
        setTotalPages(Math.max(1, calculatedPages)); // 至少为1页
      } else if (Array.isArray(response)) {
        // 兼容原有格式（直接返回数组）
        console.log('使用旧格式处理:', response);
        const dataArray: any[] = response || [];
        
        // 如果是数组格式，需要进行客户端分页
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const paginatedData = dataArray.slice(startIndex, endIndex);
        
        setRecords(paginatedData);
        setTotalRecords(dataArray.length);
        const calculatedPages = Math.ceil(dataArray.length / pageSize);
        console.log('数组格式 - 计算总页数:', calculatedPages, '总记录数:', dataArray.length, '每页条数:', pageSize, '当前页数据:', paginatedData.length);
        setTotalPages(Math.max(1, calculatedPages)); // 至少为1页
      } else {
        console.log('未知格式:', response);
        setRecords([]);
        setTotalRecords(0);
        setTotalPages(1);
      }
    } catch (error) {
      console.error('获取通话记录失败:', error);
      setRecords([]);
      setTotalRecords(0);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  // 当搜索条件改变时，重置到第一页
  useEffect(() => {
    setCurrentPage(1);
    setSelectedRecords(new Set()); // 清空选择状态
    fetchRecords(1);
  }, [searchTerm, filters, pageSize]);

  // 当页码改变时获取数据
  useEffect(() => {
    if (currentPage > 1) {
      fetchRecords(currentPage);
    }
  }, [currentPage]);

  // 处理分页点击
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      setCurrentPage(page);
    }
  };

  // 处理每页显示条数改变
  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // 重置到第一页
  };

  // 处理页数跳转
  const [jumpToPage, setJumpToPage] = useState('');
  
  const handleJumpToPage = () => {
    const pageNum = parseInt(jumpToPage);
    if (!isNaN(pageNum) && pageNum >= 1 && pageNum <= totalPages) {
      setCurrentPage(pageNum);
      setJumpToPage('');
    } else {
      alert(`请输入1-${totalPages}之间的页码`);
    }
  };

  const handleJumpInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleJumpToPage();
    }
  };

  // 批量编辑相关函数
  const toggleBatchEditMode = () => {
    setIsBatchEditMode(!isBatchEditMode);
    setSelectedRecords(new Set()); // 清空选择状态
  };

  // 全选/取消全选（针对当前页）
  const handleSelectAll = () => {
    if (selectedRecords.size === records.length && records.length > 0) {
      // 如果当前页全选了，则取消全选
      const newSelected = new Set(selectedRecords);
      records.forEach(r => newSelected.delete(r._id));
      setSelectedRecords(newSelected);
    } else {
      // 选择当前页所有记录
      const newSelected = new Set(selectedRecords);
      records.forEach(r => newSelected.add(r._id));
      setSelectedRecords(newSelected);
    }
  };

  // 选择/取消选择单个记录
  const handleSelectRecord = (recordId: string) => {
    const newSelected = new Set(selectedRecords);
    if (newSelected.has(recordId)) {
      newSelected.delete(recordId);
    } else {
      newSelected.add(recordId);
    }
    setSelectedRecords(newSelected);
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedRecords.size === 0) {
      alert('请先选择要删除的记录');
      return;
    }

    const confirmMessage = `确定要删除选中的 ${selectedRecords.size} 条通话记录吗？此操作不可撤销。`;
    if (!confirm(confirmMessage)) {
      return;
    }

    try {
      // 调用API删除记录
      const result = await callRecordsAPI.batchDelete(Array.from(selectedRecords));
      
      console.log('删除结果:', result); // 调试日志
      
      // 检查返回结果的格式
      if (result && typeof result === 'object') {
        const deletedCount = result.deleted_count || 0;
        const notFoundCount = result.not_found_count || 0;
        
        if (notFoundCount > 0) {
          alert(`删除完成：成功删除 ${deletedCount} 条记录，${notFoundCount} 条记录未找到`);
        } else {
          alert(`成功删除 ${deletedCount} 条记录`);
        }
      } else {
        // 如果返回格式不符合预期，但删除可能成功了
        alert(`删除操作已执行，请刷新页面查看结果`);
      }
      
      setSelectedRecords(new Set());
      fetchRecords(currentPage); // 重新获取数据
    } catch (error) {
      console.error('批量删除失败:', error);
      alert(`删除失败：${error instanceof Error ? error.message : '请重试'}`);
    }
  };

  // 生成页码按钮
  const renderPageButtons = () => {
    const buttons = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      buttons.push(
        <button
          key={i}
          onClick={() => handlePageChange(i)}
          className={`w-9 h-9 border rounded flex items-center justify-center ${
            i === currentPage 
              ? 'bg-blue-500 text-white border-blue-500' 
              : 'hover:bg-gray-50 border-gray-300'
          }`}
        >
          {i}
        </button>
      );
    }
    
    return buttons;
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-8 pb-5 border-b">
        <h1 className="text-3xl font-bold text-slate-800">通话记录</h1>
        <div className="flex w-2/5">
          <input 
            type="text" 
            placeholder="搜索患者编号、姓名或关键词..."
            className="flex-1 px-4 py-2 border rounded-l focus:outline-none focus:border-blue-500"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <button className="px-4 py-2 bg-blue-500 text-white rounded-r hover:bg-blue-600">
            <MagnifyingGlassCircleIcon className="w-5 h-5" />
          </button>
        </div>
      </div>

      <div className="bg-white rounded-lg p-5 shadow-sm">
        <div className="flex flex-wrap gap-4 mb-5 p-4 bg-gray-50 rounded">
          <div className="flex items-center gap-2">
            <label className="font-medium">日期范围：</label>
            <input 
              type="date" 
              value={filters.startDate}
              onChange={(e) => setFilters({...filters, startDate: e.target.value})}
              className="px-3 py-1 border rounded"
              placeholder="开始日期"
            />
            <span>至</span>
            <input 
              type="date" 
              value={filters.endDate}
              onChange={(e) => setFilters({...filters, endDate: e.target.value})}
              className="px-3 py-1 border rounded"
              placeholder="结束日期"
            />
          </div>
          <div className="flex items-center gap-2">
            <label className="font-medium">通话类型：</label>
            <select 
              value={filters.callType}
              onChange={(e) => setFilters({...filters, callType: e.target.value})}
              className="px-3 py-1 border rounded"
            >
              <option>全部</option>
              <option>首次入组</option>
              <option>日常随访</option>
              <option>中止提醒</option>
            </select>
          </div>
          <div className="flex items-center gap-2">
            <label className="font-medium">随访状态：</label>
            <select 
              value={filters.status}
              onChange={(e) => setFilters({...filters, status: e.target.value})}
              className="px-3 py-1 border rounded"
            >
              <option>全部</option>
              <option>已接通</option>
              <option>未接通</option>
              <option>拒绝接听</option>
            </select>
          </div>
          <button className="px-4 py-1 bg-blue-500 text-white rounded hover:bg-blue-600">
            应用筛选
          </button>
          <button 
            onClick={toggleBatchEditMode}
            className={`px-4 py-1 rounded ${
              isBatchEditMode 
                ? 'bg-orange-100 text-orange-700 hover:bg-orange-200' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {isBatchEditMode ? '退出批量编辑' : '批量编辑'}
          </button>
        </div>

        {/* 结果统计信息 */}
        <div className="mb-4 flex justify-between items-center">
          <div className="text-sm text-gray-600">
            共找到 {totalRecords} 条记录，当前第 {currentPage} 页，共 {totalPages} 页
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">每页显示：</span>
            <div className="flex gap-1">
              {[10, 20, 50].map((size) => (
                <button
                  key={size}
                  onClick={() => handlePageSizeChange(size)}
                  className={`px-3 py-1 text-sm border rounded ${
                    pageSize === size
                      ? 'bg-blue-500 text-white border-blue-500'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  {size}
                </button>
              ))}
            </div>
            <span className="text-sm text-gray-600">条</span>
          </div>
        </div>

        {/* 批量操作工具栏 */}
        {isBatchEditMode && (
          <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-4">
                <span className="text-sm font-medium text-blue-800">
                  已选择 {selectedRecords.size} 条记录
                </span>
                <div className="flex items-center gap-2 text-sm text-blue-700">
                  <span>注意：只对当前页的记录进行批量操作</span>
                </div>
              </div>
              
              {selectedRecords.size > 0 && (
                <div className="flex gap-2">
                  <button 
                    onClick={handleBatchDelete}
                    className="flex items-center gap-1 px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200"
                  >
                    <TrashIcon className="w-4 h-4" />
                    <span>删除</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                {isBatchEditMode && (
                  <th className="text-left py-3 px-4 font-semibold text-gray-600">
                    <input 
                      type="checkbox"
                      checked={records.length > 0 && records.every(r => selectedRecords.has(r._id))}
                      onChange={handleSelectAll}
                      className="rounded border-gray-300"
                    />
                  </th>
                )}
                <th className="text-left py-3 px-4 font-semibold text-gray-600">患者编号</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-600">患者信息</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-600">通话时间</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-600">通话状态</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-600">训练数据</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-600">健康状况</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-600">设备状态</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-600">医生干预</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-600">操作</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan={isBatchEditMode ? 10 : 9} className="py-8 text-center text-gray-500">
                    加载中...
                  </td>
                </tr>
              ) : records.length === 0 ? (
                <tr>
                  <td colSpan={isBatchEditMode ? 10 : 9} className="py-8 text-center text-gray-500">
                    暂无通话记录
                  </td>
                </tr>
              ) : (
                records.map((record) => (
                  <React.Fragment key={record._id}>
                    {/* 主要记录行 */}
                    <tr className="hover:bg-gray-50">
                      {isBatchEditMode && (
                        <td className="py-4 px-4">
                          <input 
                            type="checkbox"
                            checked={selectedRecords.has(record._id)}
                            onChange={() => handleSelectRecord(record._id)}
                            className="rounded border-gray-300"
                          />
                        </td>
                      )}
                      <td className="py-4 px-4 font-mono text-sm font-medium">{record['患者编号'] || '--'}</td>
                      <td className="py-4 px-4">
                        <div className="font-medium text-gray-900">{record['患者名字']}</div>
                        <div className="text-sm text-gray-500">{record['手机号']}</div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="font-medium">{record['记录日期']}</div>
                        <div className="text-sm text-gray-500">{record['通话时间']}</div>
                      </td>
                      <td className="py-4 px-4">
                        <StatusBadge 
                          status={record['训练完成情况'] === '完成' ? '已接通' : 
                                  record['训练完成情况'] === '未接通' ? '未接通' : 
                                  record['训练完成情况'] || '未知'} 
                          variant={record['训练完成情况'] === '完成' ? 'completed' : 
                                  record['训练完成情况'] === '未接通' ? 'pending' : 'inactive'} 
                        />
                        {record['对话历史记录'] && record['对话历史记录'].length > 0 && (
                          <div className="text-xs text-gray-500 mt-1">
                            对话 {record['对话历史记录'].length} 条
                          </div>
                        )}
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex flex-col gap-1">
                          {record['训练次数'] && (
                            <span className="px-2 py-1 bg-blue-100 text-blue-600 rounded text-xs font-medium w-fit">
                              训练 {record['训练次数']} 
                            </span>
                          )}
                          {record['训练时长'] && (
                            <span className="px-2 py-1 bg-purple-100 text-purple-600 rounded text-xs w-fit">
                              时长 {record['训练时长']}
                            </span>
                          )}
                          {record['依从性'] && (
                            <span className="px-2 py-1 bg-indigo-100 text-indigo-600 rounded text-xs w-fit">
                              依从性 {record['依从性']}
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex flex-col gap-1">
                          {record['是否有不适感'] === '否' && (
                            <span className="px-2 py-1 bg-green-100 text-green-600 rounded text-xs w-fit">
                              无不适感
                            </span>
                          )}
                          {record['是否有不适感'] === '是' && (
                            <span className="px-2 py-1 bg-red-100 text-red-600 rounded text-xs w-fit">
                              {record['不适感内容'] || '有不适'}
                            </span>
                          )}
                          {record['锻炼后是否有缓解'] === '是' && (
                            <span className="px-2 py-1 bg-emerald-100 text-emerald-600 rounded text-xs w-fit">
                              症状缓解
                            </span>
                          )}
                          {record['锻炼后是否有缓解'] === '否' && (
                            <span className="px-2 py-1 bg-orange-100 text-orange-600 rounded text-xs w-fit">
                              未见缓解
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex flex-col gap-1">
                          {record['锻炼辅助仪器是否有问题'] === '否' && (
                            <span className="px-2 py-1 bg-green-100 text-green-600 rounded text-xs w-fit">
                              设备正常
                            </span>
                          )}
                          {record['锻炼辅助仪器是否有问题'] === '是' && (
                            <span className="px-2 py-1 bg-red-100 text-red-600 rounded text-xs w-fit">
                              {record['锻炼辅助仪器问题内容'] || '设备异常'}
                            </span>
                          )}
                          {record['设备使用情况'] && (
                            <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs w-fit">
                              {record['设备使用情况']}
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex flex-col gap-1">
                          {record['是否需要医生人工和患者联系'] === '是' ? (
                            <span className="px-2 py-1 bg-red-100 text-red-600 rounded text-xs w-fit">
                              需要联系
                            </span>
                          ) : record['是否需要医生人工和患者联系'] === '否' ? (
                            <span className="px-2 py-1 bg-green-100 text-green-600 rounded text-xs w-fit">
                              无需干预
                            </span>
                          ) : (
                            <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs w-fit">
                              未评估
                            </span>
                          )}
                          {record['医生建议'] && (
                            <span className="px-2 py-1 bg-blue-100 text-blue-600 rounded text-xs w-fit">
                              有建议
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex gap-2">
                          {record['对话历史记录'] && record['对话历史记录'].length > 0 && (
                            <button 
                              onClick={() => {
                                setExpandedRecords(prev => ({
                                  ...prev,
                                  [record._id]: !prev[record._id]
                                }));
                              }}
                              className="text-blue-500 hover:text-blue-600 text-sm px-2 py-1 rounded border border-blue-200 hover:bg-blue-50"
                            >
                              {expandedRecords[record._id] ? '收起详情' : '查看详情'}
                            </button>
                          )}
                          {record['对话历史记录'] && (
                            <button className="text-green-500 hover:text-green-600 text-sm px-2 py-1 rounded border border-green-200 hover:bg-green-50">
                              <PlayIcon className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>

                    {/* 展开的详细信息行 */}
                    {expandedRecords[record._id] && record['对话历史记录'] && (
                      <tr>
                        <td colSpan={isBatchEditMode ? 10 : 9} className="px-4 py-4 bg-gray-50">
                          <div className="space-y-4">
                            {/* 康复效果评估信息 */}
                            {(record['康复效果评估'] || record['疼痛改善情况'] || record['活动能力变化']) && (
                              <div className="border-l-4 border-blue-500 pl-4">
                                <h4 className="font-medium text-gray-900 mb-2">康复效果评估</h4>
                                <div className="flex flex-wrap gap-2">
                                  {record['康复效果评估'] && (
                                    <span className="px-2 py-1 bg-emerald-100 text-emerald-600 rounded text-sm">
                                      整体评估: {record['康复效果评估']}
                                    </span>
                                  )}
                                  {record['疼痛改善情况'] && (
                                    <span className="px-2 py-1 bg-teal-100 text-teal-600 rounded text-sm">
                                      疼痛改善: {record['疼痛改善情况']}
                                    </span>
                                  )}
                                  {record['活动能力变化'] && (
                                    <span className="px-2 py-1 bg-cyan-100 text-cyan-600 rounded text-sm">
                                      活动能力: {record['活动能力变化']}
                                    </span>
                                  )}
                                </div>
                              </div>
                            )}

                            {/* 完整对话记录 */}
                            <div className="border-l-4 border-green-500 pl-4">
                              <div className="flex items-center justify-between mb-3">
                                <h4 className="font-medium text-gray-900">完整通话对话</h4>
                                <div className="flex items-center gap-2">
                                  <span className="text-sm text-gray-500">
                                    共 {record['对话历史记录'].length} 条对话
                                  </span>
                                  <button className="flex items-center gap-1 text-green-500 hover:text-green-600 text-sm px-2 py-1 rounded border border-green-200 hover:bg-green-50">
                                    <PlayIcon className="w-4 h-4" />
                                    播放录音
                                  </button>
                                </div>
                              </div>
                              
                              <div className="bg-white border rounded-lg p-4 max-h-96 overflow-y-auto">
                                <div className="space-y-3">
                                  {record['对话历史记录'].map((chat, chatIndex) => (
                                    <div key={chatIndex} className={`flex ${chat.role === 'assistant' ? 'justify-start' : 'justify-end'}`}>
                                      <div className={`max-w-[80%] p-3 rounded-lg ${
                                        chat.role === 'assistant' 
                                          ? 'bg-blue-100 text-blue-900' 
                                          : 'bg-green-100 text-green-900'
                                      }`}>
                                        <div className="text-xs font-medium mb-1 opacity-80">
                                          {chat.role === 'assistant' ? '智能助手' : record['患者名字']}
                                        </div>
                                        <div className="text-sm">{chat.content}</div>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            </div>

                            {/* 额外信息 */}
                            {record['医生建议'] && (
                              <div className="border-l-4 border-orange-500 pl-4">
                                <h4 className="font-medium text-gray-900 mb-2">医生建议</h4>
                                <div className="bg-white border rounded p-3 text-sm text-gray-700">
                                  {record['医生建议']}
                                </div>
                              </div>
                            )}
                          </div>
                        </td>
                      </tr>
                    )}
                  </React.Fragment>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* 分页控件 */}
        <div className="flex justify-between items-center mt-5 p-4 bg-gray-50 rounded-lg border">
          {/* 左侧页数跳转 */}
          <div className="flex items-center gap-2">
            {totalPages > 1 ? (
              <>
                <span className="text-sm text-gray-600">跳转到</span>
                <input
                  type="number"
                  min="1"
                  max={totalPages}
                  value={jumpToPage}
                  onChange={(e) => setJumpToPage(e.target.value)}
                  onKeyPress={handleJumpInputKeyPress}
                  placeholder="页码"
                  className="w-16 px-2 py-1 text-sm border rounded text-center"
                />
                <span className="text-sm text-gray-600">页</span>
                <button 
                  onClick={handleJumpToPage}
                  className="px-3 py-1 text-sm bg-blue-500 text-white border border-blue-500 rounded hover:bg-blue-600"
                >
                  跳转
                </button>
              </>
            ) : (
              <span className="text-sm text-gray-600">第 {currentPage} / {totalPages} 页</span>
            )}
          </div>
          
          {/* 中间导航按钮 - 始终显示 */}
          <div className="flex items-center gap-2 flex-wrap">
            {/* 首页按钮 */}
            <button 
              onClick={() => handlePageChange(1)}
              disabled={currentPage === 1 || totalPages <= 1}
              className={`px-3 py-2 border rounded text-sm ${
                currentPage === 1 || totalPages <= 1
                  ? 'text-gray-400 border-gray-200 cursor-not-allowed bg-gray-100' 
                  : 'hover:bg-gray-50 border-gray-300 text-gray-700 bg-white'
              }`}
            >
              首页
            </button>
            
            {/* 上一页按钮 */}
            <button 
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1 || totalPages <= 1}
              className={`w-9 h-9 border rounded flex items-center justify-center ${
                currentPage === 1 || totalPages <= 1
                  ? 'text-gray-400 border-gray-200 cursor-not-allowed bg-gray-100' 
                  : 'hover:bg-gray-50 border-gray-300 bg-white'
              }`}
            >
              <ChevronLeftIcon className="w-4 h-4" />
            </button>
            
            {/* 页码按钮 */}
            {totalPages > 1 ? renderPageButtons() : (
              <button className="w-9 h-9 border rounded flex items-center justify-center bg-blue-500 text-white border-blue-500">
                {currentPage}
              </button>
            )}
            
            {/* 下一页按钮 */}
            <button 
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages || totalPages <= 1}
              className={`w-9 h-9 border rounded flex items-center justify-center ${
                currentPage === totalPages || totalPages <= 1
                  ? 'text-gray-400 border-gray-200 cursor-not-allowed bg-gray-100' 
                  : 'hover:bg-gray-50 border-gray-300 bg-white'
              }`}
            >
              <ChevronRightIcon className="w-4 h-4" />
            </button>
            
            {/* 尾页按钮 */}
            <button 
              onClick={() => handlePageChange(totalPages)}
              disabled={currentPage === totalPages || totalPages <= 1}
              className={`px-3 py-2 border rounded text-sm ${
                currentPage === totalPages || totalPages <= 1
                  ? 'text-gray-400 border-gray-200 cursor-not-allowed bg-gray-100' 
                  : 'hover:bg-gray-50 border-gray-300 text-gray-700 bg-white'
              }`}
            >
              尾页
            </button>
          </div>
          
          {/* 右侧分页状态 */}
          <div className="text-sm text-gray-600">
            共 {totalRecords} 条记录
          </div>
        </div>

        {/* 调试信息 - 可通过环境变量控制显示 */}
        {/* {process.env.NODE_ENV === 'development' && (
          <div className="mt-2 p-2 bg-gray-100 text-xs text-gray-600 rounded">
            调试信息: 总记录数={totalRecords}, 当前页={currentPage}, 总页数={totalPages}, 每页条数={pageSize}, 记录数组长度={records.length}
          </div>
        )} */}
      </div>
    </div>
  );
};

export default RecordingsPage; 