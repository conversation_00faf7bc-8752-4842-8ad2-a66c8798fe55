import React, { useState } from 'react';
import UserManagement from '../components/UserManagement';
import SchedulerControl from '../components/SchedulerControl';

interface SettingsState {
  startTime: string;
  endTime: string;
  firstCallScript: string;
  dailyCallScript: string;
  retryCount: string;
  retryInterval: string;
  autoExport: string;
  exportFormat: string;
}

const SettingsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('system');
  const [settings, setSettings] = useState<SettingsState>({
    startTime: '09:00',
    endTime: '18:00',
    firstCallScript: '您好，我是膝关节康复中心的智能助手，您之前通过微信公众号报名参加了我们的膝关节康复仪器试用活动。请问您现在方便接听电话吗？',
    dailyCallScript: '您好，我是您的康复随访助手，今天打电话是想了解一下您昨天膝关节康复器的使用情况，请问您昨天做摆腿运动了吗？',
    retryCount: '2次',
    retryInterval: '1小时',
    autoExport: '每周导出',
    exportFormat: 'Excel'
  });

  const handleSave = () => {
    alert('设置已保存！');
  };

  return (
    <div>
      <div className="mb-8 pb-5 border-b">
        <h1 className="text-3xl font-bold text-slate-800">系统设置</h1>
      </div>

      {/* 标签页导航 */}
      <div className="mb-6">
        <nav className="flex space-x-8">
          <button
            onClick={() => setActiveTab('system')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'system'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            系统配置
          </button>
          <button
            onClick={() => setActiveTab('robot')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'robot'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            机器人管理
          </button>
          <button
            onClick={() => setActiveTab('users')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'users'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            用户管理
          </button>
        </nav>
      </div>

      {/* 标签页内容 */}
      {activeTab === 'system' && (
        <div className="bg-white rounded-lg p-6 shadow-sm">
          <h2 className="text-xl font-semibold text-slate-800 mb-6">智能随访设置</h2>

          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium mb-3">随访时间设置</label>
              <div className="grid grid-cols-2 gap-4 max-w-md">
                <div>
                  <label className="block text-sm text-gray-600 mb-1">开始时间</label>
                  <input 
                    type="time" 
                    value={settings.startTime}
                    onChange={(e) => setSettings({...settings, startTime: e.target.value})}
                    className="w-full px-3 py-2 border rounded"
                  />
                </div>
                <div>
                  <label className="block text-sm text-gray-600 mb-1">结束时间</label>
                  <input 
                    type="time" 
                    value={settings.endTime}
                    onChange={(e) => setSettings({...settings, endTime: e.target.value})}
                    className="w-full px-3 py-2 border rounded"
                  />
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">首次入组随访话术</label>
              <textarea 
                value={settings.firstCallScript}
                onChange={(e) => setSettings({...settings, firstCallScript: e.target.value})}
                className="w-full px-3 py-2 border rounded h-24 resize-y"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">日常随访话术</label>
              <textarea 
                value={settings.dailyCallScript}
                onChange={(e) => setSettings({...settings, dailyCallScript: e.target.value})}
                className="w-full px-3 py-2 border rounded h-24 resize-y"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-3">未接通重试设置</label>
              <div className="grid grid-cols-2 gap-4 max-w-md">
                <div>
                  <label className="block text-sm text-gray-600 mb-1">重试次数</label>
                  <select 
                    value={settings.retryCount}
                    onChange={(e) => setSettings({...settings, retryCount: e.target.value})}
                    className="w-full px-3 py-2 border rounded"
                  >
                    <option>1次</option>
                    <option>2次</option>
                    <option>3次</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm text-gray-600 mb-1">间隔时间</label>
                  <select 
                    value={settings.retryInterval}
                    onChange={(e) => setSettings({...settings, retryInterval: e.target.value})}
                    className="w-full px-3 py-2 border rounded"
                  >
                    <option>30分钟</option>
                    <option>1小时</option>
                    <option>2小时</option>
                  </select>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-3">数据导出设置</label>
              <div className="grid grid-cols-2 gap-4 max-w-md">
                <div>
                  <label className="block text-sm text-gray-600 mb-1">自动导出</label>
                  <select 
                    value={settings.autoExport}
                    onChange={(e) => setSettings({...settings, autoExport: e.target.value})}
                    className="w-full px-3 py-2 border rounded"
                  >
                    <option>不自动导出</option>
                    <option>每日导出</option>
                    <option>每周导出</option>
                    <option>每月导出</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm text-gray-600 mb-1">导出格式</label>
                  <select 
                    value={settings.exportFormat}
                    onChange={(e) => setSettings({...settings, exportFormat: e.target.value})}
                    className="w-full px-3 py-2 border rounded"
                  >
                    <option>Excel</option>
                    <option>CSV</option>
                    <option>JSON</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="pt-4">
              <button 
                onClick={handleSave}
                className="px-6 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                保存设置
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 机器人管理标签页 */}
      {activeTab === 'robot' && (
        <SchedulerControl />
      )}

      {/* 用户管理标签页 */}
      {activeTab === 'users' && (
        <UserManagement />
      )}
    </div>
  );
};

export default SettingsPage; 