#!/usr/bin/env python3
"""
锻炼计划数据库初始化脚本
创建exercise_plans集合并插入测试数据
"""

import pymongo
from datetime import datetime, timedelta
from bson import ObjectId
from config import Config
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def connect_to_database():
    """连接到MongoDB数据库"""
    try:
        client = pymongo.MongoClient(Config.MONGO_URL)
        client.server_info()  # 测试连接
        db = client[Config.DATABASE_NAME]
        logger.info(f"✅ 成功连接到数据库: {Config.DATABASE_NAME}")
        return client, db
    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        return None, None

def generate_test_exercise_plans():
    """生成测试锻炼计划数据"""
    plans = []
    
    # 计划1：张三的4周康复计划（进行中）
    plan1 = {
        "_id": ObjectId(),
        "plan_id": "EP_20250621_001",
        "patient_id": "P0001",
        "patient_name": "张三",
        "doctor_id": "doctor01",
        "doctor_name": "李医生",
        
        "plan_name": "膝关节康复训练计划",
        "description": "术后康复训练，逐步增加运动强度，促进关节功能恢复",
        "status": "active",
        
        "total_weeks": 4,
        "initial_daily_count": 200,
        "weekly_increment": 100,
        "max_rest_days_per_week": 2,
        
        "start_date": "2025-06-15",
        "end_date": "2025-07-13",
        "current_week": 1,
        "current_daily_target": 200,
        
        "weekly_records": [
            {
                "week_number": 1,
                "week_start_date": "2025-06-15",
                "week_end_date": "2025-06-21",
                "target_daily_count": 200,
                "daily_records": [
                    {
                        "date": "2025-06-15",
                        "day_of_week": 1,
                        "target_count": 200,
                        "actual_count": 180,
                        "is_rest_day": False,
                        "completion_rate": 0.9,
                        "notes": "第一天，适应中"
                    },
                    {
                        "date": "2025-06-16",
                        "day_of_week": 2,
                        "target_count": 200,
                        "actual_count": 200,
                        "is_rest_day": False,
                        "completion_rate": 1.0,
                        "notes": "状态良好"
                    },
                    {
                        "date": "2025-06-17",
                        "day_of_week": 3,
                        "target_count": 0,
                        "actual_count": 0,
                        "is_rest_day": True,
                        "completion_rate": 1.0,
                        "notes": "休息日"
                    },
                    {
                        "date": "2025-06-18",
                        "day_of_week": 4,
                        "target_count": 200,
                        "actual_count": 220,
                        "is_rest_day": False,
                        "completion_rate": 1.1,
                        "notes": "超额完成"
                    },
                    {
                        "date": "2025-06-19",
                        "day_of_week": 5,
                        "target_count": 200,
                        "actual_count": 190,
                        "is_rest_day": False,
                        "completion_rate": 0.95,
                        "notes": "略有疲劳"
                    },
                    {
                        "date": "2025-06-20",
                        "day_of_week": 6,
                        "target_count": 200,
                        "actual_count": 210,
                        "is_rest_day": False,
                        "completion_rate": 1.05,
                        "notes": "恢复良好"
                    },
                    {
                        "date": "2025-06-21",
                        "day_of_week": 7,
                        "target_count": 0,
                        "actual_count": 0,
                        "is_rest_day": True,
                        "completion_rate": 1.0,
                        "notes": "周末休息"
                    }
                ],
                "week_completion_rate": 1.0,
                "rest_days_used": 2
            }
        ],
        
        "total_target_count": 1000,
        "total_actual_count": 1000,
        "overall_completion_rate": 1.0,
        "consecutive_days": 3,
        "total_rest_days": 2,
        
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
        "created_by": "doctor01",
        "last_modified_by": "doctor01"
    }
    
    # 计划2：李四的6周强化计划（进行中）
    plan2 = {
        "_id": ObjectId(),
        "plan_id": "EP_20250621_002",
        "patient_id": "P0002",
        "patient_name": "李四",
        "doctor_id": "doctor01",
        "doctor_name": "李医生",
        
        "plan_name": "膝关节强化训练计划",
        "description": "加强型康复训练，适合恢复期患者",
        "status": "active",
        
        "total_weeks": 6,
        "initial_daily_count": 150,
        "weekly_increment": 50,
        "max_rest_days_per_week": 1,
        
        "start_date": "2025-06-10",
        "end_date": "2025-07-22",
        "current_week": 2,
        "current_daily_target": 200,
        
        "weekly_records": [],
        
        "total_target_count": 1050,
        "total_actual_count": 980,
        "overall_completion_rate": 0.93,
        "consecutive_days": 5,
        "total_rest_days": 2,
        
        "created_at": datetime.utcnow() - timedelta(days=11),
        "updated_at": datetime.utcnow(),
        "created_by": "doctor01",
        "last_modified_by": "doctor01"
    }
    
    # 计划3：王五的已完成计划
    plan3 = {
        "_id": ObjectId(),
        "plan_id": "EP_20250521_001",
        "patient_id": "P0003",
        "patient_name": "王五",
        "doctor_id": "doctor",
        "doctor_name": "测试医生",
        
        "plan_name": "基础康复训练计划",
        "description": "基础康复训练，已成功完成",
        "status": "completed",
        
        "total_weeks": 3,
        "initial_daily_count": 100,
        "weekly_increment": 75,
        "max_rest_days_per_week": 2,
        
        "start_date": "2025-05-21",
        "end_date": "2025-06-11",
        "current_week": 3,
        "current_daily_target": 250,
        
        "weekly_records": [],
        
        "total_target_count": 3675,
        "total_actual_count": 3500,
        "overall_completion_rate": 0.95,
        "consecutive_days": 15,
        "total_rest_days": 6,
        
        "created_at": datetime.utcnow() - timedelta(days=31),
        "updated_at": datetime.utcnow() - timedelta(days=10),
        "created_by": "doctor",
        "last_modified_by": "doctor"
    }
    
    plans.extend([plan1, plan2, plan3])
    return plans

def create_indexes(db):
    """创建索引以提高查询性能"""
    try:
        collection = db.exercise_plans
        
        # 创建复合索引
        collection.create_index([("patient_id", 1), ("status", 1)])
        collection.create_index([("doctor_id", 1), ("created_at", -1)])
        collection.create_index([("plan_id", 1)], unique=True)
        collection.create_index([("start_date", 1), ("end_date", 1)])
        
        logger.info("✅ 索引创建成功")
    except Exception as e:
        logger.error(f"❌ 索引创建失败: {e}")

def init_exercise_plans_collection():
    """初始化锻炼计划集合"""
    client, db = connect_to_database()
    if not client or not db:
        return False
    
    try:
        collection = db.exercise_plans
        
        # 检查集合是否已存在数据
        existing_count = collection.count_documents({})
        if existing_count > 0:
            logger.info(f"⚠️  exercise_plans集合已存在 {existing_count} 条记录")
            choice = input("是否清空现有数据并重新初始化？(y/N): ")
            if choice.lower() != 'y':
                logger.info("取消初始化")
                return True
            
            # 清空现有数据
            collection.delete_many({})
            logger.info("✅ 已清空现有数据")
        
        # 插入测试数据
        test_plans = generate_test_exercise_plans()
        result = collection.insert_many(test_plans)
        logger.info(f"✅ 成功插入 {len(result.inserted_ids)} 条锻炼计划记录")
        
        # 创建索引
        create_indexes(db)
        
        # 显示插入的数据概要
        logger.info("\n📋 插入的锻炼计划概要:")
        for plan in test_plans:
            logger.info(f"  - {plan['plan_id']}: {plan['patient_name']} - {plan['plan_name']} ({plan['status']})")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 初始化失败: {e}")
        return False
    finally:
        if client:
            client.close()

if __name__ == "__main__":
    logger.info("🚀 开始初始化锻炼计划数据...")
    success = init_exercise_plans_collection()
    if success:
        logger.info("✅ 锻炼计划数据初始化完成！")
    else:
        logger.error("❌ 锻炼计划数据初始化失败！")
