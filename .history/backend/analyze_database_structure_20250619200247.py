#!/usr/bin/env python3
"""
数据库结构分析脚本
读取 med_call_records 数据库中的所有集合和文档结构
"""

import pymongo
import json
from datetime import datetime
from typing import Dict, List, Any
import sys
import os

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def connect_to_database():
    """连接到MongoDB数据库"""
    try:
        # 使用默认的MongoDB连接
        mongo_url = 'mongodb://localhost:27017'
        client = pymongo.MongoClient(mongo_url, serverSelectionTimeoutMS=5000)
        
        # 测试连接
        client.server_info()
        
        # 连接到 med_call_records 数据库
        db = client['med_call_records']
        
        print(f"✅ 成功连接到数据库: {mongo_url}")
        print(f"✅ 数据库名称: med_call_records")
        
        return client, db
    
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None, None

def analyze_collection_structure(db, collection_name: str, sample_size: int = 5) -> Dict[str, Any]:
    """分析集合结构"""
    collection = db[collection_name]
    
    # 获取集合统计信息
    total_docs = collection.count_documents({})
    
    # 获取样本文档
    sample_docs = list(collection.find().limit(sample_size))
    
    # 分析字段结构
    all_fields = set()
    field_types = {}
    field_examples = {}
    
    for doc in sample_docs:
        for field, value in doc.items():
            all_fields.add(field)
            
            # 记录字段类型
            field_type = type(value).__name__
            if field not in field_types:
                field_types[field] = set()
            field_types[field].add(field_type)
            
            # 记录字段示例值
            if field not in field_examples:
                field_examples[field] = []
            
            # 处理不同类型的值
            if isinstance(value, (str, int, float, bool)):
                field_examples[field].append(value)
            elif isinstance(value, list):
                field_examples[field].append(f"[数组，长度: {len(value)}]")
            elif isinstance(value, dict):
                field_examples[field].append(f"[对象，键数: {len(value)}]")
            elif value is None:
                field_examples[field].append("null")
            else:
                field_examples[field].append(str(type(value).__name__))
    
    # 限制示例数量
    for field in field_examples:
        field_examples[field] = field_examples[field][:3]  # 只保留前3个示例
    
    return {
        'collection_name': collection_name,
        'total_documents': total_docs,
        'sample_size': len(sample_docs),
        'fields': sorted(list(all_fields)),
        'field_types': {field: list(types) for field, types in field_types.items()},
        'field_examples': field_examples,
        'sample_documents': sample_docs
    }

def format_field_info(field_name: str, types: List[str], examples: List[Any]) -> str:
    """格式化字段信息"""
    types_str = " | ".join(types)
    examples_str = ", ".join([f"`{ex}`" for ex in examples[:3]])
    return f"| `{field_name}` | {types_str} | {examples_str} |"

def generate_markdown_schema(analysis_results: List[Dict[str, Any]]) -> str:
    """生成Markdown格式的数据库架构文档"""
    
    markdown = f"""# 数据库结构分析报告

> 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
> 数据库名称: med_call_records

## 概述

本报告基于实际数据库内容分析生成，包含了 med_call_records 数据库中所有集合的结构信息。

"""
    
    # 添加集合概览
    markdown += "## 集合概览\n\n"
    markdown += "| 集合名称 | 文档数量 | 分析样本数 |\n"
    markdown += "|---------|---------|----------|\n"
    
    for result in analysis_results:
        markdown += f"| `{result['collection_name']}` | {result['total_documents']} | {result['sample_size']} |\n"
    
    markdown += "\n"
    
    # 为每个集合生成详细信息
    for result in analysis_results:
        collection_name = result['collection_name']
        markdown += f"## 集合: {collection_name}\n\n"
        markdown += f"**文档总数**: {result['total_documents']}\n\n"
        markdown += f"**分析样本数**: {result['sample_size']}\n\n"
        
        # 字段结构表
        markdown += "### 字段结构\n\n"
        markdown += "| 字段名 | 数据类型 | 示例值 |\n"
        markdown += "|-------|---------|--------|\n"
        
        for field in result['fields']:
            types = result['field_types'].get(field, ['unknown'])
            examples = result['field_examples'].get(field, [])
            markdown += format_field_info(field, types, examples) + "\n"
        
        markdown += "\n"
        
        # 示例文档
        if result['sample_documents']:
            markdown += "### 示例文档\n\n"
            for i, doc in enumerate(result['sample_documents'][:2], 1):  # 只显示前2个文档
                markdown += f"#### 示例文档 {i}\n\n"
                markdown += "```json\n"
                # 处理特殊类型以便JSON序列化
                def serialize_value(value):
                    if hasattr(value, 'isoformat'):  # datetime对象
                        return value.isoformat()
                    elif hasattr(value, '__str__') and 'ObjectId' in str(type(value)):  # ObjectId
                        return str(value)
                    elif isinstance(value, list):
                        return [serialize_value(item) for item in value]
                    elif isinstance(value, dict):
                        return {k: serialize_value(v) for k, v in value.items()}
                    else:
                        return value

                doc_copy = {k: serialize_value(v) for k, v in doc.items()}
                
                markdown += json.dumps(doc_copy, ensure_ascii=False, indent=2)
                markdown += "\n```\n\n"
        
        markdown += "---\n\n"
    
    return markdown

def main():
    """主函数"""
    print("🔍 开始分析数据库结构...")
    
    # 连接数据库
    client, db = connect_to_database()
    if client is None or db is None:
        print("❌ 无法连接到数据库，退出程序")
        sys.exit(1)
    
    try:
        # 获取所有集合名称
        collection_names = db.list_collection_names()
        print(f"📋 发现 {len(collection_names)} 个集合: {collection_names}")
        
        if not collection_names:
            print("⚠️  数据库中没有找到任何集合")
            return
        
        # 分析每个集合
        analysis_results = []
        for collection_name in collection_names:
            print(f"🔍 正在分析集合: {collection_name}")
            result = analyze_collection_structure(db, collection_name, sample_size=5)
            analysis_results.append(result)
            print(f"✅ 集合 {collection_name} 分析完成 (文档数: {result['total_documents']})")
        
        # 生成Markdown报告
        print("📝 生成Markdown报告...")
        markdown_content = generate_markdown_schema(analysis_results)
        
        # 保存到文件
        output_file = "database_schema.md"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        print(f"✅ 数据库结构分析完成！")
        print(f"📄 报告已保存到: {output_file}")
        
        # 显示简要统计
        print("\n📊 统计摘要:")
        for result in analysis_results:
            print(f"  - {result['collection_name']}: {result['total_documents']} 个文档, {len(result['fields'])} 个字段")
    
    except Exception as e:
        print(f"❌ 分析过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭数据库连接
        if client is not None:
            client.close()
            print("🔒 数据库连接已关闭")

if __name__ == "__main__":
    main()
