#!/usr/bin/env python3
"""
测试所有表格中的患者编号显示功能
验证控制面板、通话记录、训练数据等表格是否正确显示患者编号
"""

import requests
import json
import sys
import os

# 后端服务地址
BASE_URL = 'http://localhost:5000'

def login_user(username, password):
    """用户登录获取token"""
    login_url = f"{BASE_URL}/api/auth/login"
    login_data = {
        'username': username,
        'password': password
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                token = result.get('data', {}).get('token')
                user_info = result.get('data', {}).get('user', {})
                return token, user_info
            else:
                print(f"❌ 登录失败: {result.get('error')}")
                return None, None
        else:
            print(f"❌ 登录请求失败: {response.status_code}")
            return None, None
    except Exception as e:
        print(f"❌ 登录过程中出错: {e}")
        return None, None

def test_patients_api():
    """测试患者列表API的患者编号"""
    print("🔍 测试患者列表API...")
    
    # 管理员登录
    token, user_info = login_user('admin', 'admin123')
    if not token:
        print("❌ 管理员登录失败")
        return False
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(f"{BASE_URL}/api/personnel", headers=headers)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                patients = result.get('data', [])
                print(f"✅ 获取患者列表成功: {len(patients)} 个患者")
                
                # 检查患者编号
                patients_with_id = [p for p in patients if 'patient_id' in p and p['patient_id']]
                print(f"📊 有患者编号的患者: {len(patients_with_id)}/{len(patients)}")
                
                # 显示前几个患者的编号
                print("📋 患者编号示例:")
                for patient in patients[:5]:
                    print(f"   {patient.get('patient_id', '未设置')} - {patient.get('name')} - {patient.get('phone')}")
                
                return len(patients_with_id) == len(patients)
            else:
                print(f"❌ 获取患者列表失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 患者列表请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 测试患者列表API时出错: {e}")
        return False

def test_call_records_api():
    """测试通话记录API的患者编号"""
    print("\n🔍 测试通话记录API...")
    
    # 管理员登录
    token, user_info = login_user('admin', 'admin123')
    if not token:
        print("❌ 管理员登录失败")
        return False
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(f"{BASE_URL}/api/stats/call-records", headers=headers)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                records = result.get('data', [])
                print(f"✅ 获取通话记录成功: {len(records)} 条记录")
                
                # 检查患者编号
                records_with_id = [r for r in records if '患者编号' in r and r['患者编号']]
                print(f"📊 有患者编号的记录: {len(records_with_id)}/{len(records)}")
                
                # 显示前几条记录的编号
                print("📋 通话记录编号示例:")
                for record in records[:5]:
                    print(f"   {record.get('患者编号', '未设置')} - {record.get('患者名字')} - {record.get('记录日期')}")
                
                return len(records_with_id) == len(records)
            else:
                print(f"❌ 获取通话记录失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 通话记录请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 测试通话记录API时出错: {e}")
        return False

def test_dashboard_api():
    """测试控制面板API的患者编号"""
    print("\n🔍 测试控制面板API...")
    
    # 医生登录
    token, user_info = login_user('doctor01', 'doctor123')
    if not token:
        print("❌ 医生登录失败")
        return False
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        # 测试随访任务API
        response = requests.get(f"{BASE_URL}/api/personnel", headers=headers)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                tasks = result.get('data', [])
                print(f"✅ 获取随访任务成功: {len(tasks)} 个任务")
                
                # 检查患者编号
                tasks_with_id = [t for t in tasks if 'patient_id' in t and t['patient_id']]
                print(f"📊 有患者编号的任务: {len(tasks_with_id)}/{len(tasks)}")
                
                # 显示前几个任务的编号
                print("📋 随访任务编号示例:")
                for task in tasks[:3]:
                    print(f"   {task.get('patient_id', '未设置')} - {task.get('name')} - {task.get('assigned_doctor', '未分配')}")
                
                return len(tasks_with_id) == len(tasks)
            else:
                print(f"❌ 获取随访任务失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 随访任务请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 测试控制面板API时出错: {e}")
        return False

def test_search_by_patient_id():
    """测试按患者编号搜索功能"""
    print("\n🔍 测试按患者编号搜索功能...")
    
    # 管理员登录
    token, user_info = login_user('admin', 'admin123')
    if not token:
        print("❌ 管理员登录失败")
        return False
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    test_cases = [
        ('P0001', '患者列表'),
        ('P0002', '通话记录')
    ]
    
    all_passed = True
    
    for patient_id, test_type in test_cases:
        print(f"   测试搜索 {patient_id} 在 {test_type}...")
        
        try:
            if test_type == '患者列表':
                url = f"{BASE_URL}/api/personnel?search={patient_id}"
            else:  # 通话记录
                url = f"{BASE_URL}/api/stats/call-records?search={patient_id}"
            
            response = requests.get(url, headers=headers)
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    data = result.get('data', [])
                    
                    # 检查是否找到了正确的记录
                    found_correct = False
                    for item in data:
                        if test_type == '患者列表':
                            if item.get('patient_id') == patient_id:
                                found_correct = True
                                break
                        else:  # 通话记录
                            if item.get('患者编号') == patient_id:
                                found_correct = True
                                break
                    
                    if found_correct:
                        print(f"   ✅ 搜索 {patient_id} 成功: 找到 {len(data)} 条记录")
                    else:
                        print(f"   ❌ 搜索 {patient_id} 失败: 未找到匹配记录")
                        all_passed = False
                else:
                    print(f"   ❌ 搜索 {patient_id} 失败: {result.get('error')}")
                    all_passed = False
            else:
                print(f"   ❌ 搜索 {patient_id} 请求失败: {response.status_code}")
                all_passed = False
        except Exception as e:
            print(f"   ❌ 搜索 {patient_id} 时出错: {e}")
            all_passed = False
    
    return all_passed

def test_training_data_api():
    """测试训练数据API的患者编号"""
    print("\n🔍 测试训练数据API...")
    
    # 管理员登录
    token, user_info = login_user('admin', 'admin123')
    if not token:
        print("❌ 管理员登录失败")
        return False
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(f"{BASE_URL}/api/stats/training-data", headers=headers)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                training_data = result.get('data', [])
                print(f"✅ 获取训练数据成功: {len(training_data)} 条记录")
                
                if len(training_data) > 0:
                    # 检查患者编号
                    data_with_id = [d for d in training_data if '患者编号' in d and d['患者编号']]
                    print(f"📊 有患者编号的训练数据: {len(data_with_id)}/{len(training_data)}")
                    
                    # 显示前几条训练数据的编号
                    print("📋 训练数据编号示例:")
                    for data in training_data[:3]:
                        print(f"   {data.get('患者编号', '未设置')} - {data.get('患者名字')} - {data.get('记录日期')}")
                    
                    return len(data_with_id) == len(training_data)
                else:
                    print("📋 暂无训练数据")
                    return True
            else:
                print(f"❌ 获取训练数据失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 训练数据请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 测试训练数据API时出错: {e}")
        return False

def main():
    """主函数"""
    print("🧪 开始测试所有表格的患者编号显示功能...\n")
    
    # 检查后端服务是否运行
    try:
        response = requests.get(f"{BASE_URL}/api/health", timeout=5)
        if response.status_code != 200:
            print("❌ 后端服务未正常运行，请先启动后端服务")
            return
    except:
        print("❌ 无法连接到后端服务，请确保后端服务在 http://localhost:5000 运行")
        return
    
    print("✅ 后端服务连接正常\n")
    
    try:
        # 1. 测试患者列表API
        patients_passed = test_patients_api()
        
        # 2. 测试通话记录API
        call_records_passed = test_call_records_api()
        
        # 3. 测试控制面板API
        dashboard_passed = test_dashboard_api()
        
        # 4. 测试搜索功能
        search_passed = test_search_by_patient_id()
        
        # 5. 测试训练数据API
        training_passed = test_training_data_api()
        
        print("\n" + "="*60)
        print("📊 患者编号表格显示功能测试结果:")
        print("="*60)
        print(f"患者列表API测试: {'✅ 通过' if patients_passed else '❌ 失败'}")
        print(f"通话记录API测试: {'✅ 通过' if call_records_passed else '❌ 失败'}")
        print(f"控制面板API测试: {'✅ 通过' if dashboard_passed else '❌ 失败'}")
        print(f"搜索功能测试: {'✅ 通过' if search_passed else '❌ 失败'}")
        print(f"训练数据API测试: {'✅ 通过' if training_passed else '❌ 失败'}")
        
        all_passed = all([patients_passed, call_records_passed, dashboard_passed, search_passed, training_passed])
        
        if all_passed:
            print("\n🎉 所有表格的患者编号显示功能测试通过！")
            print("🎯 功能实现完整:")
            print("   ✅ 患者管理页面：显示患者编号列")
            print("   ✅ 控制面板表格：显示患者编号列")
            print("   ✅ 通话记录表格：显示患者编号列")
            print("   ✅ 训练数据表格：显示患者编号列")
            print("   ✅ 搜索功能：支持按患者编号搜索")
            print("\n💡 前端使用指南:")
            print("   - 所有表格都在第一列显示患者编号")
            print("   - 患者编号使用等宽字体显示，便于识别")
            print("   - 搜索框支持按编号、姓名、手机号搜索")
            print("   - 医生只能看到自己管理的患者数据")
        else:
            print("\n❌ 部分表格功能测试失败，请检查实现")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
