# 医疗随访系统数据库结构文档

本文档描述了医疗随访系统的MongoDB数据库结构，包含6个主要集合。

## 数据库概览

- **数据库名称**: `med_call_records`
- **集合数量**: 6个
- **主要功能**: 医疗随访、患者管理、通话记录、用户权限管理、锻炼计划管理

## 集合详细结构

### 1. call_records 集合
**用途**: 存储患者通话记录和随访数据

**文档结构**:
```json
{
  "_id": ObjectId,
  "患者名字": "string",
  "手机号": "string", 
  "记录日期": "string (YYYY-MM-DD)",
  "通话时间": "string",
  "拨号状态": "string",
  "训练完成情况": "string",
  "训练次数": "string",
  "训练时长": "string", 
  "依从性": "string",
  "是否有不适感": "string",
  "不适感内容": "string",
  "锻炼后是否有缓解": "string",
  "锻炼辅助仪器是否有问题": "string",
  "锻炼辅助仪器问题内容": "string",
  "设备使用情况": "string",
  "是否需要医生人工和患者联系": "string",
  "医生建议": "string",
  "康复效果评估": "string",
  "疼痛改善情况": "string",
  "活动能力变化": "string",
  "对话历史记录": [
    {
      "role": "assistant|user",
      "content": "string"
    }
  ]
}
```

**索引**:
- `手机号`: 单字段索引
- `记录日期`: 单字段索引
- `患者名字`: 单字段索引

### 2. personnel 集合
**用途**: 存储患者基本信息和管理数据

**文档结构**:
```json
{
  "_id": ObjectId,
  "patient_id": "string", // 患者编号，由医生填入
  "name": "string",
  "phone": "string",
  "age": number,
  "gender": "男|女",
  "enrollment_date": "string (YYYY-MM-DD)",
  "training_status": "未开始|训练中|暂停|终止|休息",
  "assigned_doctor": "string", // 分配的医生用户名
  "assigned_doctor_name": "string", // 分配的医生姓名
  "assignment_date": "string (YYYY-MM-DD)", // 医生分配日期
  "exercise_plan_id": "string", // 关联的锻炼计划ID (可选)
  "exercise_plan_name": "string", // 关联的锻炼计划名称 (冗余字段，便于显示)
  "created_at": "ISODate",
  "updated_at": "ISODate"
}
```

**索引**:
- `phone`: 唯一索引
- `patient_id`: 唯一索引
- `assigned_doctor`: 单字段索引
- `training_status`: 单字段索引
- `exercise_plan_id`: 单字段索引

### 3. users 集合
**用途**: 存储系统用户信息和权限

**文档结构**:
```json
{
  "_id": ObjectId,
  "username": "string",
  "password": "string", // 加密存储
  "email": "string",
  "full_name": "string",
  "role": "管理员|医生",
  "created_at": "ISODate",
  "updated_at": "ISODate"
}
```

**索引**:
- `username`: 唯一索引
- `email`: 唯一索引

### 4. progress 集合
**用途**: 存储患者康复进度数据

**文档结构**:
```json
{
  "_id": ObjectId,
  "progress": {
    "患者ID": {
      "currentDay": number,
      "totalDays": number,
      "compliance": number, // 依从性百分比
      "todayTraining": number,
      "avgTraining": number,
      "totalCallDays": number,
      "totalTrainingDays": number
    }
  },
  "updated_at": "ISODate"
}
```

### 5. scheduler_status 集合
**用途**: 存储定时任务状态信息

**文档结构**:
```json
{
  "_id": ObjectId,
  "status": "string",
  "last_run": "ISODate",
  "next_run": "ISODate",
  "task_name": "string"
}
```

### 6. exercise_plans 集合
**用途**: 存储锻炼计划模板和配置

**文档结构**:
```json
{
  "_id": ObjectId,
  "plan_name": "string", // 计划名称
  "description": "string", // 计划描述
  "status": "active|completed|paused|cancelled", // 计划状态
  "total_weeks": number, // 总周数
  "initial_daily_count": number, // 初始每日次数
  "weekly_increment": number, // 每周增加次数
  "max_rest_days_per_week": number, // 每周最多休息天数
  "created_at": "ISODate",
  "updated_at": "ISODate",
  "created_by": "string", // 创建者用户名
  "last_modified_by": "string" // 最后修改者用户名
}
```

**索引**:
- `created_by`: 单字段索引
- `status`: 单字段索引
- `plan_name`: 单字段索引
