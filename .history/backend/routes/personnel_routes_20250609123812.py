from flask import Blueprint, request, jsonify
from datetime import datetime
from bson import ObjectId
from database import get_db
from auth.auth import require_auth
from utils.serializers import serialize_doc

personnel_bp = Blueprint('personnel', __name__, url_prefix='/api/personnel')

@personnel_bp.route('', methods=['GET'])
@require_auth
def get_personnel():
    """获取患者列表"""
    try:
        db = get_db()
        search = request.args.get('search', '')
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 50))
        
        # 构建查询条件
        query = {}
        if search:
            query['$or'] = [
                {'name': {'$regex': search, '$options': 'i'}},
                {'phone': {'$regex': search, '$options': 'i'}}
            ]
        
        # 获取总数
        total = db.personnel.count_documents(query)
        
        # 分页查询
        skip = (page - 1) * limit
        cursor = db.personnel.find(query).skip(skip).limit(limit)
        personnel = [serialize_doc(doc) for doc in cursor]
        
        return jsonify({
            'success': True,
            'data': personnel,
            'total': total,
            'page': page,
            'limit': limit
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@personnel_bp.route('/batch', methods=['DELETE'])
@require_auth
def batch_delete_personnel():
    """批量删除患者"""
    try:
        db = get_db()
        data = request.get_json()
        
        if not data or 'ids' not in data:
            return jsonify({
                'success': False,
                'error': '缺少患者ID列表'
            }), 400
        
        ids = data['ids']
        if not ids or not isinstance(ids, list):
            return jsonify({
                'success': False,
                'error': '患者ID列表格式错误'
            }), 400
        
        # 转换ID格式
        object_ids = []
        for id_str in ids:
            try:
                object_ids.append(ObjectId(id_str))
            except:
                return jsonify({
                    'success': False,
                    'error': f'无效的患者ID: {id_str}'
                }), 400
        
        # 批量删除
        result = db.personnel.delete_many({'_id': {'$in': object_ids}})
        
        return jsonify({
            'success': True,
            'message': f'成功删除 {result.deleted_count} 个患者',
            'deleted_count': result.deleted_count
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@personnel_bp.route('/batch', methods=['PUT'])
@require_auth
def batch_update_personnel():
    """批量更新患者训练状态"""
    try:
        db = get_db()
        data = request.get_json()
        
        if not data or 'ids' not in data or 'training_status' not in data:
            return jsonify({
                'success': False,
                'error': '缺少必要参数'
            }), 400
        
        ids = data['ids']
        training_status = data['training_status']
        
        if not ids or not isinstance(ids, list):
            return jsonify({
                'success': False,
                'error': '患者ID列表格式错误'
            }), 400
        
        # 验证训练状态值
        valid_statuses = ['未开始', '训练中', '暂停', '终止', '休息']
        if training_status not in valid_statuses:
            return jsonify({
                'success': False,
                'error': f'无效的训练状态: {training_status}'
            }), 400
        
        # 转换ID格式
        object_ids = []
        for id_str in ids:
            try:
                object_ids.append(ObjectId(id_str))
            except:
                return jsonify({
                    'success': False,
                    'error': f'无效的患者ID: {id_str}'
                }), 400
        
        # 批量更新
        result = db.personnel.update_many(
            {'_id': {'$in': object_ids}},
            {
                '$set': {
                    'training_status': training_status,
                    'updated_at': datetime.utcnow()
                }
            }
        )
        
        return jsonify({
            'success': True,
            'message': f'成功更新 {result.modified_count} 个患者的训练状态',
            'modified_count': result.modified_count
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@personnel_bp.route('/<personnel_id>', methods=['GET'])
@require_auth
def get_personnel_detail(personnel_id):
    """获取患者详情"""
    try:
        db = get_db()
        # 获取患者基本信息
        personnel = db.personnel.find_one({'_id': ObjectId(personnel_id)})
        if not personnel:
            return jsonify({
                'success': False,
                'error': '患者不存在'
            }), 404
        
        # 获取患者通话记录
        call_records = [serialize_doc(doc) for doc in db.call_records.find({
            '手机号': personnel['phone']
        }).sort([('创建时间', -1)])]
        
        return jsonify({
            'success': True,
            'data': {
                'personnel': serialize_doc(personnel),
                'call_records': call_records
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@personnel_bp.route('', methods=['POST'])
@require_auth
def add_personnel():
    """添加新患者"""
    try:
        db = get_db()
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['name', 'phone', 'age', 'gender']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'缺少必填字段: {field}'
                }), 400
        
        # 检查电话号码是否已存在
        existing = db.personnel.find_one({'phone': data['phone']})
        if existing:
            return jsonify({
                'success': False,
                'error': '该电话号码已存在'
            }), 400
        
        # 添加时间戳
        data['created_at'] = datetime.utcnow()
        data['updated_at'] = datetime.utcnow()
        
        # 插入数据
        result = db.personnel.insert_one(data)
        
        # 获取插入的文档并序列化
        inserted_doc = db.personnel.find_one({'_id': result.inserted_id})
        
        return jsonify({
            'success': True,
            'data': serialize_doc(inserted_doc)
        })
        
    except Exception as e:
        print(f"❌ 添加患者时出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@personnel_bp.route('/<personnel_id>', methods=['PUT'])
@require_auth
def update_personnel(personnel_id):
    """更新患者信息"""
    try:
        db = get_db()
        data = request.get_json()
        
        # 验证患者是否存在
        personnel = db.personnel.find_one({'_id': ObjectId(personnel_id)})
        if not personnel:
            return jsonify({
                'success': False,
                'error': '患者不存在'
            }), 404
        
        # 如果更新电话号码，检查是否与其他患者重复
        if 'phone' in data and data['phone'] != personnel['phone']:
            existing = db.personnel.find_one({
                'phone': data['phone'],
                '_id': {'$ne': ObjectId(personnel_id)}
            })
            if existing:
                return jsonify({
                    'success': False,
                    'error': '该电话号码已被其他患者使用'
                }), 400
        
        # 添加更新时间戳
        data['updated_at'] = datetime.utcnow()
        
        # 更新数据
        result = db.personnel.update_one(
            {'_id': ObjectId(personnel_id)},
            {'$set': data}
        )
        
        if result.modified_count > 0:
            # 获取更新后的数据
            updated_personnel = db.personnel.find_one({'_id': ObjectId(personnel_id)})
            return jsonify({
                'success': True,
                'data': serialize_doc(updated_personnel)
            })
        else:
            return jsonify({
                'success': True,
                'message': '没有数据被修改',
                'data': serialize_doc(personnel)
            })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@personnel_bp.route('/<personnel_id>', methods=['DELETE'])
@require_auth
def delete_personnel(personnel_id):
    """删除患者"""
    try:
        db = get_db()
        
        # 验证患者是否存在
        personnel = db.personnel.find_one({'_id': ObjectId(personnel_id)})
        if not personnel:
            return jsonify({
                'success': False,
                'error': '患者不存在'
            }), 404
        
        # 删除患者
        result = db.personnel.delete_one({'_id': ObjectId(personnel_id)})
        
        if result.deleted_count > 0:
            return jsonify({
                'success': True,
                'message': '患者删除成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '删除失败'
            }), 500
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500