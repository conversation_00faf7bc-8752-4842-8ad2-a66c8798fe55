import React, { useState, useEffect } from 'react';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  ClockIcon,
  UserIcon,
  ChartBarIcon,
  ClipboardDocumentListIcon
} from '@heroicons/react/24/outline';
import { exercisePlanAPI } from '../services/api';
import { ExercisePlan } from '../types';
import CreateExercisePlanModal from '../components/CreateExercisePlanModal';
import EditExercisePlanModal from '../components/EditExercisePlanModal';

interface ExercisePlansPageProps {
  refreshKey?: number;
}

const ExercisePlansPage: React.FC<ExercisePlansPageProps> = ({ refreshKey = 0 }) => {
  const [exercisePlans, setExercisePlans] = useState<ExercisePlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<ExercisePlan | null>(null);

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);
  const pageSize = 10;

  useEffect(() => {
    fetchExercisePlans();
  }, [refreshKey, currentPage, statusFilter]);

  const fetchExercisePlans = async () => {
    try {
      setLoading(true);
      const response = await exercisePlanAPI.getList({
        page: currentPage,
        page_size: pageSize,
        status: statusFilter || undefined
      });

      if (response) {
        setExercisePlans(response.data || []);
        setTotal(response.pagination?.total || 0);
        setTotalPages(response.pagination?.total_pages || 1);
      }
    } catch (error) {
      console.error('获取锻炼计划失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleViewDetail = (plan: ExercisePlan) => {
    setSelectedPlan(plan);
    setShowEditModal(true);
  };

  const handleDeletePlan = async (planId: string) => {
    if (!confirm('确定要删除这个锻炼计划模板吗？')) return;

    try {
      await exercisePlanAPI.delete(planId);
      fetchExercisePlans();
    } catch (error) {
      console.error('删除锻炼计划模板失败:', error);
      alert('删除失败，请重试');
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { label: '进行中', className: 'bg-green-100 text-green-800' },
      completed: { label: '已完成', className: 'bg-blue-100 text-blue-800' },
      paused: { label: '暂停', className: 'bg-yellow-100 text-yellow-800' },
      cancelled: { label: '已取消', className: 'bg-red-100 text-red-800' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.active;
    
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${config.className}`}>
        {config.label}
      </span>
    );
  };

  const filteredPlans = exercisePlans.filter(plan => {
    // 搜索过滤
    const matchesSearch = plan.plan_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (plan.description && plan.description.toLowerCase().includes(searchTerm.toLowerCase()));

    // 状态过滤
    let matchesStatus = true;
    if (statusFilter === 'all') {
      matchesStatus = true; // 显示所有状态
    } else if (statusFilter) {
      matchesStatus = plan.status === statusFilter; // 显示特定状态
    } else {
      matchesStatus = plan.status !== 'cancelled'; // 默认隐藏已取消的计划
    }

    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-8 pb-5 border-b">
        <h1 className="text-3xl font-bold text-slate-800">锻炼计划模板</h1>
        <div className="flex items-center space-x-4">
          <div className="flex w-96">
            <input
              type="text"
              placeholder="搜索模板名称或描述..."
              className="flex-1 px-4 py-2 border rounded-l focus:outline-none focus:border-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <button className="px-4 py-2 bg-blue-500 text-white rounded-r hover:bg-blue-600">
              <MagnifyingGlassIcon className="w-5 h-5" />
            </button>
          </div>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-4 py-2 border rounded focus:outline-none focus:border-blue-500"
          >
            <option value="">活跃模板</option>
            <option value="active">进行中</option>
            <option value="completed">已完成</option>
            <option value="paused">暂停</option>
            <option value="cancelled">已取消</option>
            <option value="all">全部状态</option>
          </select>
          <button
            onClick={() => setShowCreateModal(true)}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 flex items-center"
          >
            <PlusIcon className="w-5 h-5 mr-2" />
            新建模板
          </button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 text-blue-600">
              <ClipboardDocumentListIcon className="w-6 h-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">总模板数</p>
              <p className="text-2xl font-bold text-gray-900">
                {exercisePlans.filter(p => p.status !== 'cancelled').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 text-green-600">
              <ChartBarIcon className="w-6 h-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">进行中</p>
              <p className="text-2xl font-bold text-gray-900">
                {exercisePlans.filter(p => p.status === 'active').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-yellow-100 text-yellow-600">
              <ClockIcon className="w-6 h-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">已完成</p>
              <p className="text-2xl font-bold text-gray-900">
                {exercisePlans.filter(p => p.status === 'completed').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-100 text-purple-600">
              <UserIcon className="w-6 h-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">暂停中</p>
              <p className="text-2xl font-bold text-gray-900">
                {exercisePlans.filter(p => p.status === 'paused').length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 锻炼计划列表 */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  计划信息
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  计划参数
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  创建信息
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  状态
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredPlans.map((plan) => (
                <tr key={plan._id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{plan.plan_name}</div>
                      <div className="text-sm text-gray-500">编号: {plan.plan_id}</div>
                      <div className="text-sm text-gray-500 max-w-xs truncate">
                        {plan.description || '暂无描述'}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      <div>总周数: {plan.total_weeks}周</div>
                      <div>初始: {plan.initial_daily_count}次/天</div>
                      <div>递增: +{plan.weekly_increment}次/周</div>
                      <div>最多休息: {plan.max_rest_days_per_week}天/周</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{plan.doctor_name || '未知'}</div>
                      <div className="text-sm text-gray-500">创建: {plan.created_at.split('T')[0]}</div>
                      <div className="text-sm text-gray-500">更新: {plan.updated_at.split('T')[0]}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="space-y-1">
                      {getStatusBadge(plan.status)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleViewDetail(plan)}
                        className="text-blue-600 hover:text-blue-900"
                        title="编辑计划"
                      >
                        <PencilIcon className="w-5 h-5" />
                      </button>
                      <button
                        onClick={() => handleDeletePlan(plan._id)}
                        className="text-red-600 hover:text-red-900"
                        title="删除模板"
                      >
                        <TrashIcon className="w-5 h-5" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* 分页 */}
        {totalPages > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                上一页
              </button>
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                下一页
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  显示第 <span className="font-medium">{(currentPage - 1) * pageSize + 1}</span> 到{' '}
                  <span className="font-medium">{Math.min(currentPage * pageSize, total)}</span> 条，
                  共 <span className="font-medium">{total}</span> 条记录
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    上一页
                  </button>
                  <span className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                    {currentPage} / {totalPages}
                  </span>
                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    下一页
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 创建锻炼计划模态框 */}
      <CreateExercisePlanModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={() => {
          fetchExercisePlans();
          setShowCreateModal(false);
        }}
      />

      {/* 编辑锻炼计划模态框 */}
      <EditExercisePlanModal
        isOpen={showEditModal}
        onClose={() => {
          setShowEditModal(false);
          setSelectedPlan(null);
        }}
        onSuccess={() => {
          fetchExercisePlans();
          setShowEditModal(false);
          setSelectedPlan(null);
        }}
        plan={selectedPlan}
      />
    </div>
  );
};

export default ExercisePlansPage;
