import React, { useState, useEffect } from 'react';
import { 
  PlusIcon,
  EyeIcon,
  PhoneIcon,
  PencilIcon,
  ChevronLeftIcon,
  ArrowDownTrayIcon,
  FunnelIcon,
  MagnifyingGlassCircleIcon,
  XMarkIcon,
  PlayIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  TrashIcon,
  CheckIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

import { personnelAPI, progressAPI } from '../services/api';
import StatusBadge from '../components/StatusBadge';
import { Patient, ProgressData, CallRecord } from '../types';

interface PatientsPageProps {
  setShowAddPatientModal: (show: boolean) => void;
  setSelectedPatient: (patient: Patient) => void;
  onViewPatientHistory: (patient: Patient) => void;
  onEditPatient: (patient: Patient) => void;
  viewingPatientHistory?: boolean;
  selectedPatient?: Patient | null;
  onBack?: () => void;
  refreshKey?: number; // 用于触发数据刷新
}

interface PatientCallHistoryPageProps {
  patient: Patient;
  onBack: () => void;
}

// 训练状态选项
const TRAINING_STATUS_OPTIONS = [
  { value: '未开始', label: '未开始', color: 'gray' },
  { value: '训练中', label: '训练中', color: 'green' },
  { value: '暂停', label: '暂停', color: 'yellow' },
  { value: '终止', label: '终止', color: 'red' },
  { value: '休息', label: '休息', color: 'blue' }
];

// Toast通知组件
const Toast: React.FC<{
  message: string;
  type?: 'success' | 'error' | 'warning';
  show: boolean;
  onClose: () => void;
}> = ({ message, type = 'success', show, onClose }) => {
  useEffect(() => {
    if (show) {
      const timer = setTimeout(() => {
        onClose();
      }, 4000); // 4秒后自动关闭
      return () => clearTimeout(timer);
    }
  }, [show, onClose]);

  if (!show) return null;

  const bgColor = type === 'success' ? 'bg-green-100 border-green-400 text-green-800' :
                  type === 'error' ? 'bg-red-100 border-red-400 text-red-800' :
                  'bg-yellow-100 border-yellow-400 text-yellow-800';

  return (
    <div className="fixed top-4 right-4 z-50 animate-slide-in">
      <div className={`${bgColor} border px-4 py-3 rounded-lg shadow-lg flex items-center gap-2 max-w-md`}>
        <span className="text-sm">{message}</span>
        <button 
          onClick={onClose}
          className="ml-2 text-lg hover:opacity-70"
        >
          ×
        </button>
      </div>
    </div>
  );
};

// 批量状态设置模态框
const BatchStatusModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  selectedCount: number;
  onConfirm: (status: string) => void;
}> = ({ isOpen, onClose, selectedCount, onConfirm }) => {
  const [selectedStatus, setSelectedStatus] = useState('');

  const handleConfirm = () => {
    if (selectedStatus) {
      onConfirm(selectedStatus);
      setSelectedStatus('');
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-96">
        <h3 className="text-lg font-semibold mb-4">批量设置训练状态</h3>
        <p className="text-gray-600 mb-4">
          将为 <span className="font-medium">{selectedCount}</span> 个患者设置训练状态
        </p>
        
        <div className="mb-6">
          <label className="block text-sm font-medium mb-2">选择训练状态</label>
          <select 
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="w-full px-3 py-2 border rounded focus:outline-none focus:border-blue-500"
          >
            <option value="">请选择训练状态</option>
            {TRAINING_STATUS_OPTIONS.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {selectedStatus === '训练中' && (
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded">
            <div className="flex items-center gap-2 text-blue-800">
              <ExclamationTriangleIcon className="w-5 h-5" />
              <span className="text-sm font-medium">提醒</span>
            </div>
            <p className="text-sm text-blue-700 mt-1">
              仅训练中的患者会被语音机器人每日自动随访
            </p>
          </div>
        )}
        
        <div className="flex justify-end gap-3">
          <button 
            onClick={onClose}
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
          >
            取消
          </button>
          <button 
            onClick={handleConfirm}
            disabled={!selectedStatus}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-300"
          >
            确认设置
          </button>
        </div>
      </div>
    </div>
  );
};

// 患者通话历史详情页面
const PatientCallHistoryPage: React.FC<PatientCallHistoryPageProps> = ({ patient, onBack }) => {
  const [patientRecords, setPatientRecords] = useState<CallRecord[]>([]);
  const [progress, setProgress] = useState<ProgressData>({
    currentDay: 0,
    totalDays: 30,
    compliance: 0,
    todayTraining: 0,
    avgTraining: 0
  });
  const [loading, setLoading] = useState(true);
  const [expandedDialogs, setExpandedDialogs] = useState<Record<string, boolean>>({});
  
  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [pageSize] = useState(10); // 每页显示10条记录

  const [allRecords, setAllRecords] = useState<CallRecord[]>([]);

  useEffect(() => {
    if (patient) {
      const fetchPatientDetails = async () => {
        try {
          setLoading(true);
          // 获取患者详情和所有通话记录
          const data = await personnelAPI.getDetail(patient._id);
          
          const records = data.call_records || [];
          // 按记录日期降序排列，最新的记录在前面
          const sortedRecords = records.sort((a: CallRecord, b: CallRecord) => {
            const dateA = new Date(a['记录日期'] + ' ' + (a['通话时间'] || '00:00'));
            const dateB = new Date(b['记录日期'] + ' ' + (b['通话时间'] || '00:00'));
            return dateB.getTime() - dateA.getTime();
          });
          
          setAllRecords(sortedRecords);
          setTotalRecords(sortedRecords.length);
          setTotalPages(Math.ceil(sortedRecords.length / pageSize));
          
          // 计算电话拨打天数和有训练天数
          const callDates = new Set<string>();
          const trainingDates = new Set<string>();
          
          records.forEach((record: CallRecord) => {
            if (record['记录日期']) {
              callDates.add(record['记录日期']);
              
              // 如果有训练次数且大于0，则算作有训练的一天
              const trainingCount = parseInt(record['训练次数'] || '0') || 0;
              if (trainingCount > 0) {
                trainingDates.add(record['记录日期']);
              }
            }
          });
          
          const totalCallDays = callDates.size;
          const totalTrainingDays = trainingDates.size;
          const newCompliance = totalCallDays > 0 ? Math.round((totalTrainingDays / totalCallDays) * 100) : 0;
          
          const progressData = await progressAPI.get();
          const originalProgress = progressData.progress[patient._id] || {};
          
          setProgress({
            ...originalProgress,
            currentDay: originalProgress.currentDay || 0,
            totalDays: originalProgress.totalDays || 30,
            compliance: newCompliance,
            todayTraining: originalProgress.todayTraining || 0,
            avgTraining: originalProgress.avgTraining || 0,
            totalCallDays: totalCallDays,
            totalTrainingDays: totalTrainingDays
          });
        } catch (error) {
          console.error('获取患者详情失败:', error);
        } finally {
          setLoading(false);
        }
      };
      
      fetchPatientDetails();
    }
  }, [patient, pageSize]);

  // 计算当前页的记录
  useEffect(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    setPatientRecords(allRecords.slice(startIndex, endIndex));
  }, [allRecords, currentPage, pageSize]);

  // 分页组件
  const PaginationComponent = () => {
    const pageNumbers = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }

    return (
      <div className="flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200">
        <div className="flex items-center text-sm text-gray-500">
          <span>
            显示 {((currentPage - 1) * pageSize) + 1} 到 {Math.min(currentPage * pageSize, totalRecords)} 条，
            共 {totalRecords} 条记录
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className={`px-3 py-1 rounded text-sm ${
              currentPage === 1 
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            上一页
          </button>
          
          {startPage > 1 && (
            <>
              <button
                onClick={() => setCurrentPage(1)}
                className="px-3 py-1 rounded text-sm bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                1
              </button>
              {startPage > 2 && <span className="text-gray-500">...</span>}
            </>
          )}
          
          {pageNumbers.map(pageNum => (
            <button
              key={pageNum}
              onClick={() => setCurrentPage(pageNum)}
              className={`px-3 py-1 rounded text-sm ${
                currentPage === pageNum
                  ? 'bg-blue-500 text-white'
                  : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
              }`}
            >
              {pageNum}
            </button>
          ))}
          
          {endPage < totalPages && (
            <>
              {endPage < totalPages - 1 && <span className="text-gray-500">...</span>}
              <button
                onClick={() => setCurrentPage(totalPages)}
                className="px-3 py-1 rounded text-sm bg-white border border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                {totalPages}
              </button>
            </>
          )}
          
          <button
            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className={`px-3 py-1 rounded text-sm ${
              currentPage === totalPages 
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            下一页
          </button>
        </div>
      </div>
    );
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-8 pb-5 border-b">
        <div className="flex items-center gap-4">
          <button 
            onClick={onBack}
            className="flex items-center gap-2 px-3 py-2 bg-gray-100 text-slate-700 rounded hover:bg-gray-200"
          >
            <ChevronLeftIcon className="w-4 h-4" />
            返回患者列表
          </button>
          <h1 className="text-2xl font-bold text-slate-800">{patient.name} - 通话历史</h1>
        </div>
      </div>

      {/* 患者基本信息卡片 */}
      <div className="bg-white rounded-lg p-6 shadow-sm mb-6">
        <h2 className="text-xl font-semibold mb-4">患者基本信息</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-5">
          <div>
            <label className="text-sm text-gray-500 block mb-1">患者编号</label>
            <p className="font-medium font-mono">{patient.patient_id || '未设置'}</p>
          </div>
          <div>
            <label className="text-sm text-gray-500 block mb-1">姓名</label>
            <p className="font-medium">{patient.name}</p>
          </div>
          <div>
            <label className="text-sm text-gray-500 block mb-1">性别</label>
            <p className="font-medium">{patient.gender}</p>
          </div>
          <div>
            <label className="text-sm text-gray-500 block mb-1">年龄</label>
            <p className="font-medium">{patient.age}岁</p>
          </div>
          <div>
            <label className="text-sm text-gray-500 block mb-1">手机号码</label>
            <p className="font-medium">{patient.phone}</p>
          </div>
          <div>
            <label className="text-sm text-gray-500 block mb-1">入组日期</label>
            <p className="font-medium">{patient.enrollment_date}</p>
          </div>
          <div>
            <label className="text-sm text-gray-500 block mb-1">当前天数</label>
            <p className="font-medium">{progress.currentDay || 0}/{progress.totalDays || 30}</p>
          </div>
          <div>
            <label className="text-sm text-gray-500 block mb-1">电话拨打天数</label>
            <p className="font-medium">{progress.totalCallDays || 0}天</p>
          </div>
          <div>
            <label className="text-sm text-gray-500 block mb-1">有训练天数</label>
            <p className="font-medium">{progress.totalTrainingDays || 0}天</p>
          </div>
          <div>
            <label className="text-sm text-gray-500 block mb-1">训练依从性</label>
            <p className="font-medium">
              {progress.compliance || 0}%
              <span className="text-xs text-gray-400 ml-1">
                ({progress.totalTrainingDays || 0}/{progress.totalCallDays || 0})
              </span>
            </p>
          </div>
          <div>
            <label className="text-sm text-gray-500 block mb-1">昨日训练次数</label>
            <p className="font-medium">{progress.todayTraining || 0}次</p>
          </div>
          <div>
            <label className="text-sm text-gray-500 block mb-1">平均训练次数</label>
            <p className="font-medium">{Math.round(progress.avgTraining || 0)}次</p>
          </div>
        </div>
      </div>

      {/* 通话记录表格 */}
      <div className="bg-white rounded-lg shadow-sm">
        <div className="p-5 border-b">
          <h2 className="text-xl font-semibold text-slate-800">通话记录</h2>
        </div>
        
        <div className="overflow-hidden">
          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
              <p className="mt-2 text-gray-600">加载中...</p>
            </div>
          ) : patientRecords.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              <p>暂无通话记录</p>
            </div>
          ) : (
            <div>
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="text-left py-3 px-4 font-semibold text-gray-600">通话时间</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-600">通话状态</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-600">训练数据</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-600">操作</th>
                  </tr>
                </thead>
                <tbody>
                  {patientRecords.map((record) => (
                    <React.Fragment key={record._id}>
                      <tr className="border-b hover:bg-gray-50">
                        <td className="py-4 px-4">
                          <div className="font-medium">{record['记录日期']}</div>
                          <div className="text-sm text-gray-600">{record['通话时间']}</div>
                        </td>
                        <td className="py-4 px-4">
                          <StatusBadge 
                            status={record['训练完成情况'] === '完成' ? '通话成功' : 
                                    record['训练完成情况'] === '未接通' ? '未接通' : 
                                    record['训练完成情况'] || '未知'} 
                            variant={record['训练完成情况'] === '完成' ? 'completed' : 
                                    record['训练完成情况'] === '未接通' ? 'pending' : 'inactive'} 
                          />
                        </td>
                        <td className="py-4 px-4">
                          <div className="flex flex-wrap gap-1">
                            {record['训练次数'] && (
                              <span className="px-2 py-1 bg-blue-100 text-blue-600 rounded text-xs">
                                训练{record['训练次数']}
                              </span>
                            )}
                            {record['训练时长'] && (
                              <span className="px-2 py-1 bg-purple-100 text-purple-600 rounded text-xs">
                                时长{record['训练时长']}
                              </span>
                            )}
                            {record['依从性'] && (
                              <span className="px-2 py-1 bg-indigo-100 text-indigo-600 rounded text-xs">
                                依从性{record['依从性']}
                              </span>
                            )}
                          </div>
                        </td>
                        <td className="py-4 px-4">
                          {record['对话历史记录'] && record['对话历史记录'].length > 0 && (
                            <button 
                              onClick={() => {
                                setExpandedDialogs(prev => ({
                                  ...prev,
                                  [record._id]: !prev[record._id]
                                }));
                              }}
                              className="flex items-center gap-1 text-blue-500 hover:text-blue-700"
                            >
                              <PlayIcon className="w-4 h-4" />
                              <span className="text-sm">
                                {expandedDialogs[record._id] ? '收起对话' : '查看对话'}
                              </span>
                              <ChevronDownIcon 
                                className={`w-4 h-4 transition-transform ${
                                  expandedDialogs[record._id] ? 'rotate-180' : ''
                                }`} 
                              />
                            </button>
                          )}
                        </td>
                      </tr>
                      
                      {/* 展开的对话历史 */}
                      {expandedDialogs[record._id] && record['对话历史记录'] && (
                        <tr>
                          <td colSpan={4} className="px-4 py-0">
                            <div className="bg-gray-50 rounded p-4 my-2">
                              <div className="flex justify-between items-center mb-3">
                                <h4 className="font-medium text-gray-800">对话详情</h4>
                                <button 
                                  onClick={() => {
                                    setExpandedDialogs(prev => ({
                                      ...prev,
                                      [record._id]: false
                                    }));
                                  }}
                                  className="text-gray-500 hover:text-gray-700"
                                >
                                  <XMarkIcon className="w-4 h-4" />
                                </button>
                              </div>
                              
                              <div className="space-y-2 max-h-80 overflow-y-auto bg-white p-3 rounded">
                                {record['对话历史记录'].map((chat, chatIndex) => (
                                  <div key={chatIndex} className={`flex ${chat.role === 'assistant' ? 'justify-start' : 'justify-end'}`}>
                                    <div className={`max-w-[80%] p-3 rounded-lg ${
                                      chat.role === 'assistant' 
                                        ? 'bg-blue-100 text-blue-900' 
                                        : 'bg-green-100 text-green-900'
                                    }`}>
                                      <div className="text-xs font-medium mb-1">
                                        {chat.role === 'assistant' ? '智能助手' : patient.name}
                                      </div>
                                      <div className="text-sm">{chat.content}</div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  ))}
                </tbody>
              </table>
              
              {/* 分页组件 */}
              {totalRecords > pageSize && <PaginationComponent />}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// 患者管理页面
const PatientsPage: React.FC<PatientsPageProps> = ({ 
  setShowAddPatientModal, 
  setSelectedPatient, 
  onViewPatientHistory, 
  onEditPatient,
  viewingPatientHistory = false,
  selectedPatient = null,
  onBack,
  refreshKey = 0
}) => {
  // 如果正在查看患者历史，渲染患者通话历史页面
  if (viewingPatientHistory && selectedPatient && onBack) {
    return <PatientCallHistoryPage patient={selectedPatient} onBack={onBack} />;
  }

  const [searchTerm, setSearchTerm] = useState('');
  const [patients, setPatients] = useState<Patient[]>([]);
  const [allPatients, setAllPatients] = useState<Patient[]>([]); // 存储所有患者用于分页
  const [progressData, setProgressData] = useState<Record<string, ProgressData>>({});
  const [loading, setLoading] = useState(true);
  
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [pageSize, setPageSize] = useState(10); // 每页显示10条记录
  
  // 批量编辑相关状态
  const [isBatchEditMode, setIsBatchEditMode] = useState(false);
  const [selectedPatients, setSelectedPatients] = useState<Set<string>>(new Set());
  const [showBatchStatusModal, setShowBatchStatusModal] = useState(false);
  
  // Toast状态管理
  const [toast, setToast] = useState({
    show: false,
    message: '',
    type: 'success' as 'success' | 'error' | 'warning'
  });

  const showToast = (message: string, type: 'success' | 'error' | 'warning' = 'success') => {
    setToast({ show: true, message, type });
  };

  const hideToast = () => {
    setToast(prev => ({ ...prev, show: false }));
  };

  // 获取患者数据
  const fetchPatients = async () => {
    console.log('🔄 开始获取患者数据，refreshKey:', refreshKey);
    try {
      setLoading(true);
      const [personnelData, progressDataResponse] = await Promise.all([
        personnelAPI.getList({ search: searchTerm, limit: 1000 }), // 获取更多数据用于分页
        progressAPI.get({ search: searchTerm })
      ]);
      
      setAllPatients(personnelData || []);
      setProgressData(progressDataResponse.progress || {});
      
      // 设置分页信息
      const totalCount = (personnelData || []).length;
      setTotalRecords(totalCount);
      const calculatedPages = Math.ceil(totalCount / pageSize);
      setTotalPages(Math.max(1, calculatedPages));
      
      // 重置到第一页
      setCurrentPage(1);
      console.log('✅ 患者数据获取完成，总数:', totalCount);
    } catch (error) {
      console.error('获取患者数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log('🔄 useEffect触发，searchTerm:', searchTerm, 'refreshKey:', refreshKey);
    fetchPatients();
  }, [searchTerm, refreshKey]); // 添加refreshKey作为依赖

  // 过滤患者
  const filteredPatients = allPatients.filter(patient => 
    !searchTerm || 
    patient.name.includes(searchTerm) || 
    patient.phone.includes(searchTerm)
  );

  // 计算当前页的患者数据
  const currentPagePatients = React.useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredPatients.slice(startIndex, endIndex);
  }, [filteredPatients, currentPage, pageSize]);

  // 当搜索条件改变时，重新计算分页
  React.useEffect(() => {
    setTotalRecords(filteredPatients.length);
    const calculatedPages = Math.ceil(filteredPatients.length / pageSize);
    setTotalPages(Math.max(1, calculatedPages));
    setCurrentPage(1); // 重置到第一页
    setSelectedPatients(new Set()); // 清空选择
  }, [searchTerm, filteredPatients.length, pageSize]);

  // 全选/取消全选（针对当前页）
  const handleSelectAll = () => {
    if (selectedPatients.size === currentPagePatients.length && currentPagePatients.length > 0) {
      // 如果当前页全选了，则取消全选
      const newSelected = new Set(selectedPatients);
      currentPagePatients.forEach(p => newSelected.delete(p._id));
      setSelectedPatients(newSelected);
    } else {
      // 选择当前页所有患者
      const newSelected = new Set(selectedPatients);
      currentPagePatients.forEach(p => newSelected.add(p._id));
      setSelectedPatients(newSelected);
    }
  };

  // 选择/取消选择单个患者
  const handleSelectPatient = (patientId: string) => {
    const newSelected = new Set(selectedPatients);
    if (newSelected.has(patientId)) {
      newSelected.delete(patientId);
    } else {
      newSelected.add(patientId);
    }
    setSelectedPatients(newSelected);
  };

  // 切换批量编辑模式
  const toggleBatchEditMode = () => {
    setIsBatchEditMode(!isBatchEditMode);
    setSelectedPatients(new Set());
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedPatients.size === 0) {
      showToast('请先选择要删除的患者', 'warning');
      return;
    }

    const confirmMessage = `确定要删除选中的 ${selectedPatients.size} 个患者吗？此操作不可撤销。`;
    if (!confirm(confirmMessage)) {
      return;
    }

    try {
      await personnelAPI.batchDelete(Array.from(selectedPatients));
      showToast(`成功删除 ${selectedPatients.size} 个患者`, 'success');
      setSelectedPatients(new Set());
      fetchPatients(); // 重新获取数据
    } catch (error) {
      console.error('批量删除失败:', error);
      showToast('删除失败，请重试', 'error');
    }
  };

  // 批量设置训练状态
  const handleBatchSetStatus = async (status: string) => {
    if (selectedPatients.size === 0) {
      showToast('请先选择要设置的患者', 'warning');
      return;
    }

    try {
      await personnelAPI.batchUpdateStatus(Array.from(selectedPatients), status);
      const message = status === '训练中' 
        ? `成功更新 ${selectedPatients.size} 个患者的训练状态为"训练中"。提醒：这些患者将被语音机器人每日自动随访。`
        : `成功更新 ${selectedPatients.size} 个患者的训练状态为"${status}"`;
      showToast(message, 'success');
      setSelectedPatients(new Set());
      fetchPatients(); // 重新获取数据
    } catch (error) {
      console.error('批量更新状态失败:', error);
      showToast('更新失败，请重试', 'error');
    }
  };

  // 单独更新患者训练状态
  const handleUpdatePatientStatus = async (patientId: string, status: string) => {
    try {
      await personnelAPI.update(patientId, { training_status: status });
      
      const message = status === '训练中' 
        ? `训练状态已更新为"训练中"。提醒：该患者将被语音机器人每日自动随访。`
        : `训练状态已更新为"${status}"`;
      showToast(message, 'success');
      
      fetchPatients(); // 重新获取数据
    } catch (error) {
      console.error('更新训练状态失败:', error);
      showToast('更新失败，请重试', 'error');
    }
  };

  // 处理分页点击
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      setCurrentPage(page);
    }
  };

  // 处理每页显示条数改变
  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // 重置到第一页
    
    // 重新计算总页数
    const calculatedPages = Math.ceil(filteredPatients.length / newPageSize);
    setTotalPages(Math.max(1, calculatedPages));
  };

  // 处理页数跳转
  const [jumpToPage, setJumpToPage] = useState('');
  
  const handleJumpToPage = () => {
    const pageNum = parseInt(jumpToPage);
    if (!isNaN(pageNum) && pageNum >= 1 && pageNum <= totalPages) {
      setCurrentPage(pageNum);
      setJumpToPage('');
    } else {
      alert(`请输入1-${totalPages}之间的页码`);
    }
  };

  const handleJumpInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleJumpToPage();
    }
  };

  // 生成页码按钮
  const renderPageButtons = () => {
    const buttons = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      buttons.push(
        <button
          key={i}
          onClick={() => handlePageChange(i)}
          className={`w-9 h-9 border rounded flex items-center justify-center ${
            i === currentPage 
              ? 'bg-blue-500 text-white border-blue-500' 
              : 'hover:bg-gray-50 border-gray-300'
          }`}
        >
          {i}
        </button>
      );
    }
    
    return buttons;
  };



  return (
    <div>
      <div className="flex justify-between items-center mb-8 pb-5 border-b">
        <h1 className="text-3xl font-bold text-slate-800">患者管理</h1>
        <div className="flex w-2/5">
          <input 
            type="text" 
            placeholder="搜索患者编号、姓名或电话..."
            className="flex-1 px-4 py-2 border rounded-l focus:outline-none focus:border-blue-500"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <button className="px-4 py-2 bg-blue-500 text-white rounded-r hover:bg-blue-600">
            <MagnifyingGlassCircleIcon className="w-5 h-5" />
          </button>
        </div>
      </div>

      <div className="bg-white rounded-lg p-5 shadow-sm">
        <div className="flex justify-between items-center mb-5">
          <h2 className="text-xl font-semibold text-slate-800">所有患者</h2>
          <div className="flex gap-3">
            <button className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-slate-700 rounded hover:bg-gray-200">
              <FunnelIcon className="w-4 h-4" />
              <span>筛选</span>
            </button>
            <button className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-slate-700 rounded hover:bg-gray-200">
              <ArrowDownTrayIcon className="w-4 h-4" />
              <span>导出</span>
            </button>
            <button 
              onClick={toggleBatchEditMode}
              className={`flex items-center gap-2 px-4 py-2 rounded ${
                isBatchEditMode 
                  ? 'bg-orange-100 text-orange-700 hover:bg-orange-200' 
                  : 'bg-gray-100 text-slate-700 hover:bg-gray-200'
              }`}
            >
              <PencilIcon className="w-4 h-4" />
              <span>{isBatchEditMode ? '退出批量编辑' : '批量编辑'}</span>
            </button>
            <button 
              onClick={() => setShowAddPatientModal(true)}
              className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              <PlusIcon className="w-4 h-4" />
              <span>添加患者</span>
            </button>
          </div>
        </div>

        {/* 结果统计信息 */}
        <div className="mb-4 flex justify-between items-center bg-gray-50 p-4 rounded">
          <div className="text-sm text-gray-600">
            共找到 {totalRecords} 位患者，当前第 {currentPage} 页，共 {totalPages} 页
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">每页显示：</span>
            <div className="flex gap-1">
              {[10, 20, 50].map((size) => (
                <button
                  key={size}
                  onClick={() => handlePageSizeChange(size)}
                  className={`px-3 py-1 text-sm border rounded ${
                    pageSize === size
                      ? 'bg-blue-500 text-white border-blue-500'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  {size}
                </button>
              ))}
            </div>
            <span className="text-sm text-gray-600">条</span>
          </div>
        </div>

        {/* 批量操作工具栏 */}
        {isBatchEditMode && (
          <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-4">
                <span className="text-sm font-medium text-blue-800">
                  已选择 {selectedPatients.size} 个患者
                </span>
                {selectedPatients.size > 0 && (
                  <div className="flex items-center gap-2 text-sm text-blue-700">
                    <ExclamationTriangleIcon className="w-4 h-4" />
                    <span>修改训练状态时，仅"训练中"的患者会被每日自动随访</span>
                  </div>
                )}
              </div>
              
              {selectedPatients.size > 0 && (
                <div className="flex gap-2">
                  <button 
                    onClick={handleBatchDelete}
                    className="flex items-center gap-1 px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200"
                  >
                    <TrashIcon className="w-4 h-4" />
                    <span>删除</span>
                  </button>
                  <button 
                    onClick={() => setShowBatchStatusModal(true)}
                    className="flex items-center gap-1 px-3 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200"
                  >
                    <CheckIcon className="w-4 h-4" />
                    <span>设置训练状态</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        <table className="w-full">
          <thead>
            <tr className="border-b">
              {isBatchEditMode && (
                <th className="text-left py-3 px-4 font-semibold text-gray-600">
                  <input 
                    type="checkbox"
                    checked={currentPagePatients.length > 0 && currentPagePatients.every(p => selectedPatients.has(p._id))}
                    onChange={handleSelectAll}
                    className="rounded border-gray-300"
                  />
                </th>
              )}
              <th className="text-left py-3 px-4 font-semibold text-gray-600">患者编号</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">患者信息</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">联系方式</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">入组日期</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">训练状态</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">参与状态</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">随访状态</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">训练依从性</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">操作</th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={isBatchEditMode ? 10 : 9} className="py-8 text-center text-gray-500">
                  加载中...
                </td>
              </tr>
            ) : currentPagePatients.length === 0 ? (
              <tr>
                <td colSpan={isBatchEditMode ? 10 : 9} className="py-8 text-center text-gray-500">
                  {searchTerm ? '没有找到匹配的患者' : '暂无患者数据'}
                </td>
              </tr>
            ) : (
              currentPagePatients.map((patient) => {
              const progress = progressData[patient._id] || {};
              const statusMap: Record<string, { label: string; variant: 'active' | 'pending' | 'inactive' | 'completed' }> = {
                '训练中': { label: '正常', variant: 'active' },
                '暂停': { label: '暂停', variant: 'pending' },
                '终止': { label: '已退出', variant: 'inactive' },
                '休息': { label: '休息中', variant: 'pending' }
              };
              const status = statusMap[patient.training_status] || { label: '未知', variant: 'inactive' };
              
              return (
                <tr key={patient._id} className="border-b hover:bg-gray-50">
                  {isBatchEditMode && (
                    <td className="py-3 px-4">
                      <input 
                        type="checkbox"
                        checked={selectedPatients.has(patient._id)}
                        onChange={() => handleSelectPatient(patient._id)}
                        className="rounded border-gray-300"
                      />
                    </td>
                  )}
                  <td className="py-3 px-4 font-mono text-sm font-medium">{patient.patient_id || '未设置'}</td>
                  <td className="py-3 px-4">{patient.name} ({patient.age}岁 {patient.gender})</td>
                  <td className="py-3 px-4">{patient.phone}</td>
                  <td className="py-3 px-4">{patient.enrollment_date}</td>
                  <td className="py-3 px-4">
                    {isBatchEditMode ? (
                      <select 
                        value={patient.training_status}
                        onChange={(e) => {
                          const newStatus = e.target.value;
                          if (newStatus === '训练中') {
                            const confirmed = confirm('设置为"训练中"状态后，该患者将被语音机器人每日自动随访。确认吗？');
                            if (confirmed) {
                              handleUpdatePatientStatus(patient._id, newStatus);
                            }
                          } else {
                            handleUpdatePatientStatus(patient._id, newStatus);
                          }
                        }}
                        className="px-2 py-1 border rounded text-sm"
                        onClick={(e) => e.stopPropagation()}
                      >
                        {TRAINING_STATUS_OPTIONS.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    ) : (
                      <StatusBadge 
                        status={patient.training_status} 
                        variant={
                          patient.training_status === '训练中' ? 'active' :
                          patient.training_status === '暂停' ? 'pending' :
                          patient.training_status === '休息' ? 'pending' : 'inactive'
                        } 
                      />
                    )}
                  </td>
                  <td className="py-3 px-4">
                    <StatusBadge status={status.label} variant={status.variant} />
                  </td>
                  <td className="py-3 px-4">第{progress.currentDay || 0}天</td>
                  <td className="py-3 px-4">{progress.compliance || 0}%</td>
                  <td className="py-3 px-4">
                    <button 
                      onClick={() => {
                        setSelectedPatient(patient);
                        onViewPatientHistory(patient);
                      }}
                      className="text-gray-500 hover:text-blue-500 mr-2"
                    >
                      <EyeIcon className="w-5 h-5" />
                    </button>
                    {/* <button className="text-gray-500 hover:text-blue-500 mr-2">
                      <PhoneIcon className="w-5 h-5" />
                    </button> */}
                    <button 
                      onClick={() => onEditPatient(patient)}
                      className="text-gray-500 hover:text-blue-500"
                    >
                      <PencilIcon className="w-5 h-5" />
                    </button>
                  </td>
                </tr>
              );
            })
            )}
          </tbody>
        </table>

        {/* 分页控件 */}
        <div className="flex justify-between items-center mt-5 p-4 bg-gray-50 rounded-lg border">
          {/* 左侧页数跳转 */}
          <div className="flex items-center gap-2">
            {totalPages > 1 ? (
              <>
                <span className="text-sm text-gray-600">跳转到</span>
                <input
                  type="number"
                  min="1"
                  max={totalPages}
                  value={jumpToPage}
                  onChange={(e) => setJumpToPage(e.target.value)}
                  onKeyPress={handleJumpInputKeyPress}
                  placeholder="页码"
                  className="w-16 px-2 py-1 text-sm border rounded text-center"
                />
                <span className="text-sm text-gray-600">页</span>
                <button 
                  onClick={handleJumpToPage}
                  className="px-3 py-1 text-sm bg-blue-500 text-white border border-blue-500 rounded hover:bg-blue-600"
                >
                  跳转
                </button>
              </>
            ) : (
              <span className="text-sm text-gray-600">第 {currentPage} / {totalPages} 页</span>
            )}
          </div>
          
          {/* 中间导航按钮 - 始终显示 */}
          <div className="flex items-center gap-2 flex-wrap">
            {/* 首页按钮 */}
            <button 
              onClick={() => handlePageChange(1)}
              disabled={currentPage === 1 || totalPages <= 1}
              className={`px-3 py-2 border rounded text-sm ${
                currentPage === 1 || totalPages <= 1
                  ? 'text-gray-400 border-gray-200 cursor-not-allowed bg-gray-100' 
                  : 'hover:bg-gray-50 border-gray-300 text-gray-700 bg-white'
              }`}
            >
              首页
            </button>
            
            {/* 上一页按钮 */}
            <button 
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1 || totalPages <= 1}
              className={`w-9 h-9 border rounded flex items-center justify-center ${
                currentPage === 1 || totalPages <= 1
                  ? 'text-gray-400 border-gray-200 cursor-not-allowed bg-gray-100' 
                  : 'hover:bg-gray-50 border-gray-300 bg-white'
              }`}
            >
              <ChevronLeftIcon className="w-4 h-4" />
            </button>
            
            {/* 页码按钮 */}
            {totalPages > 1 ? renderPageButtons() : (
              <button className="w-9 h-9 border rounded flex items-center justify-center bg-blue-500 text-white border-blue-500">
                {currentPage}
              </button>
            )}
            
            {/* 下一页按钮 */}
            <button 
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages || totalPages <= 1}
              className={`w-9 h-9 border rounded flex items-center justify-center ${
                currentPage === totalPages || totalPages <= 1
                  ? 'text-gray-400 border-gray-200 cursor-not-allowed bg-gray-100' 
                  : 'hover:bg-gray-50 border-gray-300 bg-white'
              }`}
            >
              <ChevronRightIcon className="w-4 h-4" />
            </button>
            
            {/* 尾页按钮 */}
            <button 
              onClick={() => handlePageChange(totalPages)}
              disabled={currentPage === totalPages || totalPages <= 1}
              className={`px-3 py-2 border rounded text-sm ${
                currentPage === totalPages || totalPages <= 1
                  ? 'text-gray-400 border-gray-200 cursor-not-allowed bg-gray-100' 
                  : 'hover:bg-gray-50 border-gray-300 text-gray-700 bg-white'
              }`}
            >
              尾页
            </button>
          </div>
          
          {/* 右侧分页状态 */}
          <div className="text-sm text-gray-600">
            共 {totalRecords} 位患者
          </div>
        </div>

        {/* 调试信息 - 可通过环境变量控制显示 */}
        {/* {process.env.NODE_ENV === 'development' && (
          <div className="mt-2 p-2 bg-gray-100 text-xs text-gray-600 rounded">
            调试信息: 总患者数={totalRecords}, 当前页={currentPage}, 总页数={totalPages}, 每页条数={pageSize}, 当前页患者数={currentPagePatients.length}
          </div>
        )} */}
      </div>

      {/* 批量状态设置模态框 */}
      <BatchStatusModal 
        isOpen={showBatchStatusModal}
        onClose={() => setShowBatchStatusModal(false)}
        selectedCount={selectedPatients.size}
        onConfirm={handleBatchSetStatus}
      />

      {/* Toast通知 */}
      <Toast 
        message={toast.message}
        type={toast.type}
        show={toast.show}
        onClose={hideToast}
      />
    </div>
  );
};

// 导出组件
export default PatientsPage;
export { PatientCallHistoryPage }; 