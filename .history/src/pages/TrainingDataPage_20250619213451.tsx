import React, { useState, useEffect } from 'react';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { 
  MagnifyingGlassCircleIcon,
  ArrowDownTrayIcon,
  FunnelIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';
import { callRecordsAPI, personnelAPI } from '../services/api';
import { CallRecord, TrainingStats } from '../types';
import StatusBadge from '../components/StatusBadge';

const TrainingDataPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [timeRange, setTimeRange] = useState('最近7天');
  const [customStartDate, setCustomStartDate] = useState('');
  const [customEndDate, setCustomEndDate] = useState('');
  const [trainingStats, setTrainingStats] = useState<TrainingStats>({
    completedCount: 0,
    totalCount: 0,
    avgTrainingCount: 0,
    minTrainingCount: 0,
    maxTrainingCount: 0,
    discomfortCount: 0,
    deviceIssueCount: 0,
    doctorInterventionCount: 0
  });
  const [trainingRecords, setTrainingRecords] = useState<CallRecord[]>([]);
  const [loading, setLoading] = useState(true);

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [pageSize, setPageSize] = useState(10); // 每页显示10条记录
  const [allRecords, setAllRecords] = useState<CallRecord[]>([]); // 存储所有记录用于客户端分页

  // 获取训练数据
  const fetchTrainingData = async () => {
    try {
      setLoading(true);
      
      // 计算查询的日期范围
      let startDate: string, endDate: string;
      const today = new Date().toISOString().split('T')[0];
      
      if (timeRange === '自定义范围') {
        startDate = customStartDate;
        endDate = customEndDate;
      } else {
        endDate = today;
        const days = timeRange === '最近1天' ? 1 : timeRange === '最近7天' ? 7 : 30;
        const start = new Date();
        start.setDate(start.getDate() - days + 1);
        startDate = start.toISOString().split('T')[0];
      }

      // 获取指定时间范围内的通话记录
      const callRecordsResp = await callRecordsAPI.getList({
        start_date: startDate,
        end_date: endDate,
        limit: 1000
      });

      // 获取患者列表用于统计总人数
      const personnelData = await personnelAPI.getList({ limit: 1000 });
      const activePersonnel = (personnelData || []).filter((p: any) => p.training_status === '训练中');

      // 处理训练数据统计
      const records = callRecordsResp?.data || [];
      const trainingData = records.filter((r: any) => r['训练次数'] && r['训练次数'] !== '--');
      
      // 统计训练次数
      const trainingCounts = trainingData.map((r: any) => parseInt(r['训练次数']) || 0).filter((count: number) => count > 0);
      
      // 统计各种指标
      const completedCount = records.filter((r: any) => r['训练完成情况'] === '完成').length;
      const discomfortCount = records.filter((r: any) => r['是否有不适感'] === '是').length;
      const deviceIssueCount = records.filter((r: any) => r['锻炼辅助仪器是否有问题'] === '是').length;
      const doctorInterventionCount = records.filter((r: any) => r['是否需要医生人工和患者联系'] === '是').length;

      setTrainingStats({
        completedCount,
        totalCount: activePersonnel.length,
        avgTrainingCount: trainingCounts.length > 0 ? Math.round(trainingCounts.reduce((a: number, b: number) => a + b, 0) / trainingCounts.length) : 0,
        minTrainingCount: trainingCounts.length > 0 ? Math.min(...trainingCounts) : 0,
        maxTrainingCount: trainingCounts.length > 0 ? Math.max(...trainingCounts) : 0,
        discomfortCount,
        deviceIssueCount,
        doctorInterventionCount
      });

      // 处理详细记录数据，按搜索词筛选
      const filteredRecords = records.filter((record: any) => 
        !searchTerm || 
        (record['患者名字'] && record['患者名字'].includes(searchTerm))
      ).sort((a: any, b: any) => (b['记录日期'] || '').localeCompare(a['记录日期'] || ''));

      // 存储所有记录并设置分页信息
      setAllRecords(filteredRecords);
      setTotalRecords(filteredRecords.length);
      const calculatedPages = Math.ceil(filteredRecords.length / pageSize);
      setTotalPages(Math.max(1, calculatedPages));
      
      // 重置到第一页
      setCurrentPage(1);
    } catch (error) {
      console.error('获取训练数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTrainingData();
  }, [timeRange, customStartDate, customEndDate, searchTerm]);

  // 计算当前页的记录
  useEffect(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    setTrainingRecords(allRecords.slice(startIndex, endIndex));
  }, [allRecords, currentPage, pageSize]);

  // 当搜索条件或页面大小改变时，重置到第一页
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, timeRange, pageSize]);

  // 处理分页点击
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      setCurrentPage(page);
    }
  };

  // 处理每页显示条数改变
  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // 重置到第一页
    
    // 重新计算总页数
    const calculatedPages = Math.ceil(totalRecords / newPageSize);
    setTotalPages(Math.max(1, calculatedPages));
  };

  // 处理页数跳转
  const [jumpToPage, setJumpToPage] = useState('');
  
  const handleJumpToPage = () => {
    const pageNum = parseInt(jumpToPage);
    if (!isNaN(pageNum) && pageNum >= 1 && pageNum <= totalPages) {
      setCurrentPage(pageNum);
      setJumpToPage('');
    } else {
      alert(`请输入1-${totalPages}之间的页码`);
    }
  };

  const handleJumpInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleJumpToPage();
    }
  };

  // 生成页码按钮
  const renderPageButtons = () => {
    const buttons = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      buttons.push(
        <button
          key={i}
          onClick={() => handlePageChange(i)}
          className={`w-9 h-9 border rounded flex items-center justify-center ${
            i === currentPage 
              ? 'bg-blue-500 text-white border-blue-500' 
              : 'hover:bg-gray-50 border-gray-300'
          }`}
        >
          {i}
        </button>
      );
    }
    
    return buttons;
  };

  // 导出Excel表格
  const exportToExcel = () => {
    // 准备导出的数据 - 使用所有记录而不仅仅是当前页
    const exportData = allRecords.map((record, index) => ({
      '序号': index + 1,
      '患者姓名': record['患者名字'] || '--',
      '日期': record['记录日期'] || '--',
      '训练次数': record['训练次数'] || '--',
      '训练时长': record['训练时长'] || '--',
      '不适感状况': record['是否有不适感'] === '是' ? (record['不适感内容'] || '有不适') : 
                   record['是否有不适感'] === '否' ? '无不适' : '--',
      '设备状态': record['锻炼辅助仪器是否有问题'] === '是' ? (record['锻炼辅助仪器问题内容'] || '有问题') :
                  record['锻炼辅助仪器是否有问题'] === '否' ? '正常' : '--',
      '医生干预': record['是否需要医生人工和患者联系'] === '是' ? '需要干预' :
                  record['是否需要医生人工和患者联系'] === '否' ? '无需干预' : '--',
      '训练状态': record['训练完成情况'] === '完成' ? '已完成' : 
                  record['训练完成情况'] === '未接通' ? '未接通' : 
                  record['训练完成情况'] || '未知',
      '手机号码': record['手机号'] || '--',
      '记录时间': record['通话时间'] || '--'
    }));

    // 创建工作簿
    const ws = XLSX.utils.json_to_sheet(exportData);
    const wb = XLSX.utils.book_new();
    
    // 设置列宽
    const colWidths = [
      { wch: 8 },   // 序号
      { wch: 12 },  // 患者姓名
      { wch: 12 },  // 日期
      { wch: 10 },  // 训练次数
      { wch: 10 },  // 训练时长
      { wch: 15 },  // 不适感状况
      { wch: 15 },  // 设备状态
      { wch: 12 },  // 医生干预
      { wch: 12 },  // 训练状态
      { wch: 15 },  // 手机号码
      { wch: 15 }   // 记录时间
    ];
    ws['!cols'] = colWidths;
    
    XLSX.utils.book_append_sheet(wb, ws, '训练数据');
    
    // 生成文件名（包含当前时间和时间范围）
    const now = new Date();
    const timestamp = now.toLocaleDateString('zh-CN').replace(/\//g, '-');
    const timeRangeText = timeRange === '自定义范围' ? 
      `${customStartDate}_${customEndDate}` : 
      timeRange.replace('最近', '');
    const fileName = `训练数据分析_${timeRangeText}_${timestamp}.xlsx`;
    
    // 导出文件
    const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
    const data = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    saveAs(data, fileName);
  };

  // 导出统计数据
  const exportStatsToExcel = () => {
    // 准备统计数据
    const statsData = [
      {
        '统计项目': '训练完成率',
        '数值': `${trainingStats.completedCount}/${trainingStats.totalCount}`,
        '百分比': trainingStats.totalCount > 0 ? `${Math.round((trainingStats.completedCount / trainingStats.totalCount) * 100)}%` : '0%',
        '备注': '已完成训练人数/总人数'
      },
      {
        '统计项目': '平均训练次数',
        '数值': trainingStats.avgTrainingCount,
        '百分比': '',
        '备注': '次/天'
      },
      {
        '统计项目': '训练次数范围',
        '数值': `${trainingStats.minTrainingCount}-${trainingStats.maxTrainingCount}`,
        '百分比': '',
        '备注': '最低-最高'
      },
      {
        '统计项目': '有不适感人数',
        '数值': trainingStats.discomfortCount,
        '百分比': '',
        '备注': '人次'
      },
      {
        '统计项目': '设备异常人数',
        '数值': trainingStats.deviceIssueCount,
        '百分比': '',
        '备注': '人次'
      },
      {
        '统计项目': '需要医生干预',
        '数值': trainingStats.doctorInterventionCount,
        '百分比': '',
        '备注': '人次'
      }
    ];

    // 创建工作簿
    const ws1 = XLSX.utils.json_to_sheet(statsData);
    const ws2 = XLSX.utils.json_to_sheet(allRecords.map((record, index) => ({
      '序号': index + 1,
      '患者姓名': record['患者名字'] || '--',
      '日期': record['记录日期'] || '--',
      '训练次数': record['训练次数'] || '--',
      '训练时长': record['训练时长'] || '--',
      '不适感状况': record['是否有不适感'] === '是' ? (record['不适感内容'] || '有不适') : 
                   record['是否有不适感'] === '否' ? '无不适' : '--',
      '设备状态': record['锻炼辅助仪器是否有问题'] === '是' ? (record['锻炼辅助仪器问题内容'] || '有问题') :
                  record['锻炼辅助仪器是否有问题'] === '否' ? '正常' : '--',
      '医生干预': record['是否需要医生人工和患者联系'] === '是' ? '需要干预' :
                  record['是否需要医生人工和患者联系'] === '否' ? '无需干预' : '--',
      '训练状态': record['训练完成情况'] === '完成' ? '已完成' : 
                  record['训练完成情况'] === '未接通' ? '未接通' : 
                  record['训练完成情况'] || '未知'
    })));
    
    const wb = XLSX.utils.book_new();
    
    // 设置列宽
    ws1['!cols'] = [{ wch: 15 }, { wch: 20 }, { wch: 10 }, { wch: 20 }];
    ws2['!cols'] = [
      { wch: 8 }, { wch: 12 }, { wch: 12 }, { wch: 10 }, { wch: 10 },
      { wch: 15 }, { wch: 15 }, { wch: 12 }, { wch: 12 }
    ];
    
    XLSX.utils.book_append_sheet(wb, ws1, '统计汇总');
    XLSX.utils.book_append_sheet(wb, ws2, '详细数据');
    
    // 生成文件名
    const now = new Date();
    const timestamp = now.toLocaleDateString('zh-CN').replace(/\//g, '-');
    const timeRangeText = timeRange === '自定义范围' ? 
      `${customStartDate}_${customEndDate}` : 
      timeRange.replace('最近', '');
    const fileName = `训练数据统计报告_${timeRangeText}_${timestamp}.xlsx`;
    
    // 导出文件
    const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
    const data = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    saveAs(data, fileName);
  };

  const statsCards = [
    {
      title: '训练完成率',
      value: `${trainingStats.completedCount}/${trainingStats.totalCount}`,
      percentage: trainingStats.totalCount > 0 ? Math.round((trainingStats.completedCount / trainingStats.totalCount) * 100) : 0,
      color: 'bg-blue-500'
    },
    {
      title: '平均训练次数',
      value: trainingStats.avgTrainingCount,
      subtitle: '次/天',
      color: 'bg-green-500'
    },
    {
      title: '训练次数范围',
      value: `${trainingStats.minTrainingCount}-${trainingStats.maxTrainingCount}`,
      subtitle: '最低-最高',
      color: 'bg-purple-500'
    },
    {
      title: '有不适感人数',
      value: trainingStats.discomfortCount,
      subtitle: '人次',
      color: 'bg-yellow-500'
    },
    {
      title: '设备异常人数',
      value: trainingStats.deviceIssueCount,
      subtitle: '人次',
      color: 'bg-red-500'
    },
    {
      title: '需要医生干预',
      value: trainingStats.doctorInterventionCount,
      subtitle: '人次',
      color: 'bg-orange-500'
    }
  ];

  return (
    <div>
      <div className="flex justify-between items-center mb-8 pb-5 border-b">
        <h1 className="text-3xl font-bold text-slate-800">训练数据分析</h1>
        <div className="flex w-2/5">
          <input 
            type="text" 
            placeholder="搜索患者姓名..." 
            className="flex-1 px-4 py-2 border rounded-l focus:outline-none focus:border-blue-500"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <button className="px-4 py-2 bg-blue-500 text-white rounded-r hover:bg-blue-600">
            <MagnifyingGlassCircleIcon className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* 时间范围选择器 */}
      <div className="bg-white rounded-lg p-5 shadow-sm mb-6">
        <div className="flex flex-wrap items-center gap-4">
          <label className="font-medium text-gray-700">时间范围：</label>
          <div className="flex gap-2">
            {['最近1天', '最近7天', '最近30天', '自定义范围'].map(option => (
              <button
                key={option}
                onClick={() => setTimeRange(option)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  timeRange === option
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {option}
              </button>
            ))}
          </div>
          
          {timeRange === '自定义范围' && (
            <div className="flex items-center gap-2 ml-4">
              <input
                type="date"
                value={customStartDate}
                onChange={(e) => setCustomStartDate(e.target.value)}
                className="px-3 py-2 border rounded"
              />
              <span>至</span>
              <input
                type="date"
                value={customEndDate}
                onChange={(e) => setCustomEndDate(e.target.value)}
                className="px-3 py-2 border rounded"
              />
            </div>
          )}
          
          <div className="ml-auto flex gap-2">
            <button 
              onClick={exportStatsToExcel}
              className="flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
            >
              <ArrowDownTrayIcon className="w-4 h-4" />
              导出统计报告
            </button>
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-5 mb-8">
        {statsCards.map((card, index) => (
          <div key={index} className="bg-white rounded-lg p-5 shadow-sm">
            <div className={`w-12 h-12 ${card.color} rounded-lg flex items-center justify-center mb-4`}>
              <div className="w-6 h-6 bg-white rounded opacity-90"></div>
            </div>
            <h3 className="text-gray-600 text-sm mb-2">{card.title}</h3>
            <div className="text-2xl font-bold text-slate-800 mb-1">
              {card.value}
              {card.percentage !== undefined && (
                <span className="text-lg text-blue-500 ml-1">({card.percentage}%)</span>
              )}
            </div>
            {card.subtitle && (
              <p className="text-sm text-gray-500">{card.subtitle}</p>
            )}
          </div>
        ))}
      </div>

      {/* 详细数据表格 */}
      <div className="bg-white rounded-lg shadow-sm">
        <div className="p-5 border-b">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">训练记录详情</h2>
            <div className="flex gap-3">
              <button className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-slate-700 rounded hover:bg-gray-200">
                <FunnelIcon className="w-4 h-4" />
                筛选
              </button>
              <button 
                onClick={exportToExcel}
                className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                <ArrowDownTrayIcon className="w-4 h-4" />
                导出表格
              </button>
            </div>
          </div>
        </div>

        {/* 结果统计信息 */}
        <div className="p-4 flex justify-between items-center bg-gray-50">
          <div className="text-sm text-gray-600">
            共找到 {totalRecords} 条记录，当前第 {currentPage} 页，共 {totalPages} 页
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">每页显示：</span>
            <div className="flex gap-1">
              {[10, 20, 50].map((size) => (
                <button
                  key={size}
                  onClick={() => handlePageSizeChange(size)}
                  className={`px-3 py-1 text-sm border rounded ${
                    pageSize === size
                      ? 'bg-blue-500 text-white border-blue-500'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  {size}
                </button>
              ))}
            </div>
            <span className="text-sm text-gray-600">条</span>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="text-left py-3 px-4 font-semibold text-gray-600">患者编号</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-600">患者姓名</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-600">日期</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-600">训练次数</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-600">训练时长</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-600">不适感</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-600">设备状态</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-600">医生干预</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-600">训练状态</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan={9} className="py-8 text-center text-gray-500">
                    加载中...
                  </td>
                </tr>
              ) : trainingRecords.length === 0 ? (
                <tr>
                  <td colSpan={9} className="py-8 text-center text-gray-500">
                    暂无训练数据
                  </td>
                </tr>
              ) : (
                trainingRecords.map((record, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="py-3 px-4 font-mono text-sm font-medium">{record['患者编号'] || '--'}</td>
                    <td className="py-3 px-4 font-medium">{record['患者名字'] || '--'}</td>
                    <td className="py-3 px-4">{record['记录日期'] || '--'}</td>
                    <td className="py-3 px-4">
                      {record['训练次数'] ? (
                        <span className="px-2 py-1 bg-blue-100 text-blue-600 rounded text-sm font-medium">
                          {record['训练次数']} 
                        </span>
                      ) : '--'}
                    </td>
                    <td className="py-3 px-4">{record['训练时长'] || '--'}</td>
                    <td className="py-3 px-4">
                      {record['是否有不适感'] === '是' ? (
                        <span className="px-2 py-1 bg-red-100 text-red-600 rounded text-sm">
                          {record['不适感内容'] || '有不适'}
                        </span>
                      ) : record['是否有不适感'] === '否' ? (
                        <span className="px-2 py-1 bg-green-100 text-green-600 rounded text-sm">
                          无不适
                        </span>
                      ) : '--'}
                    </td>
                    <td className="py-3 px-4">
                      {record['锻炼辅助仪器是否有问题'] === '是' ? (
                        <span className="px-2 py-1 bg-red-100 text-red-600 rounded text-sm">
                          {record['锻炼辅助仪器问题内容'] || '有问题'}
                        </span>
                      ) : record['锻炼辅助仪器是否有问题'] === '否' ? (
                        <span className="px-2 py-1 bg-green-100 text-green-600 rounded text-sm">
                          正常
                        </span>
                      ) : '--'}
                    </td>
                    <td className="py-3 px-4">
                      {record['是否需要医生人工和患者联系'] === '是' ? (
                        <span className="px-2 py-1 bg-orange-100 text-orange-600 rounded text-sm">
                          需要干预
                        </span>
                      ) : record['是否需要医生人工和患者联系'] === '否' ? (
                        <span className="px-2 py-1 bg-green-100 text-green-600 rounded text-sm">
                          无需干预
                        </span>
                      ) : '--'}
                    </td>
                    <td className="py-3 px-4">
                      <StatusBadge 
                        status={record['训练完成情况'] === '完成' ? '已完成' : 
                                record['训练完成情况'] === '未接通' ? '未接通' : 
                                record['训练完成情况'] || '未知'} 
                        variant={record['训练完成情况'] === '完成' ? 'completed' : 
                                record['训练完成情况'] === '未接通' ? 'pending' : 'inactive'} 
                      />
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* 分页控件 */}
        <div className="flex justify-between items-center mt-5 p-4 bg-gray-50 rounded-lg border">
          {/* 左侧页数跳转 */}
          <div className="flex items-center gap-2">
            {totalPages > 1 ? (
              <>
                <span className="text-sm text-gray-600">跳转到</span>
                <input
                  type="number"
                  min="1"
                  max={totalPages}
                  value={jumpToPage}
                  onChange={(e) => setJumpToPage(e.target.value)}
                  onKeyPress={handleJumpInputKeyPress}
                  placeholder="页码"
                  className="w-16 px-2 py-1 text-sm border rounded text-center"
                />
                <span className="text-sm text-gray-600">页</span>
                <button 
                  onClick={handleJumpToPage}
                  className="px-3 py-1 text-sm bg-blue-500 text-white border border-blue-500 rounded hover:bg-blue-600"
                >
                  跳转
                </button>
              </>
            ) : (
              <span className="text-sm text-gray-600">第 {currentPage} / {totalPages} 页</span>
            )}
          </div>
          
          {/* 中间导航按钮 - 始终显示 */}
          <div className="flex items-center gap-2 flex-wrap">
            {/* 首页按钮 */}
            <button 
              onClick={() => handlePageChange(1)}
              disabled={currentPage === 1 || totalPages <= 1}
              className={`px-3 py-2 border rounded text-sm ${
                currentPage === 1 || totalPages <= 1
                  ? 'text-gray-400 border-gray-200 cursor-not-allowed bg-gray-100' 
                  : 'hover:bg-gray-50 border-gray-300 text-gray-700 bg-white'
              }`}
            >
              首页
            </button>
            
            {/* 上一页按钮 */}
            <button 
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1 || totalPages <= 1}
              className={`w-9 h-9 border rounded flex items-center justify-center ${
                currentPage === 1 || totalPages <= 1
                  ? 'text-gray-400 border-gray-200 cursor-not-allowed bg-gray-100' 
                  : 'hover:bg-gray-50 border-gray-300 bg-white'
              }`}
            >
              <ChevronLeftIcon className="w-4 h-4" />
            </button>
            
            {/* 页码按钮 */}
            {totalPages > 1 ? renderPageButtons() : (
              <button className="w-9 h-9 border rounded flex items-center justify-center bg-blue-500 text-white border-blue-500">
                {currentPage}
              </button>
            )}
            
            {/* 下一页按钮 */}
            <button 
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages || totalPages <= 1}
              className={`w-9 h-9 border rounded flex items-center justify-center ${
                currentPage === totalPages || totalPages <= 1
                  ? 'text-gray-400 border-gray-200 cursor-not-allowed bg-gray-100' 
                  : 'hover:bg-gray-50 border-gray-300 bg-white'
              }`}
            >
              <ChevronRightIcon className="w-4 h-4" />
            </button>
            
            {/* 尾页按钮 */}
            <button 
              onClick={() => handlePageChange(totalPages)}
              disabled={currentPage === totalPages || totalPages <= 1}
              className={`px-3 py-2 border rounded text-sm ${
                currentPage === totalPages || totalPages <= 1
                  ? 'text-gray-400 border-gray-200 cursor-not-allowed bg-gray-100' 
                  : 'hover:bg-gray-50 border-gray-300 text-gray-700 bg-white'
              }`}
            >
              尾页
            </button>
          </div>
          
          {/* 右侧分页状态 */}
          <div className="text-sm text-gray-600">
            共 {totalRecords} 条记录
          </div>
        </div>

        {/* 调试信息 - 可通过环境变量控制显示 */}
        {/* {process.env.NODE_ENV === 'development' && (
          <div className="mt-2 p-2 bg-gray-100 text-xs text-gray-600 rounded">
            调试信息: 总记录数={totalRecords}, 当前页={currentPage}, 总页数={totalPages}, 每页条数={pageSize}, 记录数组长度={trainingRecords.length}
          </div>
        )} */}
      </div>
    </div>
  );
};

export default TrainingDataPage; 