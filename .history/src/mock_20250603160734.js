// mockData.js
export const mockPersonnel = [
  {
    _id: "65a1b2c3d4e5f6789abcdef0",
    name: "张丽珍",
    phone: "13912345678",
    age: 76,
    gender: "女",
    enrollment_date: "2025-02-15",
    training_status: "训练中",
    created_at: "2025-02-15T08:00:00Z",
    updated_at: "2025-02-28T10:30:00Z"
  },
  {
    _id: "65a1b2c3d4e5f6789abcdef1",
    name: "李大明",
    phone: "13512341234",
    age: 68,
    gender: "男",
    enrollment_date: "2025-02-18",
    training_status: "训练中",
    created_at: "2025-02-18T09:00:00Z",
    updated_at: "2025-02-28T10:30:00Z"
  },
  {
    _id: "65a1b2c3d4e5f6789abcdef2",
    name: "王建国",
    phone: "13698769876",
    age: 71,
    gender: "男",
    enrollment_date: "2025-02-10",
    training_status: "训练中",
    created_at: "2025-02-10T10:00:00Z",
    updated_at: "2025-02-28T10:30:00Z"
  },
  {
    _id: "65a1b2c3d4e5f6789abcdef3",
    name: "赵淑华",
    phone: "13843214321",
    age: 73,
    gender: "女",
    enrollment_date: "2025-02-12",
    training_status: "训练中",
    created_at: "2025-02-12T11:00:00Z",
    updated_at: "2025-02-28T10:30:00Z"
  },
  {
    _id: "65a1b2c3d4e5f6789abcdef4",
    name: "刘海涛",
    phone: "13312346789",
    age: 65,
    gender: "男",
    enrollment_date: "2025-02-20",
    training_status: "终止",
    created_at: "2025-02-20T14:00:00Z",
    updated_at: "2025-02-27T10:30:00Z"
  },
  {
    _id: "65a1b2c3d4e5f6789abcdef5",
    name: "陈明芳",
    phone: "13712342468",
    age: 70,
    gender: "女",
    enrollment_date: "2025-01-29",
    training_status: "训练中",
    created_at: "2025-01-29T15:00:00Z",
    updated_at: "2025-02-28T10:30:00Z"
  },
  {
    _id: "65a1b2c3d4e5f6789abcdef6",
    name: "吴长福",
    phone: "13924681357",
    age: 74,
    gender: "男",
    enrollment_date: "2025-02-05",
    training_status: "暂停",
    created_at: "2025-02-05T16:00:00Z",
    updated_at: "2025-02-25T10:30:00Z"
  }
];

export const mockCallRecords = [
  {
    _id: "record001",
    患者名字: "张丽珍",
    手机号: "13912345678",
    通话时间: "2025-02-28 09:15",
    记录日期: "2025-02-28",
    对话历史记录: [
      { role: "assistant", content: "您好，张阿姨，我是您的康复随访助手，今天打电话是想了解一下您昨天膝关节康复器的使用情况，请问您昨天做摆腿运动了吗？" },
      { role: "user", content: "做了做了，我昨天按照要求做了。" },
      { role: "assistant", content: "太好了，请问您昨天一共做了多少次摆腿运动呢？" },
      { role: "user", content: "我做了35次，都记着呢。" },
      { role: "assistant", content: "做得非常棒，张阿姨！请问您在做摆腿运动的过程中有没有感到不适或者疼痛？" },
      { role: "user", content: "没有不舒服，反而感觉比前几天好多了，膝盖不那么僵硬了。" }
    ],
    训练完成情况: "完成",
    训练次数: "35次",
    训练时长: "50分钟",
    是否有不适感: "否",
    不适感内容: "",
    是否需要医生人工和患者联系: "否",
    锻炼辅助仪器是否有问题: "否",
    锻炼辅助仪器问题内容: "",
    创建时间: "2025-02-28T09:15:00Z"
  },
  {
    _id: "record002",
    患者名字: "李大明",
    手机号: "13512341234",
    通话时间: "2025-02-28 10:23",
    记录日期: "2025-02-28",
    对话历史记录: [
      { role: "assistant", content: "您好，李先生，我是您的康复随访助手，今天打电话是想了解一下您昨天膝关节康复器的使用情况，请问您昨天做摆腿运动了吗？" },
      { role: "user", content: "做了，不过没做多少次。" },
      { role: "assistant", content: "请问您昨天大概做了多少次摆腿运动呢？" },
      { role: "user", content: "大概28次吧，昨天腿有点酸，所以没多做。" }
    ],
    训练完成情况: "完成",
    训练次数: "28次",
    训练时长: "40分钟",
    是否有不适感: "是",
    不适感内容: "肌肉酸痛",
    是否需要医生人工和患者联系: "否",
    锻炼辅助仪器是否有问题: "否",
    锻炼辅助仪器问题内容: "",
    创建时间: "2025-02-28T10:23:00Z"
  },
  {
    _id: "record003",
    患者名字: "王建国",
    手机号: "13698769876",
    通话时间: "2025-02-28 11:05",
    记录日期: "2025-02-28",
    训练完成情况: "未接通",
    创建时间: "2025-02-28T11:05:00Z"
  },
  {
    _id: "record004",
    患者名字: "赵淑华",
    手机号: "13843214321",
    通话时间: "2025-02-27 09:30",
    记录日期: "2025-02-27",
    训练完成情况: "完成",
    训练次数: "32次",
    训练时长: "45分钟",
    是否有不适感: "否",
    是否需要医生人工和患者联系: "否",
    锻炼辅助仪器是否有问题: "否",
    创建时间: "2025-02-27T09:30:00Z"
  },
  {
    _id: "record005",
    患者名字: "陈明芳",
    手机号: "13712342468",
    通话时间: "2025-02-28 14:20",
    记录日期: "2025-02-28",
    训练完成情况: "完成",
    训练次数: "36次",
    训练时长: "55分钟",
    是否有不适感: "否",
    是否需要医生人工和患者联系: "否",
    锻炼辅助仪器是否有问题: "是",
    锻炼辅助仪器问题内容: "绑带有些松动",
    创建时间: "2025-02-28T14:20:00Z"
  }
];

export const mockStats = {
  totalPersonnel: 156,
  pendingToday: 28,
  completedToday: 18,
  avgTraining: 32,
  completionRate: 64.3
};

export const mockProgressData = {
  "65a1b2c3d4e5f6789abcdef0": {
    currentDay: 12,
    totalDays: 30,
    compliance: 92,
    todayTraining: 35,
    avgTraining: 32,
    records: [
      { date: "2025-02-28", training: 35, completed: true },
      { date: "2025-02-27", training: 30, completed: true },
      { date: "2025-02-26", training: 32, completed: true },
      { date: "2025-02-25", training: 0, completed: false },
      { date: "2025-02-24", training: 33, completed: true },
      { date: "2025-02-23", training: 35, completed: true },
      { date: "2025-02-22", training: 31, completed: true },
    ]
  },
  "65a1b2c3d4e5f6789abcdef1": {
    currentDay: 9,
    totalDays: 30,
    compliance: 85,
    todayTraining: 28,
    avgTraining: 30
  },
  "65a1b2c3d4e5f6789abcdef2": {
    currentDay: 17,
    totalDays: 30,
    compliance: 78,
    todayTraining: 0,
    avgTraining: 28
  },
  "65a1b2c3d4e5f6789abcdef3": {
    currentDay: 15,
    totalDays: 30,
    compliance: 95,
    todayTraining: 32,
    avgTraining: 35
  },
  "65a1b2c3d4e5f6789abcdef4": {
    currentDay: 7,
    totalDays: 30,
    compliance: 45,
    todayTraining: 0,
    avgTraining: 18
  },
  "65a1b2c3d4e5f6789abcdef5": {
    currentDay: 30,
    totalDays: 30,
    compliance: 90,
    todayTraining: 36,
    avgTraining: 32
  }
};

export const mockInsightsData = {
  topics: {
    painRelief: 78,
    difficultyMeeting: 45,
    deviceDiscomfort: 32,
    mobilityImprovement: 65
  },
  sentiment: 70,
  keywords: ["疼痛减轻", "行走改善", "膝盖松动", "坚持锻炼", "睡眠改善", "上下楼", "肌肉酸痛", "活动范围", "生活质量", "信心增强"],
  quotes: [
    { topic: "膝关节疼痛减轻", quote: "以前走路都疼，现在好多了，能下楼了。", author: "张丽珍, 76岁" },
    { topic: "摆腿次数达标困难", quote: "有时候忙起来就忘了做，晚上想起来又累了。", author: "王建国, 71岁" },
    { topic: "设备佩戴不适感", quote: "刚戴上感觉有点紧，过两天就习惯了。", author: "李大明, 68岁" },
    { topic: "日常行动能力改善", quote: "现在上下楼不用扶着墙了，感觉腿有劲多了。", author: "赵淑华, 73岁" }
  ]
};