import React, { useState, useEffect } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { personnelAPI } from '../services/api';
import { ExercisePlanOption } from '../types';

interface AddPatientModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void; // 添加成功后的回调
}

interface FormData {
  patient_id: string;
  name: string;
  phone: string;
  age: string;
  gender: '男' | '女';
  enrollment_date: string;
  training_status: '未开始' | '训练中' | '暂停' | '终止' | '休息';
  exercise_plan_id: string;
}

const AddPatientModal: React.FC<AddPatientModalProps> = ({ isOpen, onClose, onSuccess }) => {
  const [formData, setFormData] = useState<FormData>({
    patient_id: '',
    name: '',
    phone: '',
    age: '',
    gender: '男',
    enrollment_date: new Date().toISOString().split('T')[0],
    training_status: '未开始',
    exercise_plan_id: ''
  });

  const [exercisePlans, setExercisePlans] = useState<ExercisePlanOption[]>([]);
  const [defaultPlanId, setDefaultPlanId] = useState<string>('');

  // 获取锻炼计划列表
  useEffect(() => {
    if (isOpen) {
      fetchExercisePlans();
    }
  }, [isOpen]);

  const fetchExercisePlans = async () => {
    try {
      const response = await personnelAPI.getAvailableExercisePlans();
      setExercisePlans(response || []);

      // 设置默认计划（第一个活跃的计划）
      const activePlans = response?.filter((plan: ExercisePlanOption) => plan.status === 'active') || [];
      if (activePlans.length > 0) {
        const defaultId = activePlans[0]._id;
        setDefaultPlanId(defaultId);
        setFormData(prev => ({ ...prev, exercise_plan_id: defaultId }));
      }
    } catch (error) {
      console.error('获取锻炼计划失败:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      console.log('🔄 开始添加患者...');
      await personnelAPI.add({
        ...formData,
        age: parseInt(formData.age)
      });
      console.log('✅ 患者添加成功');
      alert('患者添加成功！');
      
      // 重置表单
      setFormData({
        patient_id: '',
        name: '',
        phone: '',
        age: '',
        gender: '男',
        enrollment_date: new Date().toISOString().split('T')[0],
        training_status: '未开始',
        exercise_plan_id: defaultPlanId
      });
      console.log('🔄 表单已重置');
      
      onClose();
      console.log('🔄 模态框已关闭');
      
      // 调用成功回调，通知父组件刷新数据
      if (onSuccess) {
        console.log('🔄 调用刷新回调...');
        onSuccess();
        console.log('✅ 刷新回调已调用');
      } else {
        console.log('⚠️ 没有刷新回调函数');
      }
    } catch (error) {
      console.error('添加患者失败:', error);
      alert('添加患者失败，请重试');
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-11/12 max-w-2xl">
        <div className="p-5 border-b flex justify-between items-center">
          <h2 className="text-2xl font-semibold">添加新患者</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-5">
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">患者编号 *</label>
            <input
              type="text"
              name="patient_id"
              value={formData.patient_id}
              onChange={handleChange}
              className="w-full px-3 py-2 border rounded focus:outline-none focus:border-blue-500"
              placeholder="请输入患者编号（如：P0001, EXP001等）"
              required
            />
            <p className="text-sm text-gray-500 mt-1">
              患者编号用于实验记录，请确保唯一性
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium mb-2">姓名 *</label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className="w-full px-3 py-2 border rounded focus:outline-none focus:border-blue-500"
                placeholder="请输入患者姓名"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">性别</label>
              <select 
                name="gender"
                value={formData.gender}
                onChange={handleChange}
                className="w-full px-3 py-2 border rounded focus:outline-none focus:border-blue-500"
              >
                <option value="男">男</option>
                <option value="女">女</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">年龄 *</label>
              <input 
                type="number" 
                name="age"
                value={formData.age}
                onChange={handleChange}
                className="w-full px-3 py-2 border rounded focus:outline-none focus:border-blue-500" 
                placeholder="请输入年龄" 
                required
              />
            </div>
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">联系电话 *</label>
            <input 
              type="tel" 
              name="phone"
              value={formData.phone}
              onChange={handleChange}
              className="w-full px-3 py-2 border rounded focus:outline-none focus:border-blue-500" 
              placeholder="请输入手机号码" 
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium mb-2">入组日期</label>
              <input
                type="date"
                name="enrollment_date"
                value={formData.enrollment_date}
                onChange={handleChange}
                className="w-full px-3 py-2 border rounded focus:outline-none focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">训练状态</label>
              <select
                name="training_status"
                value={formData.training_status}
                onChange={handleChange}
                className="w-full px-3 py-2 border rounded focus:outline-none focus:border-blue-500"
              >
                <option value="未开始">未开始</option>
                <option value="训练中">训练中</option>
                <option value="暂停">暂停</option>
                <option value="终止">终止</option>
                <option value="休息">休息</option>
              </select>
            </div>
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">锻炼计划</label>
            <select
              name="exercise_plan_id"
              value={formData.exercise_plan_id}
              onChange={handleChange}
              className="w-full px-3 py-2 border rounded focus:outline-none focus:border-blue-500"
            >
              <option value="">请选择锻炼计划（可选）</option>
              {exercisePlans.map(plan => (
                <option key={plan._id} value={plan._id}>
                  {plan.plan_name} {plan.status === 'active' ? '(推荐)' : `(${plan.status})`}
                </option>
              ))}
            </select>
            <p className="text-sm text-gray-500 mt-1">
              为患者分配合适的锻炼计划，可以在后续修改
            </p>
          </div>

          <div className="p-5 border-t flex justify-end gap-3 -mx-5 -mb-5 mt-5">
            <button 
              type="button"
              onClick={onClose} 
              className="px-4 py-2 bg-gray-100 text-slate-700 rounded hover:bg-gray-200"
            >
              取消
            </button>
            <button 
              type="submit"
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              保存
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddPatientModal; 