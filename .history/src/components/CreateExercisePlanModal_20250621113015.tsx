import React, { useState, useEffect } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { exercisePlanAPI, personnelAPI } from '../services/api';
import { Patient } from '../types';

interface CreateExercisePlanModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const CreateExercisePlanModal: React.FC<CreateExercisePlanModalProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    patient_id: '',
    plan_name: '',
    description: '',
    total_weeks: 4,
    initial_daily_count: 200,
    weekly_increment: 100,
    max_rest_days_per_week: 2,
    start_date: new Date().toISOString().split('T')[0]
  });

  useEffect(() => {
    if (isOpen) {
      fetchPatients();
    }
  }, [isOpen]);

  const fetchPatients = async () => {
    try {
      const response = await personnelAPI.getList({ limit: 1000 });
      setPatients(response.data || []);
    } catch (error) {
      console.error('获取患者列表失败:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.patient_id) {
      alert('请选择患者');
      return;
    }

    try {
      setLoading(true);
      await exercisePlanAPI.create(formData);
      onSuccess();
      onClose();
      // 重置表单
      setFormData({
        patient_id: '',
        plan_name: '',
        description: '',
        total_weeks: 4,
        initial_daily_count: 200,
        weekly_increment: 100,
        max_rest_days_per_week: 2,
        start_date: new Date().toISOString().split('T')[0]
      });
    } catch (error: any) {
      console.error('创建锻炼计划失败:', error);
      alert(error.message || '创建失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name.includes('count') || name.includes('weeks') || name.includes('increment') || name.includes('days')
        ? parseInt(value) || 0
        : value
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">创建锻炼计划</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 患者选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              选择患者 *
            </label>
            <select
              name="patient_id"
              value={formData.patient_id}
              onChange={handleInputChange}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">请选择患者</option>
              {patients.map(patient => (
                <option key={patient._id} value={patient.patient_id}>
                  {patient.patient_id} - {patient.name}
                </option>
              ))}
            </select>
          </div>

          {/* 计划名称 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              计划名称 *
            </label>
            <input
              type="text"
              name="plan_name"
              value={formData.plan_name}
              onChange={handleInputChange}
              required
              placeholder="例如：膝关节康复训练计划"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* 计划描述 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              计划描述
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
              placeholder="描述锻炼计划的目标和注意事项..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* 锻炼参数 */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                总锻炼周数 *
              </label>
              <input
                type="number"
                name="total_weeks"
                value={formData.total_weeks}
                onChange={handleInputChange}
                min="1"
                max="52"
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                初始每日次数 *
              </label>
              <input
                type="number"
                name="initial_daily_count"
                value={formData.initial_daily_count}
                onChange={handleInputChange}
                min="1"
                max="2000"
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                每周递增次数 *
              </label>
              <input
                type="number"
                name="weekly_increment"
                value={formData.weekly_increment}
                onChange={handleInputChange}
                min="0"
                max="500"
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                每周最多休息天数 *
              </label>
              <input
                type="number"
                name="max_rest_days_per_week"
                value={formData.max_rest_days_per_week}
                onChange={handleInputChange}
                min="0"
                max="7"
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* 开始日期 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              开始日期 *
            </label>
            <input
              type="date"
              name="start_date"
              value={formData.start_date}
              onChange={handleInputChange}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* 计划预览 */}
          <div className="bg-gray-50 p-4 rounded-md">
            <h4 className="font-medium text-gray-900 mb-2">计划预览</h4>
            <div className="text-sm text-gray-600 space-y-1">
              <p>• 第1周：每天 {formData.initial_daily_count} 次</p>
              <p>• 第2周：每天 {formData.initial_daily_count + formData.weekly_increment} 次</p>
              <p>• 第3周：每天 {formData.initial_daily_count + formData.weekly_increment * 2} 次</p>
              {formData.total_weeks > 3 && (
                <p>• 第{formData.total_weeks}周：每天 {formData.initial_daily_count + formData.weekly_increment * (formData.total_weeks - 1)} 次</p>
              )}
              <p>• 每周最多可休息 {formData.max_rest_days_per_week} 天</p>
            </div>
          </div>

          {/* 按钮 */}
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
            >
              {loading ? '创建中...' : '创建计划'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateExercisePlanModal;
