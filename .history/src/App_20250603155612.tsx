// App.jsx
import React, { useState, useEffect } from 'react';
import { 
  HomeIcon, 
  UserGroupIcon, 
  ChartPieIcon, 
  MicrophoneIcon, 
  LightBulbIcon, 
  CogIcon,
  PlusIcon,
  EyeIcon,
  PhoneIcon,
  PencilIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  CalendarIcon,
  ClockIcon,
  ArrowDownTrayIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  XMarkIcon,
  PlayIcon,
  PauseIcon,
  FunnelIcon,
  MagnifyingGlassCircleIcon
} from '@heroicons/react/24/outline';


import { 
  mockPersonnel, 
  mockCallRecords, 
  mockStats, 
  mockProgressData, 
  mockInsightsData 
} from './mock';

// 侧边栏组件
const Sidebar = ({ activeMenu, setActiveMenu }) => {
  const menuItems = [
    { id: 'dashboard', icon: HomeIcon, label: '控制面板' },
    { id: 'patients', icon: UserGroupIcon, label: '患者管理' },
    { id: 'progress', icon: ChartPieIcon, label: '康复进度' },
    { id: 'recordings', icon: MicrophoneIcon, label: '通话记录' },
    { id: 'insights', icon: LightBulbIcon, label: '智能摘要' },
    { id: 'settings', icon: CogIcon, label: '系统设置' },
  ];

  return (
    <div className="w-64 bg-slate-800 text-white flex-shrink-0">
      <div className="p-5 border-b border-white/10 text-center">
        <h2 className="text-2xl font-bold mb-1">膝盖康复助手</h2>
        <p className="text-sm opacity-70">智能随访管理系统</p>
      </div>
      <div className="py-5">
        {menuItems.map(item => {
          const Icon = item.icon;
          return (
            <div
              key={item.id}
              onClick={() => setActiveMenu(item.id)}
              className={`px-5 py-3 flex items-center cursor-pointer transition-colors ${
                activeMenu === item.id 
                  ? 'bg-blue-500' 
                  : 'hover:bg-white/10'
              }`}
            >
              <Icon className="w-6 h-6 mr-3" />
              <span>{item.label}</span>
            </div>
          );
        })}
      </div>
    </div>
  );
};

// 统计卡片组件
const StatCard = ({ title, value, trend, trendValue, isUp }) => {
  return (
    <div className="bg-white rounded-lg p-5 shadow-sm">
      <h3 className="text-gray-500 text-sm mb-4">{title}</h3>
      <div className="text-3xl font-bold text-slate-800 mb-3">{value}</div>
      <div className={`flex items-center text-sm ${isUp ? 'text-green-500' : 'text-red-500'}`}>
        {isUp ? <ArrowUpIcon className="w-4 h-4 mr-1" /> : <ArrowDownIcon className="w-4 h-4 mr-1" />}
        <span>{trendValue}</span>
      </div>
    </div>
  );
};

// 状态标签组件
const StatusBadge = ({ status, variant }) => {
  const variants = {
    active: 'bg-green-100 text-green-600',
    pending: 'bg-yellow-100 text-yellow-600',
    inactive: 'bg-red-100 text-red-600',
    completed: 'bg-blue-100 text-blue-600',
  };

  return (
    <span className={`inline-block px-3 py-1 rounded-full text-xs ${variants[variant]}`}>
      {status}
    </span>
  );
};

// 控制面板页面
const DashboardPage = ({ setShowPatientModal, setShowAddPatientModal, setSelectedPatient }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [todayTasks, setTodayTasks] = useState([]);
  const [stats, setStats] = useState(mockStats);

  useEffect(() => {
    // 模拟获取今日任务数据
    const tasks = mockPersonnel.filter(p => p.training_status === '训练中').map(person => {
      const todayRecord = mockCallRecords.find(r => 
        r.手机号 === person.phone && r.记录日期 === '2025-02-28'
      );
      return {
        ...person,
        callStatus: todayRecord ? 
          (todayRecord.训练完成情况 === '完成' ? '已完成' : 
           todayRecord.训练完成情况 === '未接通' ? '未接通' : '未完成') : 
          '待随访',
        callStatusType: todayRecord ? 
          (todayRecord.训练完成情况 === '完成' ? 'completed' : 
           todayRecord.训练完成情况 === '未接通' ? 'pending' : 'inactive') : 
          'active',
        todayTraining: todayRecord?.训练次数 || '--'
      };
    });
    setTodayTasks(tasks);
  }, []);

  const filteredTasks = todayTasks.filter(task => 
    !searchTerm || 
    task.name.includes(searchTerm) || 
    task.phone.includes(searchTerm)
  );

  const statsData = [
    { title: '有效志愿者总数', value: stats.totalPersonnel, trend: '比上周增加12人', isUp: true },
    { title: '今日待随访人数', value: stats.pendingToday, trend: '比昨天减少5人', isUp: false },
    { title: '今日已完成随访', value: stats.completedToday, trend: `完成率${stats.completionRate}%`, isUp: true },
    { title: '平均每日摆腿次数', value: stats.avgTraining, trend: '比上周增加3次', isUp: true },
  ];

  return (
    <div>
      <div className="flex justify-between items-center mb-8 pb-5 border-b">
        <h1 className="text-3xl font-bold text-slate-800">控制面板</h1>
        <div className="flex w-2/5">
          <input 
            type="text" 
            placeholder="搜索患者姓名或电话..." 
            className="flex-1 px-4 py-2 border rounded-l focus:outline-none focus:border-blue-500"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <button className="px-4 py-2 bg-blue-500 text-white rounded-r hover:bg-blue-600">
            <MagnifyingGlassCircleIcon className="w-5 h-5" />
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5 mb-8">
        {statsData.map((stat, index) => (
          <StatCard key={index} {...stat} />
        ))}
      </div>

      <div className="bg-white rounded-lg p-5 shadow-sm">
        <div className="flex justify-between items-center mb-5">
          <h2 className="text-xl font-semibold text-slate-800">今日随访任务</h2>
          <div className="flex gap-3">
            <button className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-slate-700 rounded hover:bg-gray-200">
              <FunnelIcon className="w-4 h-4" />
              <span>筛选</span>
            </button>
            <button 
              onClick={() => setShowAddPatientModal(true)}
              className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              <PlusIcon className="w-4 h-4" />
              <span>添加患者</span>
            </button>
          </div>
        </div>

        <table className="w-full">
          <thead>
            <tr className="border-b">
              <th className="text-left py-3 px-4 font-semibold text-gray-600">患者信息</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">联系方式</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">入组日期</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">随访状态</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">最近摆腿数据</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">操作</th>
            </tr>
          </thead>
          <tbody>
            {filteredTasks.map((task) => (
              <tr key={task._id} className="border-b hover:bg-gray-50">
                <td className="py-3 px-4">{task.name} ({task.age}岁 {task.gender})</td>
                <td className="py-3 px-4">{task.phone}</td>
                <td className="py-3 px-4">{task.enrollment_date}</td>
                <td className="py-3 px-4">
                  <StatusBadge status={task.callStatus} variant={task.callStatusType} />
                </td>
                <td className="py-3 px-4">{task.todayTraining}</td>
                <td className="py-3 px-4">
                  <button 
                    onClick={() => {
                      setSelectedPatient(task);
                      setShowPatientModal(true);
                    }}
                    className="text-gray-500 hover:text-blue-500 mr-2"
                  >
                    <EyeIcon className="w-5 h-5" />
                  </button>
                  <button className="text-gray-500 hover:text-blue-500 mr-2">
                    <PhoneIcon className="w-5 h-5" />
                  </button>
                  <button className="text-gray-500 hover:text-blue-500">
                    <PencilIcon className="w-5 h-5" />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        <div className="flex justify-center mt-5 gap-1">
          <button className="w-9 h-9 border rounded flex items-center justify-center hover:bg-gray-50">
            <ChevronLeftIcon className="w-4 h-4" />
          </button>
          <button className="w-9 h-9 border rounded bg-blue-500 text-white">1</button>
          <button className="w-9 h-9 border rounded hover:bg-gray-50">2</button>
          <button className="w-9 h-9 border rounded hover:bg-gray-50">3</button>
          <button className="w-9 h-9 border rounded flex items-center justify-center hover:bg-gray-50">
            <ChevronRightIcon className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

// 患者管理页面
const PatientsPage = ({ setShowPatientModal, setShowAddPatientModal, setSelectedPatient }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [patients, setPatients] = useState(mockPersonnel);

  const filteredPatients = patients.filter(patient => 
    !searchTerm || 
    patient.name.includes(searchTerm) || 
    patient.phone.includes(searchTerm)
  );

  return (
    <div>
      <div className="flex justify-between items-center mb-8 pb-5 border-b">
        <h1 className="text-3xl font-bold text-slate-800">患者管理</h1>
        <div className="flex w-2/5">
          <input 
            type="text" 
            placeholder="搜索患者姓名或电话..." 
            className="flex-1 px-4 py-2 border rounded-l focus:outline-none focus:border-blue-500"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <button className="px-4 py-2 bg-blue-500 text-white rounded-r hover:bg-blue-600">
            <MagnifyingGlassCircleIcon className="w-5 h-5" />
          </button>
        </div>
      </div>

      <div className="bg-white rounded-lg p-5 shadow-sm">
        <div className="flex justify-between items-center mb-5">
          <h2 className="text-xl font-semibold text-slate-800">所有患者</h2>
          <div className="flex gap-3">
            <button className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-slate-700 rounded hover:bg-gray-200">
              <FunnelIcon className="w-4 h-4" />
              <span>筛选</span>
            </button>
            <button className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-slate-700 rounded hover:bg-gray-200">
              <ArrowDownTrayIcon className="w-4 h-4" />
              <span>导出</span>
            </button>
            <button 
              onClick={() => setShowAddPatientModal(true)}
              className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              <PlusIcon className="w-4 h-4" />
              <span>添加患者</span>
            </button>
          </div>
        </div>

        <table className="w-full">
          <thead>
            <tr className="border-b">
              <th className="text-left py-3 px-4 font-semibold text-gray-600">患者ID</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">患者信息</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">联系方式</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">入组日期</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">参与状态</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">随访状态</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">摆腿依从性</th>
              <th className="text-left py-3 px-4 font-semibold text-gray-600">操作</th>
            </tr>
          </thead>
          <tbody>
            {filteredPatients.map((patient) => {
              const progress = mockProgressData[patient._id] || {};
              const statusMap = {
                '训练中': { label: '正常', variant: 'active' },
                '暂停': { label: '暂停', variant: 'pending' },
                '终止': { label: '已退出', variant: 'inactive' },
                '休息': { label: '休息中', variant: 'pending' }
              };
              const status = statusMap[patient.training_status] || { label: '未知', variant: 'inactive' };
              
              return (
                <tr key={patient._id} className="border-b hover:bg-gray-50">
                  <td className="py-3 px-4 font-mono text-xs">{patient._id.slice(-6).toUpperCase()}</td>
                  <td className="py-3 px-4">{patient.name} ({patient.age}岁 {patient.gender})</td>
                  <td className="py-3 px-4">{patient.phone}</td>
                  <td className="py-3 px-4">{patient.enrollment_date}</td>
                  <td className="py-3 px-4">
                    <StatusBadge status={status.label} variant={status.variant} />
                  </td>
                  <td className="py-3 px-4">第{progress.currentDay || 0}天</td>
                  <td className="py-3 px-4">{progress.compliance || 0}%</td>
                  <td className="py-3 px-4">
                    <button 
                      onClick={() => {
                        setSelectedPatient(patient);
                        setShowPatientModal(true);
                      }}
                      className="text-gray-500 hover:text-blue-500 mr-2"
                    >
                      <EyeIcon className="w-5 h-5" />
                    </button>
                    <button className="text-gray-500 hover:text-blue-500 mr-2">
                      <PhoneIcon className="w-5 h-5" />
                    </button>
                    <button className="text-gray-500 hover:text-blue-500">
                      <PencilIcon className="w-5 h-5" />
                    </button>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};

// 康复进度页面
const ProgressPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const patients = mockPersonnel.filter(p => p.training_status === '训练中');

  const getProgressColor = (progress) => {
    if (progress >= 90) return '#2ecc71';
    if (progress >= 80) return '#3498db';
    if (progress >= 60) return '#f39c12';
    return '#e74c3c';
  };

  const filteredPatients = patients.filter(patient => 
    !searchTerm || patient.name.includes(searchTerm)
  );

  return (
    <div>
      <div className="flex justify-between items-center mb-8 pb-5 border-b">
        <h1 className="text-3xl font-bold text-slate-800">康复进度</h1>
        <div className="flex w-2/5">
          <input 
            type="text" 
            placeholder="搜索患者姓名..." 
            className="flex-1 px-4 py-2 border rounded-l focus:outline-none focus:border-blue-500"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <button className="px-4 py-2 bg-blue-500 text-white rounded-r hover:bg-blue-600">
            <MagnifyingGlassCircleIcon className="w-5 h-5" />
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
        {filteredPatients.map((patient) => {
          const progress = mockProgressData[patient._id] || {
            currentDay: 0,
            totalDays: 30,
            compliance: 0,
            todayTraining: 0,
            avgTraining: 0
          };
          
          return (
            <div key={patient._id} className="bg-white rounded-lg overflow-hidden shadow-sm">
              <div className="bg-blue-500 text-white p-4 flex justify-between items-center">
                <h3 className="font-semibold">{patient.name}</h3>
                <span>{patient.age}岁 {patient.gender}</span>
              </div>
              <div className="p-4">
                <div className="relative w-36 h-36 mx-auto mb-5">
                  <svg className="w-36 h-36 transform -rotate-90">
                    <circle
                      cx="72"
                      cy="72"
                      r="60"
                      stroke="#e6e6e6"
                      strokeWidth="10"
                      fill="none"
                    />
                    <circle
                      cx="72"
                      cy="72"
                      r="60"
                      stroke={getProgressColor(progress.compliance)}
                      strokeWidth="10"
                      fill="none"
                      strokeDasharray={`${(progress.compliance / 100) * 377} 377`}
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-3xl font-bold">{progress.compliance}%</span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-3 mb-4">
                  <div className="text-center p-3 bg-gray-50 rounded">
                    <p className="text-sm text-gray-500 mb-1">当前天数</p>
                    <p className="text-lg font-semibold">{progress.currentDay}/{progress.totalDays}</p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded">
                    <p className="text-sm text-gray-500 mb-1">今日摆腿</p>
                    <p className="text-lg font-semibold">{progress.todayTraining}次</p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded">
                    <p className="text-sm text-gray-500 mb-1">平均摆腿</p>
                    <p className="text-lg font-semibold">{progress.avgTraining}次</p>
                  </div>
                  <div className="text-center p-3 bg-gray-50 rounded">
                    <p className="text-sm text-gray-500 mb-1">依从性</p>
                    <p className="text-lg font-semibold">{progress.compliance}%</p>
                  </div>
                </div>

                <button className="w-full py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                  查看详情
                </button>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

// 通话记录页面
const RecordingsPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    startDate: '2025-02-01',
    endDate: '2025-02-28',
    callType: '全部',
    status: '全部'
  });
  const [records] = useState(mockCallRecords);

  const filteredRecords = records.filter(record => {
    if (searchTerm && !record.患者名字.includes(searchTerm) && !record.手机号.includes(searchTerm)) {
      return false;
    }
    return true;
  });

  return (
    <div>
      <div className="flex justify-between items-center mb-8 pb-5 border-b">
        <h1 className="text-3xl font-bold text-slate-800">通话记录</h1>
        <div className="flex w-2/5">
          <input 
            type="text" 
            placeholder="搜索患者姓名或关键词..." 
            className="flex-1 px-4 py-2 border rounded-l focus:outline-none focus:border-blue-500"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <button className="px-4 py-2 bg-blue-500 text-white rounded-r hover:bg-blue-600">
            <MagnifyingGlassCircleIcon className="w-5 h-5" />
          </button>
        </div>
      </div>

      <div className="bg-white rounded-lg p-5 shadow-sm">
        <div className="flex flex-wrap gap-4 mb-5 p-4 bg-gray-50 rounded">
          <div className="flex items-center gap-2">
            <label className="font-medium">日期范围：</label>
            <input 
              type="date" 
              value={filters.startDate}
              onChange={(e) => setFilters({...filters, startDate: e.target.value})}
              className="px-3 py-1 border rounded"
            />
            <span>至</span>
            <input 
              type="date" 
              value={filters.endDate}
              onChange={(e) => setFilters({...filters, endDate: e.target.value})}
              className="px-3 py-1 border rounded"
            />
          </div>
          <div className="flex items-center gap-2">
            <label className="font-medium">通话类型：</label>
            <select 
              value={filters.callType}
              onChange={(e) => setFilters({...filters, callType: e.target.value})}
              className="px-3 py-1 border rounded"
            >
              <option>全部</option>
              <option>首次入组</option>
              <option>日常随访</option>
              <option>中止提醒</option>
            </select>
          </div>
          <div className="flex items-center gap-2">
            <label className="font-medium">随访状态：</label>
            <select 
              value={filters.status}
              onChange={(e) => setFilters({...filters, status: e.target.value})}
              className="px-3 py-1 border rounded"
            >
              <option>全部</option>
              <option>已接通</option>
              <option>未接通</option>
              <option>拒绝接听</option>
            </select>
          </div>
          <button className="px-4 py-1 bg-blue-500 text-white rounded hover:bg-blue-600">
            应用筛选
          </button>
        </div>

        <div className="space-y-4">
          {filteredRecords.map((record) => (
            <div key={record._id} className="border rounded-lg p-4">
              <div className="flex justify-between items-start mb-3">
                <h3 className="font-semibold text-lg">{record.患者名字} - 日常随访</h3>
                <StatusBadge 
                  status={record.训练完成情况 === '完成' ? '已接通' : record.训练完成情况 || '未接通'} 
                  variant={record.训练完成情况 === '完成' ? 'completed' : 'pending'} 
                />
              </div>
              
              <div className="flex gap-6 text-sm text-gray-600 mb-3">
                <span className="flex items-center gap-1">
                  <CalendarIcon className="w-4 h-4" />
                  {record.通话时间}
                </span>
                <span className="flex items-center gap-1">
                  <ClockIcon className="w-4 h-4" />
                  通话时长: {record.对话历史记录 ? '3分42秒' : '0分0秒'}
                </span>
                <span className="flex items-center gap-1">
                  <PhoneIcon className="w-4 h-4" />
                  {record.手机号}
                </span>
              </div>

              {record.对话历史记录 && (
                <>
                  <div className="mb-3 p-3 bg-gray-100 rounded flex items-center justify-between">
                    <span className="text-sm">通话录音</span>
                    <button className="text-blue-500 hover:text-blue-600">
                      <PlayIcon className="w-5 h-5" />
                    </button>
                  </div>

                  <div className="relative mb-3">
                    <div className="max-h-32 overflow-hidden">
                      <div className="p-3 bg-gray-50 rounded text-sm space-y-2">
                        {record.对话历史记录.slice(0, 3).map((chat, index) => (
                          <p key={index}>
                            <strong>{chat.role === 'assistant' ? '智能助手' : record.患者名字}:</strong> {chat.content}
                          </p>
                        ))}
                      </div>
                    </div>
                    <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent"></div>
                  </div>
                </>
              )}

              <div className="flex justify-between items-center">
                <div className="flex flex-wrap gap-2">
                  {record.训练次数 && (
                    <span className="px-2 py-1 bg-blue-100 text-blue-600 rounded text-xs">
                      摆腿{record.训练次数}
                    </span>
                  )}
                  {record.是否有不适感 === '否' && (
                    <span className="px-2 py-1 bg-green-100 text-green-600 rounded text-xs">
                      无不适
                    </span>
                  )}
                  {record.是否有不适感 === '是' && (
                    <span className="px-2 py-1 bg-red-100 text-red-600 rounded text-xs">
                      {record.不适感内容}
                    </span>
                  )}
                  {record.锻炼辅助仪器是否有问题 === '是' && (
                    <span className="px-2 py-1 bg-yellow-100 text-yellow-600 rounded text-xs">
                      设备问题
                    </span>
                  )}
                </div>
                <div className="flex gap-2">
                  <button className="px-3 py-1 bg-gray-100 text-slate-700 rounded text-sm hover:bg-gray-200">
                    查看完整记录
                  </button>
                  <button className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600">
                    查看摘要
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="flex justify-center mt-5 gap-1">
          <button className="w-9 h-9 border rounded flex items-center justify-center hover:bg-gray-50">
            <ChevronLeftIcon className="w-4 h-4" />
          </button>
          <button className="w-9 h-9 border rounded bg-blue-500 text-white">1</button>
          <button className="w-9 h-9 border rounded hover:bg-gray-50">2</button>
          <button className="w-9 h-9 border rounded hover:bg-gray-50">3</button>
          <button className="w-9 h-9 border rounded flex items-center justify-center hover:bg-gray-50">
            <ChevronRightIcon className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

// 智能摘要页面
const InsightsPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [timeRange, setTimeRange] = useState('最近7天');
  const insights = mockInsightsData;

  const topicsData = [
    { 
      title: '膝关节疼痛减轻', 
      percentage: insights.topics.painRelief,
      description: '大多数患者报告在使用康复设备和进行摆腿锻炼后，膝关节疼痛有明显减轻。尤其是在治疗2周后，显著改善。',
      quote: insights.quotes[0]
    },
    { 
      title: '摆腿次数达标困难', 
      percentage: insights.topics.difficultyMeeting,
      description: '部分患者表示每天坚持30次以上的摆腿运动有困难，主要原因是疲劳感和时间安排问题。',
      quote: insights.quotes[1]
    },
    { 
      title: '设备佩戴不适感', 
      percentage: insights.topics.deviceDiscomfort,
      description: '约三分之一的患者报告设备佩戴时有轻微不适感，主要集中在初次使用的前3天。之后多数适应良好。',
      quote: insights.quotes[2]
    },
    { 
      title: '日常行动能力改善', 
      percentage: insights.topics.mobilityImprovement,
      description: '超过半数患者报告日常活动能力有所提升，如上下楼梯更轻松、行走距离增加、蹲起更容易等。',
      quote: insights.quotes[3]
    }
  ];

  return (
    <div>
      <div className="flex justify-between items-center mb-8 pb-5 border-b">
        <h1 className="text-3xl font-bold text-slate-800">智能摘要</h1>
        <div className="flex w-2/5">
          <input 
            type="text" 
            placeholder="搜索患者姓名..." 
            className="flex-1 px-4 py-2 border rounded-l focus:outline-none focus:border-blue-500"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <button className="px-4 py-2 bg-blue-500 text-white rounded-r hover:bg-blue-600">
            <MagnifyingGlassCircleIcon className="w-5 h-5" />
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-5">
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg p-5 shadow-sm">
            <div className="flex justify-between items-center mb-5">
              <h2 className="text-xl font-semibold text-slate-800">患者主要反馈话题</h2>
              <div className="flex gap-3">
                <select 
                  value={timeRange}
                  onChange={(e) => setTimeRange(e.target.value)}
                  className="px-3 py-1 border rounded"
                >
                  <option>最近7天</option>
                  <option>最近30天</option>
                  <option>全部记录</option>
                </select>
                <button className="flex items-center gap-2 px-3 py-1 bg-gray-100 text-slate-700 rounded hover:bg-gray-200">
                  <ArrowDownTrayIcon className="w-4 h-4" />
                  <span>导出报告</span>
                </button>
              </div>
            </div>

            <div className="space-y-4">
              {topicsData.map((topic, index) => (
                <div key={index} className="border-b pb-4 last:border-b-0">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-semibold text-lg">{topic.title}</h3>
                    <span className="px-3 py-1 bg-blue-100 text-blue-600 rounded-full text-sm">
                      {topic.percentage}% 患者提及
                    </span>
                  </div>
                  <p className="text-gray-600 mb-3">{topic.description}</p>
                  <div className="p-3 bg-gray-50 border-l-4 border-blue-500 italic text-sm">
                    "{topic.quote.quote}" - {topic.quote.author}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="space-y-5">
          <div className="bg-white rounded-lg p-5 shadow-sm">
            <h3 className="font-semibold text-lg mb-4">热门关键词</h3>
            <div className="flex flex-wrap gap-2">
              {insights.keywords.map((keyword, index) => (
                <span key={index} className="px-3 py-1 bg-blue-100 text-blue-600 rounded-full text-sm">
                  {keyword}
                </span>
              ))}
            </div>
          </div>

          <div className="bg-white rounded-lg p-5 shadow-sm">
            <h3 className="font-semibold text-lg mb-4">患者情绪分析</h3>
            <div className="mb-4">
              <div className="h-3 bg-gray-200 rounded-full relative overflow-hidden">
                <div 
                  className="absolute top-0 left-0 h-full rounded-full"
                  style={{
                    width: `${insights.sentiment}%`,
                    background: 'linear-gradient(to right, #e74c3c, #f39c12, #2ecc71)'
                  }}
                ></div>
                <div 
                  className="absolute top-1/2 transform -translate-y-1/2 w-1 h-5 bg-slate-700"
                  style={{ left: `${insights.sentiment}%` }}
                ></div>
              </div>
              <div className="flex justify-between text-xs mt-1">
                <span>消极</span>
                <span>中性</span>
                <span>积极</span>
              </div>
            </div>
            <p className="text-lg font-semibold mb-2">整体情绪得分: {insights.sentiment}/100</p>
            <p className="text-sm text-gray-600">
              患者总体情绪积极，对治疗效果表现出满意态度。少数负面情绪主要与坚持训练的困难和初期不适感有关。
            </p>
          </div>

          <div className="bg-white rounded-lg p-5 shadow-sm">
            <h3 className="font-semibold text-lg mb-4">治疗依从性分析</h3>
            <div className="text-3xl font-bold text-blue-500 mb-3">82%</div>
            <p className="text-sm text-gray-600 mb-3">整体依从性良好，其中：</p>
            <ul className="space-y-2 text-sm">
              <li className="flex items-start">
                <span className="text-green-500 mr-2">•</span>
                95% 患者完成至少70%的摆腿训练
              </li>
              <li className="flex items-start">
                <span className="text-green-500 mr-2">•</span>
                87% 患者每天至少做20次摆腿
              </li>
              <li className="flex items-start">
                <span className="text-yellow-500 mr-2">•</span>
                75% 患者坚持每日佩戴设备
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

// 系统设置页面
const SettingsPage = () => {
  const [settings, setSettings] = useState({
    startTime: '09:00',
    endTime: '18:00',
    firstCallScript: '您好，我是膝关节康复中心的智能助手，您之前通过微信公众号报名参加了我们的膝关节康复仪器试用活动。请问您现在方便接听电话吗？',
    dailyCallScript: '您好，我是您的康复随访助手，今天打电话是想了解一下您昨天膝关节康复器的使用情况，请问您昨天做摆腿运动了吗？',
    retryCount: '2次',
    retryInterval: '1小时',
    autoExport: '每周导出',
    exportFormat: 'Excel'
  });

  const handleSave = () => {
    alert('设置已保存！');
  };

  return (
    <div>
      <div className="mb-8 pb-5 border-b">
        <h1 className="text-3xl font-bold text-slate-800">系统设置</h1>
      </div>

      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h2 className="text-xl font-semibold text-slate-800 mb-6">智能随访设置</h2>

        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium mb-3">随访时间设置</label>
            <div className="grid grid-cols-2 gap-4 max-w-md">
              <div>
                <label className="block text-sm text-gray-600 mb-1">开始时间</label>
                <input 
                  type="time" 
                  value={settings.startTime}
                  onChange={(e) => setSettings({...settings, startTime: e.target.value})}
                  className="w-full px-3 py-2 border rounded"
                />
              </div>
              <div>
                <label className="block text-sm text-gray-600 mb-1">结束时间</label>
                <input 
                  type="time" 
                  value={settings.endTime}
                  onChange={(e) => setSettings({...settings, endTime: e.target.value})}
                  className="w-full px-3 py-2 border rounded"
                />
              </div>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">首次入组随访话术</label>
            <textarea 
              value={settings.firstCallScript}
              onChange={(e) => setSettings({...settings, firstCallScript: e.target.value})}
              className="w-full px-3 py-2 border rounded h-24 resize-y"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">日常随访话术</label>
            <textarea 
              value={settings.dailyCallScript}
              onChange={(e) => setSettings({...settings, dailyCallScript: e.target.value})}
              className="w-full px-3 py-2 border rounded h-24 resize-y"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-3">未接通重试设置</label>
            <div className="grid grid-cols-2 gap-4 max-w-md">
              <div>
                <label className="block text-sm text-gray-600 mb-1">重试次数</label>
                <select 
                  value={settings.retryCount}
                  onChange={(e) => setSettings({...settings, retryCount: e.target.value})}
                  className="w-full px-3 py-2 border rounded"
                >
                  <option>1次</option>
                  <option>2次</option>
                  <option>3次</option>
                </select>
              </div>
              <div>
                <label className="block text-sm text-gray-600 mb-1">间隔时间</label>
                <select 
                  value={settings.retryInterval}
                  onChange={(e) => setSettings({...settings, retryInterval: e.target.value})}
                  className="w-full px-3 py-2 border rounded"
                >
                  <option>30分钟</option>
                  <option>1小时</option>
                  <option>2小时</option>
                </select>
              </div>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-3">数据导出设置</label>
            <div className="grid grid-cols-2 gap-4 max-w-md">
              <div>
                <label className="block text-sm text-gray-600 mb-1">自动导出</label>
                <select 
                  value={settings.autoExport}
                  onChange={(e) => setSettings({...settings, autoExport: e.target.value})}
                  className="w-full px-3 py-2 border rounded"
                >
                  <option>不自动导出</option>
                  <option>每日导出</option>
                  <option>每周导出</option>
                  <option>每月导出</option>
                </select>
              </div>
              <div>
                <label className="block text-sm text-gray-600 mb-1">导出格式</label>
                <select 
                  value={settings.exportFormat}
                  onChange={(e) => setSettings({...settings, exportFormat: e.target.value})}
                  className="w-full px-3 py-2 border rounded"
                >
                  <option>Excel</option>
                  <option>CSV</option>
                  <option>JSON</option>
                </select>
              </div>
            </div>
          </div>

          <div className="pt-4">
            <button 
              onClick={handleSave}
              className="px-6 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              保存设置
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// 患者详情模态框
const PatientDetailModal = ({ isOpen, onClose, patient }) => {
  if (!isOpen || !patient) return null;

  const progress = mockProgressData[patient._id] || {};
  const patientRecords = mockCallRecords.filter(r => r.手机号 === patient.phone);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-11/12 max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="p-5 border-b flex justify-between items-center sticky top-0 bg-white">
          <h2 className="text-2xl font-semibold">患者详情 - {patient.name}</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        <div className="p-5">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-5 mb-8">
            <div>
              <label className="text-sm text-gray-500 block mb-1">患者ID</label>
              <p className="font-medium">{patient._id.slice(-6).toUpperCase()}</p>
            </div>
            <div>
              <label className="text-sm text-gray-500 block mb-1">姓名</label>
              <p className="font-medium">{patient.name}</p>
            </div>
            <div>
              <label className="text-sm text-gray-500 block mb-1">性别</label>
              <p className="font-medium">{patient.gender}</p>
            </div>
            <div>
              <label className="text-sm text-gray-500 block mb-1">年龄</label>
              <p className="font-medium">{patient.age}岁</p>
            </div>
            <div>
              <label className="text-sm text-gray-500 block mb-1">手机号码</label>
              <p className="font-medium">{patient.phone}</p>
            </div>
            <div>
              <label className="text-sm text-gray-500 block mb-1">入组日期</label>
              <p className="font-medium">{patient.enrollment_date}</p>
            </div>
            <div>
              <label className="text-sm text-gray-500 block mb-1">当前天数</label>
              <p className="font-medium">{progress.currentDay || 0}/{progress.totalDays || 30}</p>
            </div>
            <div>
              <label className="text-sm text-gray-500 block mb-1">依从性</label>
              <p className="font-medium">{progress.compliance || 0}%</p>
            </div>
          </div>

          {progress.records && (
            <div className="mb-8">
              <h3 className="text-lg font-semibold mb-4">摆腿数据趋势</h3>
              <div className="bg-gray-50 p-4 rounded">
                <div className="grid grid-cols-7 gap-2">
                  {progress.records.map((record, index) => (
                    <div key={index} className="text-center">
                      <div className="text-xs text-gray-500 mb-1">{record.date.slice(5)}</div>
                      <div className={`h-20 flex items-end justify-center ${record.completed ? 'bg-blue-100' : 'bg-gray-100'} rounded`}>
                        <div 
                          className={`w-full ${record.completed ? 'bg-blue-500' : 'bg-gray-300'} rounded-t`}
                          style={{ height: `${(record.training / 40) * 100}%` }}
                        ></div>
                      </div>
                      <div className="text-xs mt-1">{record.training}次</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          <h3 className="text-lg font-semibold mb-4">通话记录</h3>
          <div className="space-y-4">
            {patientRecords.map((record) => (
              <div key={record._id} className="border rounded-lg p-4 bg-gray-50">
                <div className="flex justify-between mb-3">
                  <div className="font-semibold">{record.记录日期}</div>
                  <div className="text-gray-500">通话时间: {record.通话时间}</div>
                </div>
                {record.训练完成情况 && (
                  <div className="flex flex-wrap gap-2 mb-3">
                    <span className="px-2 py-1 bg-blue-100 text-blue-600 rounded text-sm">
                      {record.训练完成情况}
                    </span>
                    {record.训练次数 && (
                      <span className="px-2 py-1 bg-blue-100 text-blue-600 rounded text-sm">
                        摆腿{record.训练次数}
                      </span>
                    )}
                    {record.是否有不适感 === '是' && (
                      <span className="px-2 py-1 bg-red-100 text-red-600 rounded text-sm">
                        {record.不适感内容}
                      </span>
                    )}
                  </div>
                )}
                
                {record.对话历史记录 && (
                  <>
                    <div className="mb-3 p-3 bg-gray-100 rounded flex items-center justify-between">
                      <span className="text-sm">通话录音</span>
                      <button className="text-blue-500 hover:text-blue-600">
                        <PlayIcon className="w-5 h-5" />
                      </button>
                    </div>
                    <div className="max-h-48 overflow-y-auto border rounded p-3 bg-white text-sm">
                      {record.对话历史记录.map((chat, index) => (
                        <p key={index} className="mb-2">
                          <strong>{chat.role === 'assistant' ? '智能助手' : patient.name}:</strong> {chat.content}
                        </p>
                      ))}
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
        </div>

        <div className="p-5 border-t flex justify-end gap-3 sticky bottom-0 bg-white">
          <button className="px-4 py-2 bg-gray-100 text-slate-700 rounded hover:bg-gray-200">导出记录</button>
          <button className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">安排随访</button>
        </div>
      </div>
    </div>
  );
};

// 添加患者模态框
const AddPatientModal = ({ isOpen, onClose }) => {
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    age: '',
    gender: '男',
    enrollment_date: new Date().toISOString().split('T')[0],
    training_status: '未开始'
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('提交表单:', formData);
    alert('患者添加成功！');
    onClose();
  };

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-11/12 max-w-2xl">
        <div className="p-5 border-b flex justify-between items-center">
          <h2 className="text-2xl font-semibold">添加新患者</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-5">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium mb-2">姓名 *</label>
              <input 
                type="text" 
                name="name"
                value={formData.name}
                onChange={handleChange}
                className="w-full px-3 py-2 border rounded focus:outline-none focus:border-blue-500" 
                placeholder="请输入患者姓名" 
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">性别</label>
              <select 
                name="gender"
                value={formData.gender}
                onChange={handleChange}
                className="w-full px-3 py-2 border rounded focus:outline-none focus:border-blue-500"
              >
                <option value="男">男</option>
                <option value="女">女</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">年龄 *</label>
              <input 
                type="number" 
                name="age"
                value={formData.age}
                onChange={handleChange}
                className="w-full px-3 py-2 border rounded focus:outline-none focus:border-blue-500" 
                placeholder="请输入年龄" 
                required
              />
            </div>
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">联系电话 *</label>
            <input 
              type="tel" 
              name="phone"
              value={formData.phone}
              onChange={handleChange}
              className="w-full px-3 py-2 border rounded focus:outline-none focus:border-blue-500" 
              placeholder="请输入手机号码" 
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium mb-2">入组日期</label>
              <input 
                type="date" 
                name="enrollment_date"
                value={formData.enrollment_date}
                onChange={handleChange}
                className="w-full px-3 py-2 border rounded focus:outline-none focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">训练状态</label>
              <select 
                name="training_status"
                value={formData.training_status}
                onChange={handleChange}
                className="w-full px-3 py-2 border rounded focus:outline-none focus:border-blue-500"
              >
                <option value="未开始">未开始</option>
                <option value="训练中">训练中</option>
                <option value="暂停">暂停</option>
                <option value="终止">终止</option>
                <option value="休息">休息</option>
              </select>
            </div>
          </div>

          <div className="p-5 border-t flex justify-end gap-3 -mx-5 -mb-5 mt-5">
            <button 
              type="button"
              onClick={onClose} 
              className="px-4 py-2 bg-gray-100 text-slate-700 rounded hover:bg-gray-200"
            >
              取消
            </button>
            <button 
              type="submit"
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              保存
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// 主应用组件
const App = () => {
  const [activeMenu, setActiveMenu] = useState('dashboard');
  const [showPatientModal, setShowPatientModal] = useState(false);
  const [showAddPatientModal, setShowAddPatientModal] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState(null);

  const renderPage = () => {
    const pageProps = {
      setShowPatientModal,
      setShowAddPatientModal,
      setSelectedPatient
    };

    switch (activeMenu) {
      case 'dashboard':
        return <DashboardPage {...pageProps} />;
      case 'patients':
        return <PatientsPage {...pageProps} />;
      case 'progress':
        return <ProgressPage />;
      case 'recordings':
        return <RecordingsPage />;
      case 'insights':
        return <InsightsPage />;
      case 'settings':
        return <SettingsPage />;
      default:
        return <DashboardPage {...pageProps} />;
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar activeMenu={activeMenu} setActiveMenu={setActiveMenu} />
      <div className="flex-1 overflow-y-auto p-5">
        {renderPage()}
      </div>
      <PatientDetailModal 
        isOpen={showPatientModal} 
        onClose={() => {
          setShowPatientModal(false);
          setSelectedPatient(null);
        }}
        patient={selectedPatient}
      />
      <AddPatientModal 
        isOpen={showAddPatientModal} 
        onClose={() => setShowAddPatientModal(false)} 
      />
    </div>
  );
};

export default App;
