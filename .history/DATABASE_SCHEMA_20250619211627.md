# 数据库结构文档

> 基于实际数据库内容分析生成
> 生成时间: 2025-06-19 19:14:11
> 数据库名称: med_call_records

## 概述

本项目使用MongoDB作为数据存储解决方案，主要用于管理医疗随访系统中的患者信息、通话记录、用户账户和智能对话缓存。数据库包含5个主要集合。

## 集合概览

| 集合名称 | 文档数量 | 主要用途 |
|---------|---------|---------|
| `call_records` | 155 | 存储通话记录和患者训练情况 |
| `personnel` | 4 | 存储患者基本信息 |
| `sys_info` | 7 | 存储系统配置和用户账户信息 |
| `reply_cache` | 1 | 存储智能对话模板（正则表达式版本） |
| `reply_cache_str` | 1 | 存储智能对话模板（字符串匹配版本） |

## 数据库连接配置

在 `backend/config.py` 中配置:
```python
MONGO_URL = 'mongodb://localhost:27017'
DATABASE_NAME = 'med_call_records'
```

## 详细集合结构

### 1. call_records - 通话记录集合

**用途**: 存储每次通话的详细记录和患者训练情况

**主要字段**:
- `_id`: ObjectId - 文档唯一标识
- `患者名字`: String - 患者姓名
- `手机号`: String - 患者手机号
- `通话时间`: String - 通话开始时间 (YYYY-MM-DD HH:MM)
- `记录日期`: String - 记录日期 (YYYY-MM-DD)
- `拨号状态`: String - 通话状态 ("拨号中", "无法接通", "通话结束", "通话结束-信息提取")
- `对话历史记录`: Array - 完整对话记录
- `训练完成情况`: String - 训练是否完成及原因
- `训练次数`: String - 训练次数
- `训练时长`: String - 训练持续时间
- `是否有不适感`: String - 是否有不适症状 ("是"/"否")
- `不适感内容`: String - 具体不适症状描述
- `是否需要医生人工和患者联系`: String - 是否需要医生介入 ("是"/"否")
- `锻炼辅助仪器是否有问题`: String - 设备是否正常 ("是"/"否")
- `锻炼辅助仪器问题内容`: String - 设备问题描述
- `创建时间`: DateTime - 记录创建时间

**示例文档**:
```json
{
  "_id": "6841038f24953c4c3a058f6f",
  "患者名字": "张三",
  "手机号": "***********",
  "通话时间": "2025-06-05 10:40",
  "记录日期": "2025-06-05",
  "拨号状态": "拨号中",
  "创建时间": "2025-06-05T10:40:15.265000",
  "对话历史记录": [
    {
      "role": "assistant",
      "content": "您好，我是武汉协和医院骨科的智能助理，请问是张三吗？"
    }
  ]
}
```

### 2. personnel - 患者信息集合

**用途**: 存储需要电话随访的患者基本信息

**主要字段**:
- `_id`: ObjectId - 文档唯一标识
- `patient_id`: String - 患者编号，由医生填入，唯一标识 (如: P0001, P0002)
- `name`: String - 患者姓名
- `phone`: String - 手机号码，唯一标识
- `age`: Number - 年龄
- `gender`: String - 性别 ("男"/"女")
- `enrollment_date`: String - 入组时间 (YYYY-MM-DD)
- `training_status`: String - 当前训练状态
- `assigned_doctor`: String - 分配的医生用户名
- `assigned_doctor_name`: String - 分配的医生姓名
- `assignment_date`: String - 医生分配日期 (YYYY-MM-DD)
- `created_at`: DateTime - 记录创建时间
- `updated_at`: DateTime - 记录更新时间

**训练状态值**:
- "训练中" - 正在进行康复训练
- "未开始" - 尚未开始训练
- "暂停" - 暂停训练
- "终止" - 终止训练
- "休息" - 休息中

**示例文档**:
```json
{
  "_id": "683d2b16fa75c786fa6d7344",
  "patient_id": "P0001",
  "name": "张三",
  "phone": "***********",
  "age": 35,
  "gender": "男",
  "enrollment_date": "2024-01-15",
  "training_status": "训练中",
  "assigned_doctor": "doctor01",
  "assigned_doctor_name": "李医生",
  "assignment_date": "2025-06-19",
  "created_at": "2025-06-02T12:39:50.702000",
  "updated_at": "2025-06-19T10:00:00.000000"
}
```

### 3. sys_info - 系统信息集合

**用途**: 存储系统配置信息和用户账户

**主要字段**:
- `_id`: ObjectId - 文档唯一标识
- `doc_type`: String - 文档类型 ("user_account", "system_config")
- `username`: String - 用户名
- `password_hash`: String - 密码哈希值
- `role`: String - 用户角色 ("管理员", "医生")
- `full_name`: String - 用户全名
- `email`: String - 邮箱地址
- `phone`: String - 电话号码
- `is_active`: Boolean - 账户是否激活
- `login_count`: Number - 登录次数
- `last_login`: DateTime - 最后登录时间
- `created_at`: DateTime - 账户创建时间
- `updated_at`: DateTime - 账户更新时间

**系统配置字段** (当doc_type为system_config时):
- `system_name`: String - 系统名称
- `version`: String - 系统版本
- `status`: String - 系统状态
- `call_time_slots`: Array - 通话时间段配置
- `max_concurrent_calls`: Number - 最大并发通话数
- `batch_size`: Number - 批处理大小
- `retry_times`: Number - 重试次数

### 4. reply_cache - 智能对话缓存集合

**用途**: 存储智能对话系统的问题模板和回复规则（正则表达式版本）

**主要字段**:
- `_id`: String - 固定值 "dialogue_cache"
- `question_templates`: Array - 问题模板数组
- `stats`: Object - 统计信息

**问题模板结构**:
```json
{
  "id": "唯一标识",
  "question_pattern": "问题正则表达式模式",
  "reply_templates": [
    {
      "id": "回复模板ID",
      "user_reply_pattern": "用户回复匹配模式",
      "next_question": "下一个问题",
      "variables": {},
      "hit_count": 0
    }
  ],
  "tags": ["标签"],
  "enabled": true,
  "created_at": "创建时间"
}
```

### 5. reply_cache_str - 字符串对话缓存集合

**用途**: 存储智能对话系统的问题模板和回复规则（字符串匹配版本）

**主要字段**:
- `_id`: String - 固定值 "dialogue_cache_str"
- `question_templates`: Array - 问题模板数组（字符串匹配版本）

## 索引建议

为提高查询性能，建议创建以下索引：

```javascript
// 患者信息集合索引
db.personnel.createIndex({"phone": 1}, {unique: true})
db.personnel.createIndex({"patient_id": 1}, {unique: true})
db.personnel.createIndex({"name": 1})
db.personnel.createIndex({"training_status": 1})
db.personnel.createIndex({"assigned_doctor": 1})

// 通话记录集合索引
db.call_records.createIndex({"手机号": 1})
db.call_records.createIndex({"患者名字": 1})
db.call_records.createIndex({"记录日期": 1})
db.call_records.createIndex({"通话时间": -1})
db.call_records.createIndex({"是否需要医生人工和患者联系": 1})
db.call_records.createIndex({"是否有不适感": 1})

// 系统信息集合索引
db.sys_info.createIndex({"doc_type": 1})
db.sys_info.createIndex({"username": 1}, {unique: true})
```

## 数据统计

- **总文档数**: 168个文档
- **主要业务数据**: 通话记录155条，患者信息4条
- **系统数据**: 用户账户和配置7条，对话模板2条
- **数据时间范围**: 2024年1月至2025年6月

## 注意事项

1. **字段命名**: 通话记录集合使用中文字段名，需要在代码中正确处理
2. **时间格式**: 混合使用字符串和DateTime类型存储时间
3. **数据一致性**: 患者姓名和手机号在不同集合中需要保持一致
4. **对话模板**: 分为正则表达式和字符串匹配两个版本，用于不同的匹配策略
5. **医生-患者关系**: 通过personnel集合中的assigned_doctor字段建立，支持一对多关系（一个医生管理多个患者）

