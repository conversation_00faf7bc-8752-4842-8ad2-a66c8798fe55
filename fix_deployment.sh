#!/bin/bash

echo "=== 修复部署问题脚本 ==="
echo "问题：前端无法连接到后端API"
echo "解决方案：修改API地址配置和CORS设置"
echo ""

echo "1. 重新构建前端..."
echo "cd /path/to/your/project"
echo "npm run build"
echo ""

echo "2. 重启后端服务..."
echo "cd backend"
echo "# 停止现有的Python进程"
echo "pkill -f 'python.*smart_start.py'"
echo "# 或者在运行后端的终端按 Ctrl+C 停止"
echo ""

echo "3. 重新启动后端..."
echo "python smart_start.py"
echo ""

echo "4. 验证修复..."
echo "# 检查后端是否在 0.0.0.0:5000 监听"
echo "# 浏览器访问：http://服务器IP:5173"
echo "# 登录时前端会自动连接到：http://服务器IP:5000/api"
echo ""

echo "=== 已修复的问题 ==="
echo "✅ 前端API地址现在会根据访问的主机动态配置"
echo "✅ 后端CORS配置允许跨域访问"
echo "✅ 后端监听所有网络接口 (0.0.0.0:5000)"
echo ""

echo "如果问题仍然存在，请检查："
echo "- 服务器防火墙是否开放5000端口"
echo "- 网络是否可以从浏览器访问服务器的5000端口"
echo "- 浏览器控制台是否有其他错误信息" 