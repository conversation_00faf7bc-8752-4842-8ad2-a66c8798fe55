#!/bin/bash

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKEND_DIR="/home/<USER>/workspace/suifangweb/backend"
FRONTEND_DIR="/home/<USER>/workspace/suifangweb"

echo "=== 服务状态检查 ==="
echo ""

# 检查后端服务
echo "后端服务 (smart_start.py):"
if [ -f "$SCRIPT_DIR/backend.pid" ]; then
    PID=$(cat "$SCRIPT_DIR/backend.pid")
    if ps -p "$PID" > /dev/null 2>&1; then
        echo "  状态: 运行中 (PID: $PID)"
        echo "  端口: 5000"
        echo "  工作目录: $BACKEND_DIR"
        echo "  内存使用: $(ps -o pid,vsz,rss,comm -p $PID | tail -1)"
        echo "  运行时间: $(ps -o pid,etime,comm -p $PID | tail -1)"
    else
        echo "  状态: 未运行 (PID文件存在但进程不存在)"
    fi
else
    echo "  状态: 未运行"
fi

echo ""

# 检查前端服务
echo "前端服务 (server.py):"
if [ -f "$SCRIPT_DIR/frontend.pid" ]; then
    PID=$(cat "$SCRIPT_DIR/frontend.pid")
    if ps -p "$PID" > /dev/null 2>&1; then
        echo "  状态: 运行中 (PID: $PID)"
        echo "  端口: 5173"
        echo "  工作目录: $FRONTEND_DIR"
        echo "  内存使用: $(ps -o pid,vsz,rss,comm -p $PID | tail -1)"
        echo "  运行时间: $(ps -o pid,etime,comm -p $PID | tail -1)"
    else
        echo "  状态: 未运行 (PID文件存在但进程不存在)"
    fi
else
    echo "  状态: 未运行"
fi

echo ""

# 检查frpc服务
echo "frpc服务:"
if [ -f "$SCRIPT_DIR/frpc.pid" ]; then
    PID=$(cat "$SCRIPT_DIR/frpc.pid")
    if ps -p "$PID" > /dev/null 2>&1; then
        echo "  状态: 运行中 (PID: $PID)"
        echo "  工作目录: $SCRIPT_DIR"
        echo "  内存使用: $(ps -o pid,vsz,rss,comm -p $PID | tail -1)"
        echo "  运行时间: $(ps -o pid,etime,comm -p $PID | tail -1)"
    else
        echo "  状态: 未运行 (PID文件存在但进程不存在)"
    fi
else
    echo "  状态: 未运行"
fi

echo ""

# 检查端口占用
echo "端口占用情况:"
PORT_5000=$(ss -tlnp | grep :5000)
PORT_5173=$(ss -tlnp | grep :5173)

if [ -n "$PORT_5000" ]; then
    echo "  端口5000: $PORT_5000"
else
    echo "  端口5000: 未占用"
fi

if [ -n "$PORT_5173" ]; then
    echo "  端口5173: $PORT_5173"
else
    echo "  端口5173: 未占用"
fi

echo ""

# 检查目录存在性
echo "目录检查:"
echo "  后端目录 ($BACKEND_DIR): $([ -d "$BACKEND_DIR" ] && echo '存在' || echo '不存在')"
echo "  前端目录 ($FRONTEND_DIR): $([ -d "$FRONTEND_DIR" ] && echo '存在' || echo '不存在')"
echo "  后端脚本: $([ -f "$BACKEND_DIR/smart_start.py" ] && echo '存在' || echo '不存在')"
echo "  前端脚本: $([ -f "$FRONTEND_DIR/server.py" ] && echo '存在' || echo '不存在')"

echo ""

# 显示最近的日志
echo "最近日志 (最后3行):"
if [ -f "$SCRIPT_DIR/backend.log" ]; then
    echo "  后端日志:"
    tail -3 "$SCRIPT_DIR/backend.log" | sed 's/^/    /'
    echo ""
fi

if [ -f "$SCRIPT_DIR/frontend.log" ]; then
    echo "  前端日志:"
    tail -3 "$SCRIPT_DIR/frontend.log" | sed 's/^/    /'
    echo ""
fi

if [ -f "$SCRIPT_DIR/frpc.log" ]; then
    echo "  frpc日志:"
    tail -3 "$SCRIPT_DIR/frpc.log" | sed 's/^/    /'
fi
