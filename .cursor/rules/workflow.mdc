---
description: 
globs: 
alwaysApply: false
---
# 开发工作流程指南

## 项目启动和开发

### 本地开发环境
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 类型检查
npm run build

# 代码检查
npm run lint
```

### 关键开发文件
- [src/App.tsx](mdc:src/App.tsx) - 主应用组件，包含路由和布局
- [src/main.tsx](mdc:src/main.tsx) - React应用入口点
- [src/mock.js](mdc:src/mock.js) - 模拟数据，用于开发阶段
- [vite.config.ts](mdc:vite.config.ts) - Vite构建配置
- [tailwind.config.js](mdc:tailwind.config.js) - Tailwind CSS配置

## 代码组织规范

### 目录结构最佳实践
```
backend/     # 系统后端代码目录,使用Python+Flask+Mongodb

src/
├── components/     # 可复用UI组件
├── pages/         # 页面级组件
├── hooks/         # 自定义React Hooks
├── lib/           # 工具函数和配置
├── types/         # TypeScript类型定义
├── assets/        # 静态资源
└── mock.js        # 开发用模拟数据

```

### 新功能开发流程
1. **需求分析** - 理解业务需求和用户场景
2. **类型定义** - 在 [src/types/](mdc:src/types) 中定义相关类型
3. **API接口** - 设计和实现数据接口
4. **组件开发** - 开发UI组件和页面
5. **状态管理** - 使用React Query管理数据状态
6. **样式实现** - 使用Tailwind CSS和Radix UI
7. **测试验证** - 功能测试和代码review

## 数据处理规范

### Mock数据使用
开发阶段使用 [src/mock.js](mdc:src/mock.js) 中的模拟数据：
- personnel数组模拟患者信息
- call_records数组模拟通话记录
- 数据结构要与MongoDB schema保持一致

### API集成准备
- 使用Axios处理HTTP请求
- 实现统一的错误处理机制
- 使用React Query进行数据缓存和状态管理
- API端点配置要集中管理

## UI组件开发

### Radix UI组件使用
项目使用shadcn/ui组件库，配置在 [components.json](mdc:components.json)：
- Dialog - 模态框组件
- Toast - 消息提示组件
- Select - 下拉选择组件
- Tabs - 标签页组件
- Avatar - 头像组件

### Tailwind CSS使用
- 使用配置文件 [tailwind.config.js](mdc:tailwind.config.js) 自定义主题
- 响应式设计使用 `sm:` `md:` `lg:` `xl:` 前缀
- 颜色系统基于设计规范
- 组件样式要保持一致性

## 性能优化策略

### React优化
- 使用React.memo优化组件重渲染
- 合理使用useMemo和useCallback
- 懒加载页面组件和大型库
- 避免不必要的状态更新

### 数据加载优化
- 实现分页加载大量数据
- 使用React Query缓存机制
- 图表数据按需加载
- 地图组件延迟初始化

### 构建优化
- Vite配置优化代码分割
- 静态资源压缩和缓存
- TypeScript编译优化
- 生产环境移除开发工具

## 调试和故障排除

### 常见问题解决
- TypeScript类型错误检查
- ESLint规则冲突解决
- Tailwind CSS样式问题
- React Query缓存问题
- 组件状态同步问题

### 开发工具使用
- React Developer Tools
- 浏览器开发者工具
- Vite HMR热更新
- TypeScript编译器检查