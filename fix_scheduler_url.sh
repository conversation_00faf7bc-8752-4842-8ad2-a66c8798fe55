#!/bin/bash

echo "=== 修复调度器URL问题 ==="
echo ""

echo "问题分析："
echo "❌ 前端请求URL: http://192.168.3.12:5173/api/scheduler/start"
echo "✅ 应该请求: http://114.132.154.140:5000/api/scheduler/start"
echo ""

echo "已修复的问题："
echo "✅ 调度器服务现在使用硬编码的后端服务器地址"
echo "✅ 移除了动态URL生成，避免相对路径问题"
echo "✅ 添加了调试信息，可在浏览器控制台查看实际URL"
echo ""

echo "需要执行的步骤："
echo "1. 重新构建前端"
echo "   cd /path/to/your/project"
echo "   npm run build"
echo ""

echo "2. 刷新浏览器页面"
echo "   - 打开开发者工具控制台"
echo "   - 查看是否显示: '🔧 SchedulerService 初始化，baseUrl: http://114.132.154.140:5000/api/scheduler'"
echo ""

echo "3. 测试调度器状态"
echo "   - 在AI电话机器人管理界面"
echo "   - 查看状态是否正常显示"
echo ""

echo "4. 验证网络请求"
echo "   - 在浏览器开发者工具的Network标签"
echo "   - 查看请求是否发送到 114.132.154.140:5000"
echo ""

echo "如果仍有问题，请检查："
echo "- 浏览器缓存是否清除"
echo "- 前端是否重新构建"
echo "- 后端服务是否在5000端口正常运行" 