from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
import uvicorn
import os

app = FastAPI()

# 挂载静态文件目录
app.mount("/static", StaticFiles(directory="dist"), name="static")

@app.get("/")
async def read_root():
    return FileResponse('dist/index.html')

@app.get("/{path:path}")
async def serve_spa(path: str):
    """处理SPA路由，所有路径都返回index.html"""
    file_path = os.path.join("dist", path)
    if os.path.exists(file_path) and os.path.isfile(file_path):
        return FileResponse(file_path)
    else:
        # SPA fallback - 返回index.html让前端路由处理
        return FileResponse('dist/index.html')

if __name__ == "__main__":
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=5173,
        # workers=4,  # 多进程
        loop="uvloop",  # 使用更快的事件循环
        log_level="info"
    )