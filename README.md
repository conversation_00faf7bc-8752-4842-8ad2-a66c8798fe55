# 医疗随访管理系统

基于 React + TypeScript + Vite + Flask + MongoDB 的智能随访管理系统，用于管理患者信息和AI电话随访记录。

## 项目架构

### 前端技术栈
- **前端框架**: React 18.3.1 + TypeScript
- **构建工具**: Vite
- **样式方案**: Tailwind CSS + Radix UI组件库
- **状态管理**: React Query用于数据获取和缓存
- **路由**: React Router DOM
- **表单处理**: React Hook Form + Zod验证
- **图表库**: Recharts + Chart.js
- **HTTP请求**: Fetch API

### 后端技术栈
- **后端框架**: Python + Flask
- **数据库**: MongoDB
- **异步驱动**: motor (异步MongoDB驱动)
- **跨域支持**: Flask-CORS

## 功能特性

- 📊 **控制面板** - 实时统计数据和今日随访任务
- 👥 **患者管理** - 患者信息的增删改查和档案管理
- 📈 **康复进度** - 可视化展示患者康复数据和依从性
- 📞 **通话记录** - AI智能通话记录管理和质量评估
- 🧠 **智能摘要** - 自动分析通话内容生成摘要报告
- ⚙️ **系统设置** - 随访话术和系统参数配置

## 项目结构

```
├── frontend/
│   ├── src/
│   │   ├── components/     # React组件
│   │   ├── pages/         # 页面组件
│   │   ├── services/      # API服务
│   │   ├── types/         # TypeScript类型定义
│   │   └── App.tsx        # 主应用组件
│   ├── package.json
│   └── vite.config.ts
├── backend/
│   ├── app.py            # Flask主应用
│   ├── mock_data.py      # Mock数据
│   ├── requirements.txt  # Python依赖
│   ├── start.py         # 启动脚本
│   └── README.md        # 后端文档
└── README.md
```

## 快速开始

### 环境要求

- Node.js 16+
- Python 3.8+
- MongoDB 4.4+

### 1. 克隆项目

```bash
git clone <repository-url>
cd SuifangWeb
```

### 2. 启动后端服务

```bash
# 进入后端目录
cd backend

# 安装Python依赖
pip install -r requirements.txt

# 启动MongoDB服务（需要先安装MongoDB）
# macOS: brew services start mongodb-community
# Ubuntu: sudo systemctl start mongod
# Windows: 启动MongoDB服务

# 启动后端服务
python app.py
# 或使用启动脚本（会进行环境检查）
python start.py
```

后端服务将在 `http://localhost:5000` 启动

### 3. 启动前端服务

```bash
# 安装前端依赖
npm install

# 启动开发服务器
npm run dev
```

前端服务将在 `http://localhost:5173` 启动

## API 接口

### 基础URL
```
http://localhost:5000/api
```

### 主要接口

#### 患者管理
- `GET /personnel` - 获取患者列表
- `POST /personnel` - 添加新患者
- `GET /personnel/:id` - 获取患者详情

#### 通话记录
- `GET /call-records` - 获取通话记录列表

#### 统计数据
- `GET /stats` - 获取统计数据

#### 康复进度
- `GET /progress` - 获取康复进度数据

#### 智能摘要
- `GET /insights` - 获取智能摘要数据

详细API文档请参考 [backend/README.md](backend/README.md)

## 数据库设计

详细的数据库模式参考 [DATABASE_SCHEMA.md](DATABASE_SCHEMA.md)

主要集合：
- **personnel** - 患者基本信息
- **call_records** - 通话记录和训练情况

## 开发指南

### 前端开发

```bash
# 开发模式
npm run dev

# 构建生产版本
npm run build

# 预览构建结果
npm run preview

# 代码检查
npm run lint
```

### 后端开发

```bash
# 开发模式（自动重载）
python app.py

# 生产模式
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

## 环境变量配置

### 后端环境变量

```bash
# MongoDB连接地址
export MONGO_URL=mongodb://localhost:27017

# 数据库名称
export DATABASE_NAME=medical_followup
```

## 部署说明

### 前端部署

构建前端资源：
```bash
npm run build
```

部署 `dist/` 目录到静态文件服务器（如 Nginx, Apache 等）

### 后端部署

使用 Gunicorn 部署：
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

或使用 Docker：
```dockerfile
FROM python:3.9
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "app:app"]
```

## 注意事项

1. **数据初始化**: 首次运行后端时会自动插入Mock数据
2. **跨域配置**: 开发环境已配置CORS，生产环境需要适当调整
3. **数据库连接**: 确保MongoDB服务正常运行
4. **API调用**: 前端默认连接到 `http://localhost:5000`，部署时需要修改API_BASE_URL

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

[MIT License](LICENSE)
