import path from "path"
import react from "@vitejs/plugin-react"
import { defineConfig, loadEnv } from "vite"

export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')
  
  // 后端API服务器配置
  const API_HOST = env.VITE_API_HOST || '***************'
  const API_PORT = env.VITE_API_PORT || '5000'
  const API_TARGET = `http://${API_HOST}:${API_PORT}`

  return {
    plugins: [react()],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    // 定义全局变量，可在代码中使用
    define: {
      __API_HOST__: JSON.stringify(API_HOST),
      __API_PORT__: JSON.stringify(API_PORT),
      __API_BASE_URL__: JSON.stringify(`${API_TARGET}/api`),
    },
    build: {
      // 忽略构建警告
      rollupOptions: {
        onwarn(warning, warn) {
          // 忽略未使用变量的警告
          if (warning.code === 'UNUSED_EXTERNAL_IMPORT') return
          if (warning.code === 'CIRCULAR_DEPENDENCY') return
          warn(warning)
        },
      }
    },
    server: {
      host: '0.0.0.0', // 允许外部访问
      port: 5173,      // 默认端口，可以修改
      proxy: {
        '/api': {
          target: API_TARGET,
          changeOrigin: true,
          secure: false,
        },
      },
    },
  }
})
