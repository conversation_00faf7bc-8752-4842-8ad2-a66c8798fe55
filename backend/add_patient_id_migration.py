#!/usr/bin/env python3
"""
数据库迁移脚本：为现有患者添加patient_id字段
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from database import get_db
from datetime import datetime

def generate_patient_id(index, name, phone):
    """生成患者编号
    格式：P + 4位数字编号
    例如：P0001, P0002, P0003...
    """
    return f"P{index:04d}"

def add_patient_id_to_existing_patients():
    """为现有患者添加patient_id字段"""
    print("🔄 开始为现有患者添加patient_id字段...")
    
    db = get_db()
    
    # 获取所有没有patient_id字段的患者
    patients_without_id = list(db.personnel.find({
        "patient_id": {"$exists": False}
    }).sort("created_at", 1))  # 按创建时间排序
    
    if not patients_without_id:
        print("✅ 所有患者都已经有patient_id字段")
        return
    
    print(f"📋 找到 {len(patients_without_id)} 个需要添加patient_id的患者")
    
    # 获取现有的最大patient_id编号
    existing_patients_with_id = list(db.personnel.find({
        "patient_id": {"$exists": True}
    }))
    
    max_number = 0
    if existing_patients_with_id:
        for patient in existing_patients_with_id:
            patient_id = patient.get('patient_id', '')
            if patient_id.startswith('P') and len(patient_id) == 5:
                try:
                    number = int(patient_id[1:])
                    max_number = max(max_number, number)
                except ValueError:
                    continue
    
    print(f"📊 当前最大编号: P{max_number:04d}")
    
    # 为每个患者分配编号
    updated_count = 0
    for i, patient in enumerate(patients_without_id, start=max_number + 1):
        patient_id = generate_patient_id(i, patient['name'], patient['phone'])
        
        # 检查编号是否已存在（避免重复）
        existing = db.personnel.find_one({"patient_id": patient_id})
        while existing:
            i += 1
            patient_id = generate_patient_id(i, patient['name'], patient['phone'])
            existing = db.personnel.find_one({"patient_id": patient_id})
        
        # 更新患者记录
        result = db.personnel.update_one(
            {"_id": patient["_id"]},
            {
                "$set": {
                    "patient_id": patient_id,
                    "updated_at": datetime.utcnow()
                }
            }
        )
        
        if result.modified_count > 0:
            print(f"✅ 患者 {patient['name']} ({patient['phone']}) -> {patient_id}")
            updated_count += 1
        else:
            print(f"❌ 更新患者 {patient['name']} 失败")
    
    print(f"\n📊 迁移完成: 成功为 {updated_count} 个患者添加了patient_id")
    return updated_count

def verify_patient_ids():
    """验证患者编号的唯一性和格式"""
    print("\n🔍 验证患者编号...")
    
    db = get_db()
    
    # 获取所有患者
    all_patients = list(db.personnel.find({}))
    
    # 检查是否所有患者都有patient_id
    patients_without_id = [p for p in all_patients if 'patient_id' not in p]
    if patients_without_id:
        print(f"❌ 还有 {len(patients_without_id)} 个患者没有patient_id")
        for patient in patients_without_id:
            print(f"   - {patient['name']} ({patient['phone']})")
        return False
    
    # 检查patient_id的唯一性
    patient_ids = [p['patient_id'] for p in all_patients]
    unique_ids = set(patient_ids)
    
    if len(patient_ids) != len(unique_ids):
        print("❌ 发现重复的patient_id")
        duplicates = [pid for pid in patient_ids if patient_ids.count(pid) > 1]
        print(f"   重复的编号: {set(duplicates)}")
        return False
    
    # 检查patient_id格式
    invalid_format = []
    for patient in all_patients:
        patient_id = patient['patient_id']
        if not (patient_id.startswith('P') and len(patient_id) == 5 and patient_id[1:].isdigit()):
            invalid_format.append(f"{patient['name']}: {patient_id}")
    
    if invalid_format:
        print("❌ 发现格式不正确的patient_id")
        for item in invalid_format:
            print(f"   - {item}")
        return False
    
    print("✅ 所有患者编号验证通过")
    print(f"📊 总计 {len(all_patients)} 个患者，编号范围: {min(patient_ids)} - {max(patient_ids)}")
    
    return True

def show_patient_list():
    """显示患者列表"""
    print("\n📋 当前患者列表:")
    
    db = get_db()
    patients = list(db.personnel.find({}).sort("patient_id", 1))
    
    print("| 编号   | 姓名   | 手机号       | 分配医生     | 医生姓名   |")
    print("|--------|--------|--------------|--------------|------------|")
    
    for patient in patients:
        patient_id = patient.get('patient_id', '未设置')
        name = patient.get('name', '')
        phone = patient.get('phone', '')
        assigned_doctor = patient.get('assigned_doctor', '未分配')
        doctor_name = patient.get('assigned_doctor_name', '未分配')
        
        print(f"| {patient_id:<6} | {name:<6} | {phone:<12} | {assigned_doctor:<12} | {doctor_name:<10} |")

def create_patient_id_index():
    """为patient_id字段创建唯一索引"""
    print("\n🔧 创建patient_id索引...")
    
    db = get_db()
    
    try:
        # 创建唯一索引
        db.personnel.create_index("patient_id", unique=True, name="patient_id_unique")
        print("✅ patient_id唯一索引创建成功")
    except Exception as e:
        print(f"❌ 创建索引失败: {e}")

def main():
    """主函数"""
    print("🚀 开始患者编号迁移...")
    
    try:
        # 1. 为现有患者添加patient_id
        updated_count = add_patient_id_to_existing_patients()
        
        # 2. 验证患者编号
        if verify_patient_ids():
            print("✅ 患者编号迁移和验证成功")
        else:
            print("❌ 患者编号验证失败")
            return
        
        # 3. 显示患者列表
        show_patient_list()
        
        # 4. 创建索引
        create_patient_id_index()
        
        print("\n" + "="*60)
        print("🎉 患者编号迁移完成！")
        print("="*60)
        print("📝 迁移总结:")
        print(f"   - 为 {updated_count} 个患者添加了编号")
        print("   - 所有编号格式正确且唯一")
        print("   - 创建了patient_id唯一索引")
        print("\n💡 下一步:")
        print("   - 修改前端添加患者表单，增加编号输入框")
        print("   - 修改后端API，支持patient_id字段")
        print("   - 更新数据库结构文档")
        
    except Exception as e:
        print(f"❌ 迁移过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
