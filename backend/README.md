# 医疗随访管理系统后端

基于 Flask + MongoDB 的后端API服务，提供患者管理、通话记录、统计数据等功能。

## 环境要求

- Python 3.8+
- MongoDB 4.4+
- pip

## 安装和运行

### 1. 安装依赖

```bash
cd backend
pip install -r requirements.txt
```

### 2. 启动 MongoDB

确保 MongoDB 服务正在运行。默认连接地址为 `mongodb://localhost:27017`

### 3. 运行后端服务

```bash
python app.py
```

服务将在 `http://localhost:5000` 启动。

## 环境变量配置

可以通过环境变量配置MongoDB连接：

```bash
export MONGO_URL=mongodb://localhost:27017
```

## API 接口

### 患者管理
- `GET /api/personnel` - 获取患者列表
- `POST /api/personnel` - 添加新患者
- `GET /api/personnel/:id` - 获取患者详情

### 通话记录
- `GET /api/call-records` - 获取通话记录列表

### 统计数据
- `GET /api/stats` - 获取统计数据

### 康复进度
- `GET /api/progress` - 获取康复进度数据

### 智能摘要
- `GET /api/insights` - 获取智能摘要数据

### 健康检查
- `GET /api/health` - 服务健康检查

## 数据库结构

### personnel 集合
- `_id`: ObjectId
- `name`: 患者姓名
- `phone`: 联系电话
- `age`: 年龄
- `gender`: 性别
- `enrollment_date`: 入组日期
- `training_status`: 训练状态
- `created_at`: 创建时间
- `updated_at`: 更新时间

### call_records 集合
- `_id`: ObjectId
- `patient_name`: 患者姓名
- `phone`: 联系电话
- `call_time`: 通话时间
- `record_date`: 记录日期
- `conversation_history`: 对话历史记录
- `training_completion`: 训练完成情况
- `training_times`: 训练次数
- `training_duration`: 训练时长
- `has_discomfort`: 是否有不适感
- `discomfort_content`: 不适感内容
- `needs_doctor_contact`: 是否需要医生联系
- `device_has_problem`: 设备是否有问题
- `device_problem_content`: 设备问题内容
- `created_at`: 创建时间

## 开发说明

- 使用 Flask 作为 Web 框架
- 使用 motor 作为异步 MongoDB 驱动
- 支持 CORS 跨域请求
- 自动初始化 mock 数据

## 注意事项

- 首次运行时会自动插入 mock 数据
- 确保 MongoDB 服务正常运行
- API 响应格式统一为 `{ success: boolean, data: any, error?: string }` 