from flask import Flask, jsonify, request
from flask_cors import CORS
import pymongo
import json
from datetime import datetime, timedelta
from bson import ObjectId
import os
import jwt
import bcrypt
from functools import wraps

import logging

app = Flask(__name__)

# 配置日志
logging.basicConfig(level=logging.DEBUG)
app.logger.setLevel(logging.DEBUG)

# JWT配置
app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'your-secret-key-change-in-production')
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)

# 或者更详细的日志配置
# handler = logging.FileHandler('app.log')
# handler.setLevel(logging.ERROR)
# app.logger.addHandler(handler)

CORS(app)

# MongoDB配置
MONGO_URL = os.getenv('MONGO_URL', 'mongodb://localhost:27017')
DATABASE_NAME = 'med_call_records'

client = pymongo.MongoClient(MONGO_URL)
db = client[DATABASE_NAME]

# 处理ObjectId和datetime的JSON序列化
def serialize_doc(doc):
    """将MongoDB文档转换为可序列化的字典"""
    if isinstance(doc, dict):
        result = {}
        for key, value in doc.items():
            if isinstance(value, ObjectId):
                result[key] = str(value)
            elif isinstance(value, datetime):
                result[key] = value.isoformat()
            elif isinstance(value, list):
                result[key] = [serialize_doc(item) for item in value]
            elif isinstance(value, dict):
                result[key] = serialize_doc(value)
            else:
                result[key] = value
        return result
    return doc

def serialize_user(user_doc):
    """将用户文档转换为前端所需格式，将_id转换为id"""
    if not user_doc:
        return None
    
    result = serialize_doc(user_doc)
    if '_id' in result:
        result['id'] = result.pop('_id')
    return result

# JWT认证相关函数
def generate_token(user_data):
    """生成JWT token"""
    payload = {
        'user_id': str(user_data['_id']),
        'username': user_data['username'],
        'role': user_data['role'],
        'exp': datetime.utcnow() + app.config['JWT_ACCESS_TOKEN_EXPIRES'],
        'iat': datetime.utcnow()
    }
    return jwt.encode(payload, app.config['JWT_SECRET_KEY'], algorithm='HS256')

def verify_token(token):
    """验证JWT token"""
    try:
        payload = jwt.decode(token, app.config['JWT_SECRET_KEY'], algorithms=['HS256'])
        return payload
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None

def require_auth(f):
    """装饰器：要求认证"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'success': False, 'error': '缺少认证token'}), 401
        
        if token.startswith('Bearer '):
            token = token[7:]
        
        payload = verify_token(token)
        if not payload:
            return jsonify({'success': False, 'error': 'token无效或已过期'}), 401
        
        request.current_user = payload
        return f(*args, **kwargs)
    return decorated_function

def require_admin(f):
    """装饰器：要求管理员权限"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not hasattr(request, 'current_user') or request.current_user.get('role') != '管理员':
            return jsonify({'success': False, 'error': '需要管理员权限'}), 403
        return f(*args, **kwargs)
    return decorated_function

def hash_password(password):
    """加密密码"""
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

def verify_password(password, password_hash):
    """验证密码"""
    return bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8'))

# Mock数据用于初始化
from mock_data import get_mock_personnel, get_mock_call_records, get_mock_stats, get_mock_progress_data, get_mock_insights_data

def init_db():
    """初始化数据库，插入mock数据"""
    try:
        # 检查是否已有数据
        personnel_count = db.personnel.count_documents({})
        if personnel_count == 0:
            # 插入mock患者数据
            mock_personnel = get_mock_personnel()
            db.personnel.insert_many(mock_personnel)
            print("已插入mock患者数据")
            
            # 插入mock通话记录数据
            mock_records = get_mock_call_records()
            db.call_records.insert_many(mock_records)
            print("已插入mock通话记录数据")
        
        # 检查是否有用户账号，如果没有则创建默认账号
        user_count = db.sys_info.count_documents({'doc_type': 'user_account'})
        if user_count == 0:
            # 创建默认管理员账号
            admin_user = {
                'doc_type': 'user_account',
                'username': 'admin',
                'password_hash': hash_password('admin123'),
                'role': '管理员',
                'full_name': '系统管理员',
                'email': '<EMAIL>',
                'is_active': True,
                'login_count': 0,
                'last_login': None,
                'created_at': datetime.utcnow()
            }
            
            # 创建默认医生账号
            doctor_user = {
                'doc_type': 'user_account',
                'username': 'doctor',
                'password_hash': hash_password('doctor123'),
                'role': '医生',
                'full_name': '测试医生',
                'email': '<EMAIL>',
                'is_active': True,
                'login_count': 0,
                'last_login': None,
                'created_at': datetime.utcnow()
            }
            
            db.sys_info.insert_many([admin_user, doctor_user])
            print("已创建默认用户账号")
            print("管理员账号: admin / admin123")
            print("医生账号: doctor / doctor123")
            
        print("数据库初始化完成")
    except Exception as e:
        print(f"数据库初始化错误: {e}")

# 认证相关API
@app.route('/api/auth/login', methods=['POST'])
def login():
    """用户登录"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        if not username or not password:
            return jsonify({
                'success': False,
                'error': '用户名和密码不能为空'
            }), 400
        
        # 查找用户
        user = db.sys_info.find_one({
            'doc_type': 'user_account',
            'username': username,
            'is_active': True
        })
        
        if not user:
            return jsonify({
                'success': False,
                'error': '用户名或密码错误'
            }), 401
        
        # 验证密码
        if not verify_password(password, user['password_hash']):
            return jsonify({
                'success': False,
                'error': '用户名或密码错误'
            }), 401
        
        # 生成token
        token = generate_token(user)
        
        # 更新登录信息
        db.sys_info.update_one(
            {'_id': user['_id']},
            {
                '$set': {
                    'last_login': datetime.utcnow()
                },
                '$inc': {
                    'login_count': 1
                }
            }
        )
        
        return jsonify({
            'success': True,
            'data': {
                'token': token,
                'user': {
                    'id': str(user['_id']),
                    'username': user['username'],
                    'role': user['role'],
                    'full_name': user['full_name'],
                    'email': user['email']
                }
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/auth/verify', methods=['GET'])
@require_auth
def verify_token_api():
    """验证token有效性"""
    try:
        # 获取完整用户信息
        user = db.sys_info.find_one({
            'doc_type': 'user_account',
            '_id': ObjectId(request.current_user['user_id']),
            'is_active': True
        })
        
        if not user:
            return jsonify({
                'success': False,
                'error': '用户不存在或已被禁用'
            }), 401
        
        return jsonify({
            'success': True,
            'data': {
                'user': {
                    'id': str(user['_id']),
                    'username': user['username'],
                    'role': user['role'],
                    'full_name': user['full_name'],
                    'email': user['email']
                }
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/auth/logout', methods=['POST'])
@require_auth
def logout():
    """用户登出（客户端清除token即可）"""
    return jsonify({
        'success': True,
        'message': '登出成功'
    })

# 患者管理相关API
@app.route('/api/personnel', methods=['GET'])
@require_auth
def get_personnel():
    """获取患者列表"""
    try:
        search = request.args.get('search', '')
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 50))
        
        # 构建查询条件
        query = {}
        if search:
            query['$or'] = [
                {'name': {'$regex': search, '$options': 'i'}},
                {'phone': {'$regex': search, '$options': 'i'}}
            ]
        
        # 获取总数
        total = db.personnel.count_documents(query)
        
        # 分页查询
        skip = (page - 1) * limit
        cursor = db.personnel.find(query).skip(skip).limit(limit)
        personnel = [serialize_doc(doc) for doc in cursor]
        
        return jsonify({
            'success': True,
            'data': personnel,
            'total': total,
            'page': page,
            'limit': limit
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/personnel/<personnel_id>', methods=['GET'])
@require_auth
def get_personnel_detail(personnel_id):
    """获取患者详情"""
    try:
        # 获取患者基本信息
        personnel = db.personnel.find_one({'_id': ObjectId(personnel_id)})
        if not personnel:
            return jsonify({
                'success': False,
                'error': '患者不存在'
            }), 404
        
        # 获取患者通话记录
        call_records = [serialize_doc(doc) for doc in db.call_records.find({
            '手机号': personnel['phone']
        }).sort([('创建时间', -1)])]
        
        return jsonify({
            'success': True,
            'data': {
                'personnel': serialize_doc(personnel),
                'call_records': call_records
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/personnel', methods=['POST'])
@require_auth
def add_personnel():
    """添加新患者"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['name', 'phone', 'age', 'gender']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'缺少必填字段: {field}'
                }), 400
        
        # 检查电话号码是否已存在
        existing = db.personnel.find_one({'phone': data['phone']})
        if existing:
            return jsonify({
                'success': False,
                'error': '该电话号码已存在'
            }), 400
        
        # 添加时间戳
        data['created_at'] = datetime.utcnow()
        data['updated_at'] = datetime.utcnow()
        
        # 插入数据
        result = db.personnel.insert_one(data)
        
        return jsonify({
            'success': True,
            'data': {
                '_id': str(result.inserted_id),
                **data
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/personnel/<personnel_id>', methods=['PUT'])
@require_auth
def update_personnel(personnel_id):
    """更新患者信息"""
    try:
        data = request.get_json()
        
        # 验证患者是否存在
        personnel = db.personnel.find_one({'_id': ObjectId(personnel_id)})
        if not personnel:
            return jsonify({
                'success': False,
                'error': '患者不存在'
            }), 404
        
        # 如果更新电话号码，检查是否与其他患者重复
        if 'phone' in data and data['phone'] != personnel['phone']:
            existing = db.personnel.find_one({
                'phone': data['phone'],
                '_id': {'$ne': ObjectId(personnel_id)}
            })
            if existing:
                return jsonify({
                    'success': False,
                    'error': '该电话号码已被其他患者使用'
                }), 400
        
        # 添加更新时间戳
        data['updated_at'] = datetime.utcnow()
        
        # 更新数据
        result = db.personnel.update_one(
            {'_id': ObjectId(personnel_id)},
            {'$set': data}
        )
        
        if result.modified_count > 0:
            # 获取更新后的数据
            updated_personnel = db.personnel.find_one({'_id': ObjectId(personnel_id)})
            return jsonify({
                'success': True,
                'data': serialize_doc(updated_personnel)
            })
        else:
            return jsonify({
                'success': True,
                'message': '没有数据被修改',
                'data': serialize_doc(personnel)
            })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 通话记录相关API
@app.route('/api/call-records', methods=['GET'])
@require_auth
def get_call_records():
    """获取通话记录列表"""
    try:
        search = request.args.get('search', '')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        call_type = request.args.get('call_type')
        status = request.args.get('status')
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 50))
        
        # 构建查询条件
        query = {}
        if search:
            query['$or'] = [
                {'患者名字': {'$regex': search, '$options': 'i'}},
                {'手机号': {'$regex': search, '$options': 'i'}}
            ]
        
        if start_date and end_date:
            query['记录日期'] = {
                '$gte': start_date,
                '$lte': end_date
            }
        
        if call_type and call_type != '全部':
            query['call_type'] = call_type
            
        if status and status != '全部':
            query['训练完成情况'] = status
        
        # 获取总数
        total = db.call_records.count_documents(query)
        
        # 分页查询
        skip = (page - 1) * limit
        cursor = db.call_records.find(query).sort([('创建时间', -1)]).skip(skip).limit(limit)
        records = [serialize_doc(doc) for doc in cursor]
        
        return jsonify({
            'success': True,
            'data': records,
            'total': total,
            'page': page,
            'limit': limit
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 统计数据API
@app.route('/api/stats', methods=['GET'])
@require_auth
def get_stats():
    """获取统计数据"""
    try:
        # 获取基本统计数据
        total_personnel = db.personnel.count_documents({})
        active_personnel = db.personnel.count_documents({'training_status': '训练中'})
        
        # 今日相关统计
        today = datetime.now().strftime('%Y-%m-%d')
        today_records = list(db.call_records.find({'记录日期': today}))
        
        pending_today = len([r for r in today_records if not r.get('训练完成情况')])
        completed_today = len([r for r in today_records if r.get('训练完成情况') == '完成'])
        
        # 计算完成率
        completion_rate = (completed_today / max(len(today_records), 1)) * 100 if today_records else 0
        
                        # 计算平均训练次数
        training_counts = []
        for r in today_records:
            if r.get('训练次数') and r.get('训练次数') != '--':
                try:
                    # 提取训练次数的数字部分
                    training_str = str(r['训练次数'])
                    training_num = ''.join(filter(str.isdigit, training_str))
                    if training_num:
                        training_counts.append(int(training_num))
                except (ValueError, TypeError):
                    continue
        avg_training = sum(training_counts) / len(training_counts) if training_counts else 0
        
        return jsonify({
            'success': True,
            'data': {
                'totalPersonnel': total_personnel,
                'pendingToday': pending_today,
                'completedToday': completed_today,
                'avgTraining': round(avg_training),
                'completionRate': round(completion_rate, 1)
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 康复进度API
@app.route('/api/progress', methods=['GET'])
@require_auth
def get_progress():
    """获取康复进度数据"""
    try:
        search = request.args.get('search', '')
        
        # 获取训练中的患者
        query = {'training_status': '训练中'}
        if search:
            query['name'] = {'$regex': search, '$options': 'i'}
        
        personnel = list(db.personnel.find(query))
        
        # 为每个患者计算进度数据
        progress_data = {}
        for person in personnel:
            try:
                # 获取患者的通话记录
                records = list(db.call_records.find({
                    '手机号': person['phone']
                }).sort([('创建时间', 1)]))
                
                # 计算进度指标
                enrollment_date = datetime.strptime(person['enrollment_date'], '%Y-%m-%d')
                current_day = (datetime.now() - enrollment_date).days + 1
                total_days = 30
                
                # 计算依从性
                completed_days = len([r for r in records if r.get('训练完成情况') == '完成'])
                compliance = (completed_days / max(current_day, 1)) * 100
                
                # 昨日训练数据
                yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
                yesterday_record = next((r for r in records if r.get('记录日期') == yesterday), None)
                yesterday_training = 0
                if yesterday_record and yesterday_record.get('训练次数'):
                    try:
                        # 提取训练次数的数字部分，处理"720次"这种格式
                        training_str = str(yesterday_record['训练次数'])
                        training_num = ''.join(filter(str.isdigit, training_str))
                        if training_num:
                            yesterday_training = int(training_num)
                    except (ValueError, TypeError):
                        yesterday_training = 0
                
                # 平均训练次数
                training_counts = []
                for r in records:
                    if r.get('训练次数') and r.get('训练次数') != '--':
                        try:
                            # 提取训练次数的数字部分
                            training_str = str(r['训练次数'])
                            training_num = ''.join(filter(str.isdigit, training_str))
                            if training_num:
                                training_counts.append(int(training_num))
                        except (ValueError, TypeError):
                            continue
                
                avg_training = sum(training_counts) / len(training_counts) if training_counts else 0
                
                progress_data[str(person['_id'])] = {
                    'currentDay': min(current_day, total_days),
                    'totalDays': total_days,
                    'compliance': round(min(compliance, 100)),
                    'todayTraining': yesterday_training,
                    'avgTraining': round(avg_training)
                }
            except Exception as person_error:
                app.logger.error(f"处理患者 {person.get('name', 'Unknown')} 数据时出错: {person_error}")
                # 为出错的患者提供默认值
                progress_data[str(person['_id'])] = {
                    'currentDay': 0,
                    'totalDays': 30,
                    'compliance': 0,
                    'todayTraining': 0,
                    'avgTraining': 0
                }
        
        return jsonify({
            'success': True,
            'data': {
                'personnel': [serialize_doc(doc) for doc in personnel],
                'progress': progress_data
            }
        })
        
    except Exception as e:
        app.logger.error(f"获取康复进度数据失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 智能摘要API
@app.route('/api/insights', methods=['GET'])
@require_auth
def get_insights():
    """获取智能摘要数据"""
    try:
        time_range = request.args.get('time_range', '最近7天')
        
        # 根据时间范围计算日期
        if time_range == '最近7天':
            start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
        elif time_range == '最近30天':
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        else:
            start_date = '2025-01-01'  # 全部记录
        
        # 获取指定时间范围内的通话记录
        records = list(db.call_records.find({
            '记录日期': {'$gte': start_date},
            '对话历史记录': {'$exists': True, '$ne': None}
        }))
        
        # 使用mock数据作为示例
        mock_insights = get_mock_insights_data()
        
        return jsonify({
            'success': True,
            'data': mock_insights
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 用户管理API（仅管理员可访问）
@app.route('/api/users', methods=['GET'])
@require_auth
@require_admin
def get_users():
    """获取用户列表（只返回医生账号）"""
    try:
        # 只获取医生账号
        users = list(db.sys_info.find({
            'doc_type': 'user_account',
            'role': '医生'
        }))
        
        # 移除密码哈希字段
        for user in users:
            user.pop('password_hash', None)
        
        return jsonify({
            'success': True,
            'data': [serialize_user(user) for user in users]
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/users', methods=['POST'])
@require_auth
@require_admin
def create_user():
    """创建新用户（只能创建医生账号）"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['username', 'password', 'full_name', 'email']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'error': f'缺少必填字段: {field}'
                }), 400
        
        # 检查用户名是否已存在
        existing_user = db.sys_info.find_one({
            'doc_type': 'user_account',
            'username': data['username']
        })
        
        if existing_user:
            return jsonify({
                'success': False,
                'error': '用户名已存在'
            }), 400
        
        # 强制设置为医生角色
        user_data = {
            'doc_type': 'user_account',
            'username': data['username'],
            'password_hash': hash_password(data['password']),
            'role': '医生',  # 强制设置为医生
            'full_name': data['full_name'],
            'email': data['email'],
            'phone': data.get('phone', ''),
            'is_active': True,
            'login_count': 0,
            'last_login': None,
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }
        
        result = db.sys_info.insert_one(user_data)
        
        # 返回创建的用户信息（不包含密码）
        created_user = db.sys_info.find_one({'_id': result.inserted_id})
        created_user.pop('password_hash', None)
        
        return jsonify({
            'success': True,
            'data': serialize_user(created_user)
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/users/<user_id>', methods=['PUT'])
@require_auth
@require_admin
def update_user(user_id):
    """更新用户信息"""
    try:
        data = request.get_json()
        
        # 查找用户
        user = db.sys_info.find_one({
            '_id': ObjectId(user_id),
            'doc_type': 'user_account',
            'role': '医生'  # 只能更新医生账号
        })
        
        if not user:
            return jsonify({
                'success': False,
                'error': '用户不存在或无权限修改'
            }), 404
        
        # 准备更新数据
        update_data = {
            'updated_at': datetime.utcnow()
        }
        
        # 可更新的字段
        allowed_fields = ['full_name', 'email', 'phone', 'is_active']
        for field in allowed_fields:
            if field in data:
                update_data[field] = data[field]
        
        # 如果要更新密码
        if 'password' in data and data['password']:
            update_data['password_hash'] = hash_password(data['password'])
        
        # 如果要更新用户名，检查是否重复
        if 'username' in data and data['username'] != user['username']:
            existing = db.sys_info.find_one({
                'doc_type': 'user_account',
                'username': data['username'],
                '_id': {'$ne': ObjectId(user_id)}
            })
            if existing:
                return jsonify({
                    'success': False,
                    'error': '用户名已被使用'
                }), 400
            update_data['username'] = data['username']
        
        # 执行更新
        result = db.sys_info.update_one(
            {'_id': ObjectId(user_id)},
            {'$set': update_data}
        )
        
        if result.modified_count > 0:
            # 返回更新后的用户信息
            updated_user = db.sys_info.find_one({'_id': ObjectId(user_id)})
            updated_user.pop('password_hash', None)
            
            return jsonify({
                'success': True,
                'data': serialize_user(updated_user)
            })
        else:
            return jsonify({
                'success': True,
                'message': '没有数据被修改'
            })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/users/<user_id>', methods=['DELETE'])
@require_auth
@require_admin
def delete_user(user_id):
    """删除用户（软删除，设置为未激活）"""
    try:
        # 查找用户
        user = db.sys_info.find_one({
            '_id': ObjectId(user_id),
            'doc_type': 'user_account',
            'role': '医生'  # 只能删除医生账号
        })
        
        if not user:
            return jsonify({
                'success': False,
                'error': '用户不存在或无权限删除'
            }), 404
        
        # 软删除：设置为未激活
        result = db.sys_info.update_one(
            {'_id': ObjectId(user_id)},
            {'$set': {
                'is_active': False,
                'updated_at': datetime.utcnow()
            }}
        )
        
        if result.modified_count > 0:
            return jsonify({
                'success': True,
                'message': '用户已停用'
            })
        else:
            return jsonify({
                'success': False,
                'error': '删除失败'
            }), 500
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 健康检查
@app.route('/api/health', methods=['GET'])
def health_check():
    return jsonify({
        'success': True,
        'message': 'API服务正常运行',
        'timestamp': datetime.utcnow().isoformat()
    })

if __name__ == '__main__':
    # 初始化数据库
    init_db()
    app.run(debug=True, host='0.0.0.0', port=5000) 