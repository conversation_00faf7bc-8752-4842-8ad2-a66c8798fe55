import pymongo
import bcrypt
from datetime import datetime

# 连接数据库
client = pymongo.MongoClient('mongodb://localhost:27017')
db = client['med_call_records']

def hash_password(password):
    """加密密码"""
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

print('修复用户账号...')

# 1. 更新admin用户密码
admin_result = db.sys_info.update_one(
    {'doc_type': 'user_account', 'username': 'admin'},
    {'$set': {
        'password_hash': hash_password('admin123'),
        'updated_at': datetime.utcnow()
    }}
)

if admin_result.modified_count > 0:
    print('✅ admin用户密码已更新为: admin123')
else:
    print('❌ admin用户更新失败')

# 2. 检查是否存在doctor用户，如果不存在则创建
doctor_user = db.sys_info.find_one({'doc_type': 'user_account', 'username': 'doctor'})

if not doctor_user:
    # 创建doctor用户
    doctor_data = {
        'doc_type': 'user_account',
        'username': 'doctor',
        'password_hash': hash_password('doctor123'),
        'role': '医生',
        'full_name': '测试医生',
        'email': '<EMAIL>',
        'is_active': True,
        'login_count': 0,
        'last_login': None,
        'created_at': datetime.utcnow(),
        'updated_at': datetime.utcnow()
    }
    
    result = db.sys_info.insert_one(doctor_data)
    if result.inserted_id:
        print('✅ doctor用户已创建，密码: doctor123')
    else:
        print('❌ doctor用户创建失败')
else:
    print('✅ doctor用户已存在')

print('\n当前用户列表:')
users = list(db.sys_info.find({'doc_type': 'user_account'}))
for user in users:
    print(f'- {user["username"]} ({user["role"]}) - 激活: {user["is_active"]}')

print('\n请使用以下账号登录:')
print('管理员: admin / admin123')
print('医生: doctor / doctor123') 