#!/usr/bin/env python3
"""
医疗随访管理系统后端启动脚本
"""

import sys
import subprocess
import importlib.util
import os

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    print(f"✓ Python版本检查通过: {sys.version}")
    return True

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'flask',
        'flask_cors',
        'motor',
        'pymongo',
        'bson'
    ]
    
    missing_packages = []
    for package in required_packages:
        if importlib.util.find_spec(package) is None:
            missing_packages.append(package)
    
    if missing_packages:
        print("错误: 缺少以下依赖包:")
        for pkg in missing_packages:
            print(f"  - {pkg}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False
    
    print("✓ 依赖包检查通过")
    return True

def check_mongodb():
    """检查MongoDB连接"""
    try:
        import pymongo
        mongo_url = os.getenv('MONGO_URL', 'mongodb://localhost:27017')
        client = pymongo.MongoClient(mongo_url, serverSelectionTimeoutMS=5000)
        client.server_info()  # 触发连接
        print(f"✓ MongoDB连接成功: {mongo_url}")
        return True
    except Exception as e:
        print(f"错误: MongoDB连接失败: {e}")
        print("请确保MongoDB服务正在运行")
        return False

def main():
    """主函数"""
    print("医疗随访管理系统后端启动检查")
    print("=" * 40)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查依赖包
    if not check_dependencies():
        sys.exit(1)
    
    # 检查MongoDB连接
    if not check_mongodb():
        print("\n警告: MongoDB连接失败，但仍会尝试启动应用")
        print("应用启动后可能无法正常工作")
    
    print("\n" + "=" * 40)
    print("启动Flask应用...")
    
    # 启动Flask应用
    try:
        from app import app
        app.run(debug=True, host='0.0.0.0', port=5000)
    except Exception as e:
        print(f"错误: 启动应用失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main() 