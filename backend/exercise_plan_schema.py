"""
锻炼计划数据模型设计

集合名称: exercise_plans
用途: 存储患者的锻炼计划配置和执行记录
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional
from bson import ObjectId

# 锻炼计划数据结构
EXERCISE_PLAN_SCHEMA = {
    "_id": "ObjectId",                    # MongoDB自动生成的唯一标识
    "plan_id": "String",                  # 计划编号，格式：EP_YYYYMMDD_序号
    "patient_id": "String",               # 患者编号，关联personnel集合
    "patient_name": "String",             # 患者姓名（冗余字段，便于查询）
    "doctor_id": "String",                # 创建医生的用户名
    "doctor_name": "String",              # 创建医生的姓名
    
    # 计划基本信息
    "plan_name": "String",                # 计划名称，如"膝关节康复训练计划"
    "description": "String",              # 计划描述
    "status": "String",                   # 计划状态：active(进行中), completed(已完成), paused(暂停), cancelled(取消)
    
    # 锻炼参数配置
    "total_weeks": "Number",              # 总锻炼周数
    "initial_daily_count": "Number",      # 初始每日锻炼次数
    "weekly_increment": "Number",         # 每周递增次数
    "max_rest_days_per_week": "Number",   # 每周最多休息天数
    
    # 时间配置
    "start_date": "String",               # 开始日期 (YYYY-MM-DD)
    "end_date": "String",                 # 结束日期 (YYYY-MM-DD)
    "current_week": "Number",             # 当前执行到第几周
    "current_daily_target": "Number",     # 当前每日目标次数
    
    # 执行记录
    "weekly_records": [                   # 每周执行记录
        {
            "week_number": "Number",      # 第几周
            "week_start_date": "String",  # 本周开始日期
            "week_end_date": "String",    # 本周结束日期
            "target_daily_count": "Number", # 本周目标每日次数
            "daily_records": [            # 每日记录
                {
                    "date": "String",     # 日期 (YYYY-MM-DD)
                    "day_of_week": "Number", # 星期几 (1-7)
                    "target_count": "Number", # 目标次数
                    "actual_count": "Number", # 实际完成次数
                    "is_rest_day": "Boolean", # 是否为休息日
                    "completion_rate": "Number", # 完成率 (0-1)
                    "notes": "String"     # 备注
                }
            ],
            "week_completion_rate": "Number", # 本周完成率
            "rest_days_used": "Number"    # 本周已使用休息天数
        }
    ],
    
    # 统计数据
    "total_target_count": "Number",       # 总目标次数
    "total_actual_count": "Number",       # 总实际完成次数
    "overall_completion_rate": "Number",  # 总体完成率
    "consecutive_days": "Number",         # 连续锻炼天数
    "total_rest_days": "Number",          # 总休息天数
    
    # 系统字段
    "created_at": "DateTime",             # 创建时间
    "updated_at": "DateTime",             # 更新时间
    "created_by": "String",               # 创建者用户名
    "last_modified_by": "String"          # 最后修改者用户名
}

# 示例数据
SAMPLE_EXERCISE_PLAN = {
    "_id": ObjectId(),
    "plan_id": "EP_20250621_001",
    "patient_id": "P0001",
    "patient_name": "张三",
    "doctor_id": "doctor01",
    "doctor_name": "李医生",
    
    "plan_name": "膝关节康复训练计划",
    "description": "术后康复训练，逐步增加运动强度",
    "status": "active",
    
    "total_weeks": 4,
    "initial_daily_count": 200,
    "weekly_increment": 100,
    "max_rest_days_per_week": 2,
    
    "start_date": "2025-06-21",
    "end_date": "2025-07-19",
    "current_week": 1,
    "current_daily_target": 200,
    
    "weekly_records": [
        {
            "week_number": 1,
            "week_start_date": "2025-06-21",
            "week_end_date": "2025-06-27",
            "target_daily_count": 200,
            "daily_records": [
                {
                    "date": "2025-06-21",
                    "day_of_week": 6,
                    "target_count": 200,
                    "actual_count": 180,
                    "is_rest_day": False,
                    "completion_rate": 0.9,
                    "notes": "第一天，适应中"
                }
            ],
            "week_completion_rate": 0.9,
            "rest_days_used": 0
        }
    ],
    
    "total_target_count": 200,
    "total_actual_count": 180,
    "overall_completion_rate": 0.9,
    "consecutive_days": 1,
    "total_rest_days": 0,
    
    "created_at": datetime.utcnow(),
    "updated_at": datetime.utcnow(),
    "created_by": "doctor01",
    "last_modified_by": "doctor01"
}

# 计划状态枚举
PLAN_STATUS = {
    "ACTIVE": "active",       # 进行中
    "COMPLETED": "completed", # 已完成
    "PAUSED": "paused",       # 暂停
    "CANCELLED": "cancelled"  # 取消
}

# 数据验证规则
VALIDATION_RULES = {
    "total_weeks": {"min": 1, "max": 52},           # 1-52周
    "initial_daily_count": {"min": 1, "max": 2000}, # 1-2000次
    "weekly_increment": {"min": 0, "max": 500},     # 0-500次
    "max_rest_days_per_week": {"min": 0, "max": 7}  # 0-7天
}

def generate_plan_id() -> str:
    """生成计划编号"""
    from datetime import datetime
    today = datetime.now().strftime("%Y%m%d")
    # 这里应该查询数据库获取今日最大序号，简化示例
    return f"EP_{today}_001"

def calculate_end_date(start_date: str, total_weeks: int) -> str:
    """计算结束日期"""
    from datetime import datetime, timedelta
    start = datetime.strptime(start_date, "%Y-%m-%d")
    end = start + timedelta(weeks=total_weeks)
    return end.strftime("%Y-%m-%d")

def calculate_weekly_target(week_number: int, initial_count: int, increment: int) -> int:
    """计算指定周的目标次数"""
    return initial_count + (week_number - 1) * increment
