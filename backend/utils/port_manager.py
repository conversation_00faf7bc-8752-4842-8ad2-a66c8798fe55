#!/usr/bin/env python3
"""
端口管理工具
用于检测端口可用性，处理端口冲突，自动选择可用端口
"""

import socket
import sys
import os
import psutil
import logging
from typing import List, Optional, Tuple
import subprocess

logger = logging.getLogger(__name__)

class PortManager:
    """端口管理器"""
    
    @staticmethod
    def is_port_available(port: int, host: str = 'localhost') -> bool:
        """
        检查端口是否可用
        
        Args:
            port: 端口号
            host: 主机地址
            
        Returns:
            bool: 端口是否可用
        """
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex((host, port))
                return result != 0
        except Exception as e:
            logger.warning(f"检查端口 {port} 时出错: {e}")
            return False
    
    @staticmethod
    def find_available_port(start_port: int = 5000, max_attempts: int = 100) -> Optional[int]:
        """
        查找可用端口
        
        Args:
            start_port: 起始端口号
            max_attempts: 最大尝试次数
            
        Returns:
            int: 可用的端口号，如果找不到则返回None
        """
        for port in range(start_port, start_port + max_attempts):
            if PortManager.is_port_available(port):
                logger.info(f"找到可用端口: {port}")
                return port
        
        logger.error(f"在 {start_port}-{start_port + max_attempts} 范围内未找到可用端口")
        return None
    
    @staticmethod
    def get_port_process_info(port: int) -> Optional[dict]:
        """
        获取占用指定端口的进程信息
        
        Args:
            port: 端口号
            
        Returns:
            dict: 进程信息，如果端口未被占用则返回None
        """
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    for conn in proc.connections():
                        if conn.laddr.port == port:
                            return {
                                'pid': proc.info['pid'],
                                'name': proc.info['name'],
                                'cmdline': ' '.join(proc.info['cmdline'][:3]) if proc.info['cmdline'] else '',
                                'status': proc.status()
                            }
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            logger.warning(f"获取端口 {port} 进程信息时出错: {e}")
        
        return None
    
    @staticmethod
    def kill_port_process(port: int, force: bool = False) -> bool:
        """
        杀死占用指定端口的进程
        
        Args:
            port: 端口号
            force: 是否强制杀死
            
        Returns:
            bool: 是否成功杀死进程
        """
        process_info = PortManager.get_port_process_info(port)
        if not process_info:
            logger.info(f"端口 {port} 未被占用")
            return True
        
        try:
            pid = process_info['pid']
            proc = psutil.Process(pid)
            
            if force:
                proc.kill()
                logger.info(f"强制杀死进程 {pid} ({process_info['name']})")
            else:
                proc.terminate()
                logger.info(f"终止进程 {pid} ({process_info['name']})")
            
            # 等待进程结束
            proc.wait(timeout=5)
            return True
            
        except psutil.NoSuchProcess:
            logger.info(f"进程 {pid} 已不存在")
            return True
        except psutil.TimeoutExpired:
            logger.warning(f"进程 {pid} 未能在5秒内结束")
            return False
        except Exception as e:
            logger.error(f"杀死进程时出错: {e}")
            return False
    
    @staticmethod
    def cleanup_socket_connections():
        """清理僵尸套接字连接"""
        try:
            if sys.platform == 'win32':
                # Windows平台使用netstat
                subprocess.run(['netstat', '-ano'], capture_output=True)
            else:
                # Linux/Mac平台
                subprocess.run(['netstat', '-tulpn'], capture_output=True)
        except Exception as e:
            logger.warning(f"清理套接字连接时出错: {e}")
    
    @staticmethod
    def get_recommended_ports() -> List[int]:
        """
        获取推荐使用的端口列表
        
        Returns:
            List[int]: 推荐端口列表
        """
        # 常用的开发端口
        recommended_ports = [5000, 5001, 5002, 8000, 8080, 8081, 8082, 3000, 3001, 4000]
        available_ports = []
        
        for port in recommended_ports:
            if PortManager.is_port_available(port):
                available_ports.append(port)
        
        return available_ports
    
    @staticmethod
    def handle_port_conflict(preferred_port: int, auto_resolve: bool = True) -> Tuple[int, str]:
        """
        处理端口冲突
        
        Args:
            preferred_port: 首选端口
            auto_resolve: 是否自动解决冲突
            
        Returns:
            Tuple[int, str]: (最终使用的端口, 处理信息)
        """
        if PortManager.is_port_available(preferred_port):
            return preferred_port, f"端口 {preferred_port} 可用"
        
        # 端口被占用，获取占用进程信息
        process_info = PortManager.get_port_process_info(preferred_port)
        conflict_info = f"端口 {preferred_port} 被占用"
        
        if process_info:
            conflict_info += f" (进程: {process_info['name']}, PID: {process_info['pid']})"
        
        if not auto_resolve:
            return preferred_port, conflict_info + " - 需要手动处理"
        
        # 自动寻找可用端口
        available_port = PortManager.find_available_port(preferred_port + 1)
        if available_port:
            return available_port, f"{conflict_info} - 自动切换到端口 {available_port}"
        else:
            return preferred_port, f"{conflict_info} - 未找到可用端口"

def print_port_status(port: int):
    """打印端口状态信息"""
    print(f"\n🔍 端口 {port} 状态检查:")
    print("-" * 30)
    
    if PortManager.is_port_available(port):
        print(f"✅ 端口 {port} 可用")
    else:
        print(f"❌ 端口 {port} 被占用")
        
        process_info = PortManager.get_port_process_info(port)
        if process_info:
            print(f"📍 占用进程: {process_info['name']} (PID: {process_info['pid']})")
            print(f"📋 命令行: {process_info['cmdline']}")
            print(f"🔄 状态: {process_info['status']}")
    
    print("-" * 30)

if __name__ == '__main__':
    # 测试端口管理器
    test_port = 5000
    print_port_status(test_port)
    
    # 获取推荐端口
    recommended = PortManager.get_recommended_ports()
    print(f"\n📋 推荐可用端口: {recommended[:5]}")
    
    # 处理端口冲突
    final_port, message = PortManager.handle_port_conflict(test_port)
    print(f"\n🎯 最终端口: {final_port}")
    print(f"📝 处理信息: {message}") 