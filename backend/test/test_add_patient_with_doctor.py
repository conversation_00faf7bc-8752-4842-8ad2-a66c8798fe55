#!/usr/bin/env python3
"""
测试添加患者时的医生关联功能
验证管理员和医生添加患者时的不同行为
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

import json
from datetime import datetime
from database import get_db

def simulate_add_patient_request(user_role, username, patient_data):
    """模拟添加患者的API请求"""
    print(f"\n🧪 模拟 {user_role} ({username}) 添加患者: {patient_data['name']}")
    
    # 模拟request.current_user
    class MockRequest:
        def __init__(self, user_role, username):
            self.current_user = {
                'role': user_role,
                'username': username
            }
    
    # 模拟添加患者的逻辑
    db = get_db()
    data = patient_data.copy()
    
    # 检查电话号码是否已存在
    existing = db.personnel.find_one({'phone': data['phone']})
    if existing:
        print(f"   ❌ 电话号码 {data['phone']} 已存在")
        return None
    
    # 模拟request对象
    mock_request = MockRequest(user_role, username)
    current_user = mock_request.current_user
    
    # 根据用户角色处理医生关联
    if current_user.get('role') == '医生':
        # 医生添加患者时，自动关联到当前医生
        doctor_username = current_user.get('username')
        
        # 获取医生的详细信息
        doctor_info = db.sys_info.find_one({
            'doc_type': 'user_account',
            'username': doctor_username,
            'role': '医生'
        })
        
        if doctor_info:
            data['assigned_doctor'] = doctor_username
            data['assigned_doctor_name'] = doctor_info.get('full_name', doctor_username)
            data['assignment_date'] = datetime.now().strftime('%Y-%m-%d')
            print(f"   ✅ 患者自动分配给医生 {doctor_info.get('full_name')} ({doctor_username})")
        else:
            print(f"   ⚠️  警告: 未找到医生信息 {doctor_username}")
    
    elif current_user.get('role') == '管理员':
        # 管理员添加患者时，不自动关联医生
        print(f"   ✅ 管理员添加患者，不自动分配医生")
    
    # 添加时间戳
    data['created_at'] = datetime.utcnow()
    data['updated_at'] = datetime.utcnow()
    
    # 插入数据
    result = db.personnel.insert_one(data)
    
    # 获取插入的文档
    inserted_doc = db.personnel.find_one({'_id': result.inserted_id})
    
    print(f"   📋 插入结果:")
    print(f"      患者姓名: {inserted_doc['name']}")
    print(f"      手机号: {inserted_doc['phone']}")
    print(f"      分配医生: {inserted_doc.get('assigned_doctor', '未分配')}")
    print(f"      医生姓名: {inserted_doc.get('assigned_doctor_name', '未分配')}")
    print(f"      分配日期: {inserted_doc.get('assignment_date', '未分配')}")
    
    return inserted_doc

def test_admin_add_patient():
    """测试管理员添加患者"""
    print("🔍 测试管理员添加患者...")
    
    patient_data = {
        'name': '测试患者A',
        'phone': '**********1',
        'age': 45,
        'gender': '男',
        'enrollment_date': '2025-06-19',
        'training_status': '未开始'
    }
    
    result = simulate_add_patient_request('管理员', 'admin', patient_data)
    
    if result:
        # 验证管理员添加的患者没有自动分配医生
        if 'assigned_doctor' not in result:
            print("   ✅ 管理员添加患者正确：未自动分配医生")
            return True
        else:
            print("   ❌ 管理员添加患者错误：不应该自动分配医生")
            return False
    
    return False

def test_doctor_add_patient():
    """测试医生添加患者"""
    print("\n🔍 测试医生添加患者...")
    
    patient_data = {
        'name': '测试患者B',
        'phone': '**********2',
        'age': 35,
        'gender': '女',
        'enrollment_date': '2025-06-19',
        'training_status': '未开始'
    }
    
    # 使用现有的医生账户
    result = simulate_add_patient_request('医生', 'doctor01', patient_data)
    
    if result:
        # 验证医生添加的患者自动分配给了当前医生
        if (result.get('assigned_doctor') == 'doctor01' and 
            result.get('assigned_doctor_name') and
            result.get('assignment_date')):
            print("   ✅ 医生添加患者正确：自动分配给当前医生")
            return True
        else:
            print("   ❌ 医生添加患者错误：未正确分配给当前医生")
            print(f"      assigned_doctor: {result.get('assigned_doctor')}")
            print(f"      assigned_doctor_name: {result.get('assigned_doctor_name')}")
            print(f"      assignment_date: {result.get('assignment_date')}")
            return False
    
    return False

def test_multiple_doctors():
    """测试多个医生添加患者"""
    print("\n🔍 测试多个医生添加患者...")
    
    db = get_db()
    
    # 获取所有医生
    doctors = list(db.sys_info.find({
        'doc_type': 'user_account',
        'role': '医生',
        'is_active': True
    }, {'username': 1, 'full_name': 1}))
    
    success_count = 0
    
    for i, doctor in enumerate(doctors[:2]):  # 只测试前2个医生
        username = doctor['username']
        full_name = doctor.get('full_name', username)
        
        patient_data = {
            'name': f'测试患者{chr(67+i)}',  # C, D, E...
            'phone': f'**********{3+i}',
            'age': 30 + i,
            'gender': '男' if i % 2 == 0 else '女',
            'enrollment_date': '2025-06-19',
            'training_status': '未开始'
        }
        
        print(f"\n   测试医生 {full_name} ({username}) 添加患者...")
        result = simulate_add_patient_request('医生', username, patient_data)
        
        if result and result.get('assigned_doctor') == username:
            print(f"   ✅ 医生 {full_name} 添加患者成功")
            success_count += 1
        else:
            print(f"   ❌ 医生 {full_name} 添加患者失败")
    
    return success_count == min(len(doctors), 2)

def verify_patient_assignment():
    """验证患者分配情况"""
    print("\n🔍 验证患者分配情况...")
    
    db = get_db()
    
    # 查询所有测试患者
    test_patients = list(db.personnel.find({
        'name': {'$regex': '^测试患者'}
    }))
    
    print(f"   找到 {len(test_patients)} 个测试患者:")
    
    for patient in test_patients:
        assigned_doctor = patient.get('assigned_doctor', '未分配')
        doctor_name = patient.get('assigned_doctor_name', '未分配')
        assignment_date = patient.get('assignment_date', '未分配')
        
        print(f"   - {patient['name']} ({patient['phone']})")
        print(f"     分配医生: {assigned_doctor}")
        print(f"     医生姓名: {doctor_name}")
        print(f"     分配日期: {assignment_date}")

def cleanup_test_data():
    """清理测试数据"""
    print("\n🧹 清理测试数据...")
    
    db = get_db()
    
    # 删除所有测试患者
    result = db.personnel.delete_many({
        'name': {'$regex': '^测试患者'}
    })
    
    print(f"   删除了 {result.deleted_count} 个测试患者")

def main():
    """主函数"""
    print("🧪 开始测试添加患者时的医生关联功能...\n")
    
    try:
        # 测试管理员添加患者
        admin_test_passed = test_admin_add_patient()
        
        # 测试医生添加患者
        doctor_test_passed = test_doctor_add_patient()
        
        # 测试多个医生添加患者
        multiple_doctors_test_passed = test_multiple_doctors()
        
        # 验证患者分配情况
        verify_patient_assignment()
        
        print("\n" + "="*60)
        print("📊 测试结果总结:")
        print("="*60)
        print(f"管理员添加患者测试: {'✅ 通过' if admin_test_passed else '❌ 失败'}")
        print(f"医生添加患者测试: {'✅ 通过' if doctor_test_passed else '❌ 失败'}")
        print(f"多医生添加患者测试: {'✅ 通过' if multiple_doctors_test_passed else '❌ 失败'}")
        
        if admin_test_passed and doctor_test_passed and multiple_doctors_test_passed:
            print("\n✅ 所有测试通过！")
            print("🎯 功能实现正确:")
            print("   - 管理员添加患者时不自动分配医生")
            print("   - 医生添加患者时自动分配给当前登录医生")
            print("   - 多个医生可以各自添加患者并正确分配")
        else:
            print("\n❌ 部分测试失败，请检查代码实现")
        
        # 清理测试数据
        cleanup_test_data()
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        
        # 即使出错也要清理测试数据
        try:
            cleanup_test_data()
        except:
            pass

if __name__ == "__main__":
    main()
