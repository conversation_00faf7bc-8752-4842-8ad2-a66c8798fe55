#!/usr/bin/env python3
"""
测试用户管理功能的脚本
验证删除按钮和停用按钮的不同功能
"""

import requests
import json

# 配置
BASE_URL = "http://localhost:5000"
ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = "admin123"

def login():
    """登录获取token"""
    response = requests.post(f"{BASE_URL}/api/auth/login", json={
        "username": ADMIN_USERNAME,
        "password": ADMIN_PASSWORD
    })
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            return data['data']['token']
    
    print(f"登录失败: {response.text}")
    return None

def get_users(token):
    """获取用户列表"""
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/api/users", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            return data['data']
    
    print(f"获取用户列表失败: {response.text}")
    return []

def create_test_user(token):
    """创建测试用户"""
    headers = {"Authorization": f"Bearer {token}"}
    user_data = {
        "username": "test_doctor",
        "password": "test123",
        "full_name": "测试医生",
        "email": "<EMAIL>",
        "phone": "13900000000"
    }
    
    response = requests.post(f"{BASE_URL}/api/users", 
                           headers=headers, 
                           json=user_data)
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            print("✅ 测试用户创建成功")
            return True
    
    print(f"❌ 创建测试用户失败: {response.text}")
    return False

def toggle_user_status(token, user_id, is_active):
    """切换用户状态（停用/激活）"""
    headers = {"Authorization": f"Bearer {token}"}
    update_data = {"is_active": not is_active}
    
    response = requests.put(f"{BASE_URL}/api/users/{user_id}", 
                          headers=headers, 
                          json=update_data)
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            action = "激活" if not is_active else "停用"
            print(f"✅ 用户{action}成功")
            return True
    
    print(f"❌ 切换用户状态失败: {response.text}")
    return False

def delete_user(token, user_id):
    """删除用户（隐藏）"""
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.delete(f"{BASE_URL}/api/users/{user_id}", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        if data.get('success'):
            print("✅ 用户删除（隐藏）成功")
            return True
    
    print(f"❌ 删除用户失败: {response.text}")
    return False

def main():
    """主测试流程"""
    print("🔍 开始测试用户管理功能...")
    
    # 1. 登录
    print("\n1. 登录管理员账户...")
    token = login()
    if not token:
        print("❌ 登录失败，测试终止")
        return
    print("✅ 登录成功")
    
    # 2. 获取当前用户列表
    print("\n2. 获取当前用户列表...")
    users = get_users(token)
    print(f"📋 当前用户数量: {len(users)}")
    for user in users:
        print(f"  - {user['full_name']} ({user['username']}) - {'激活' if user['is_active'] else '停用'}")
    
    # 3. 创建测试用户
    print("\n3. 创建测试用户...")
    if not create_test_user(token):
        print("❌ 创建测试用户失败，测试终止")
        return
    
    # 4. 重新获取用户列表，找到测试用户
    print("\n4. 查找测试用户...")
    users = get_users(token)
    test_user = None
    for user in users:
        if user['username'] == 'test_doctor':
            test_user = user
            break
    
    if not test_user:
        print("❌ 找不到测试用户")
        return
    
    print(f"✅ 找到测试用户: {test_user['full_name']} (ID: {test_user['id']})")
    
    # 5. 测试停用功能
    print("\n5. 测试停用功能...")
    if toggle_user_status(token, test_user['id'], test_user['is_active']):
        # 验证停用后用户仍在列表中
        users_after_disable = get_users(token)
        disabled_user = None
        for user in users_after_disable:
            if user['id'] == test_user['id']:
                disabled_user = user
                break
        
        if disabled_user and not disabled_user['is_active']:
            print("✅ 停用功能正常：用户仍在列表中，但状态为停用")
        else:
            print("❌ 停用功能异常")
    
    # 6. 重新激活用户
    print("\n6. 重新激活用户...")
    toggle_user_status(token, test_user['id'], False)  # False表示当前是停用状态
    
    # 7. 测试删除（隐藏）功能
    print("\n7. 测试删除（隐藏）功能...")
    if delete_user(token, test_user['id']):
        # 验证删除后用户不在列表中
        users_after_delete = get_users(token)
        hidden_user = None
        for user in users_after_delete:
            if user['id'] == test_user['id']:
                hidden_user = user
                break
        
        if not hidden_user:
            print("✅ 删除功能正常：用户已从列表中隐藏")
        else:
            print("❌ 删除功能异常：用户仍在列表中")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
