import pymongo
import bcrypt

# 连接数据库
client = pymongo.MongoClient('mongodb://localhost:27017')
db = client['med_call_records']

def verify_password(password, password_hash):
    """验证密码"""
    return bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8'))

def test_login(username, password):
    # 查找用户
    user = db.sys_info.find_one({
        'doc_type': 'user_account',
        'username': username,
        'is_active': True
    })
    
    if not user:
        print(f'❌ 用户 {username} 不存在或未激活')
        return False
    
    # 验证密码
    try:
        if verify_password(password, user['password_hash']):
            print(f'✅ 用户 {username} 登录成功')
            return True
        else:
            print(f'❌ 用户 {username} 密码错误')
            return False
    except Exception as e:
        print(f'❌ 密码验证出错: {e}')
        return False

# 测试登录
print('测试登录功能:')
print('=' * 40)

# 测试admin用户
test_login('admin', 'admin123')

# 测试doctor用户
test_login('doctor', 'doctor123')

# 测试doctor01用户
test_login('doctor01', 'doctor123')

# 检查admin用户的密码哈希
admin_user = db.sys_info.find_one({'username': 'admin'})
if admin_user:
    print(f'\nadmin用户密码哈希: {admin_user["password_hash"][:50]}...') 