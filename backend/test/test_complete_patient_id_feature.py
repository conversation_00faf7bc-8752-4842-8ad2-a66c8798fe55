#!/usr/bin/env python3
"""
完整测试患者编号功能
包括数据库迁移、后端API和前端集成测试
"""

import requests
import json
import sys
import os
from database import get_db

# 后端服务地址
BASE_URL = 'http://localhost:5000'

def test_database_migration():
    """测试数据库迁移结果"""
    print("🔍 测试数据库迁移结果...")
    
    db = get_db()
    
    # 检查所有患者是否都有patient_id
    patients = list(db.personnel.find({}))
    patients_without_id = [p for p in patients if 'patient_id' not in p]
    
    if patients_without_id:
        print(f"❌ 还有 {len(patients_without_id)} 个患者没有patient_id")
        return False
    
    # 检查patient_id的唯一性
    patient_ids = [p['patient_id'] for p in patients]
    unique_ids = set(patient_ids)
    
    if len(patient_ids) != len(unique_ids):
        print("❌ 发现重复的patient_id")
        return False
    
    print(f"✅ 数据库迁移验证通过: {len(patients)} 个患者都有唯一的patient_id")
    
    # 显示患者编号列表
    print("📋 患者编号列表:")
    for patient in sorted(patients, key=lambda x: x['patient_id']):
        assigned_doctor = patient.get('assigned_doctor', '未分配')
        print(f"   {patient['patient_id']} - {patient['name']} - {assigned_doctor}")
    
    return True

def login_user(username, password):
    """用户登录获取token"""
    login_url = f"{BASE_URL}/api/auth/login"
    login_data = {
        'username': username,
        'password': password
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                token = result.get('data', {}).get('token')
                user_info = result.get('data', {}).get('user', {})
                return token, user_info
            else:
                print(f"❌ 登录失败: {result.get('error')}")
                return None, None
        else:
            print(f"❌ 登录请求失败: {response.status_code}")
            return None, None
    except Exception as e:
        print(f"❌ 登录过程中出错: {e}")
        return None, None

def test_api_with_patient_id():
    """测试API接口的患者编号功能"""
    print("\n🔍 测试API接口的患者编号功能...")
    
    # 管理员登录
    token, user_info = login_user('admin', 'admin123')
    if not token:
        print("❌ 管理员登录失败")
        return False
    
    print(f"✅ {user_info.get('role')} 登录成功")
    
    # 测试获取患者列表
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(f"{BASE_URL}/api/personnel", headers=headers)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                patients = result.get('data', [])
                print(f"✅ 获取患者列表成功: {len(patients)} 个患者")
                
                # 检查每个患者是否都有patient_id
                patients_with_id = [p for p in patients if 'patient_id' in p]
                if len(patients_with_id) == len(patients):
                    print("✅ 所有患者都包含patient_id字段")
                    
                    # 显示前几个患者的信息
                    print("📋 患者信息示例:")
                    for patient in patients[:3]:
                        print(f"   {patient.get('patient_id')} - {patient.get('name')} - {patient.get('phone')}")
                    
                    return True
                else:
                    print(f"❌ 有 {len(patients) - len(patients_with_id)} 个患者缺少patient_id字段")
                    return False
            else:
                print(f"❌ 获取患者列表失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 获取患者列表请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 测试API时出错: {e}")
        return False

def test_add_patient_with_custom_id():
    """测试添加自定义编号的患者"""
    print("\n🔍 测试添加自定义编号的患者...")
    
    # 医生登录
    token, user_info = login_user('doctor01', 'doctor123')
    if not token:
        print("❌ 医生登录失败")
        return False
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # 添加患者
    patient_data = {
        'patient_id': 'CUSTOM001',
        'name': '自定义编号测试患者',
        'phone': '13900000999',
        'age': 45,
        'gender': '女',
        'enrollment_date': '2025-06-19',
        'training_status': '未开始'
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/personnel", json=patient_data, headers=headers)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                patient = result.get('data', {})
                print(f"✅ 添加患者成功:")
                print(f"   编号: {patient.get('patient_id')}")
                print(f"   姓名: {patient.get('name')}")
                print(f"   分配医生: {patient.get('assigned_doctor')}")
                
                # 验证编号是否正确
                if patient.get('patient_id') == 'CUSTOM001':
                    print("✅ 自定义编号设置正确")
                    
                    # 清理测试数据
                    patient_id = patient.get('id') or patient.get('_id')
                    if patient_id:
                        delete_response = requests.delete(f"{BASE_URL}/api/personnel/{patient_id}", headers=headers)
                        if delete_response.status_code == 200:
                            print("✅ 测试数据清理完成")
                    
                    return True
                else:
                    print(f"❌ 编号设置错误: 期望 CUSTOM001，实际 {patient.get('patient_id')}")
                    return False
            else:
                print(f"❌ 添加患者失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 添加患者请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 测试添加患者时出错: {e}")
        return False

def test_patient_id_validation():
    """测试患者编号验证"""
    print("\n🔍 测试患者编号验证...")
    
    # 医生登录
    token, user_info = login_user('doctor01', 'doctor123')
    if not token:
        print("❌ 医生登录失败")
        return False
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # 测试1: 空编号
    print("   测试空编号...")
    patient_data = {
        'patient_id': '',
        'name': '空编号测试',
        'phone': '13900000998',
        'age': 30,
        'gender': '男',
        'enrollment_date': '2025-06-19',
        'training_status': '未开始'
    }
    
    response = requests.post(f"{BASE_URL}/api/personnel", json=patient_data, headers=headers)
    if response.status_code != 200:
        print("   ✅ 空编号验证正确：被拒绝")
    else:
        print("   ❌ 空编号验证失败：应该被拒绝")
        return False
    
    # 测试2: 重复编号（使用现有的编号）
    print("   测试重复编号...")
    patient_data['patient_id'] = 'P0001'  # 使用现有编号
    patient_data['phone'] = '13900000997'
    
    response = requests.post(f"{BASE_URL}/api/personnel", json=patient_data, headers=headers)
    if response.status_code != 200:
        print("   ✅ 重复编号验证正确：被拒绝")
    else:
        print("   ❌ 重复编号验证失败：应该被拒绝")
        return False
    
    print("✅ 患者编号验证测试通过")
    return True

def test_search_by_patient_id():
    """测试按患者编号搜索"""
    print("\n🔍 测试按患者编号搜索...")
    
    # 管理员登录
    token, user_info = login_user('admin', 'admin123')
    if not token:
        print("❌ 管理员登录失败")
        return False
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # 搜索特定编号的患者
    search_term = 'P0001'
    try:
        response = requests.get(f"{BASE_URL}/api/personnel?search={search_term}", headers=headers)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                patients = result.get('data', [])
                
                # 检查是否找到了正确的患者
                found_patient = None
                for patient in patients:
                    if patient.get('patient_id') == search_term:
                        found_patient = patient
                        break
                
                if found_patient:
                    print(f"✅ 按编号搜索成功: 找到患者 {found_patient['name']} ({found_patient['patient_id']})")
                    return True
                else:
                    print(f"❌ 按编号搜索失败: 未找到编号为 {search_term} 的患者")
                    return False
            else:
                print(f"❌ 搜索请求失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 搜索请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 测试搜索时出错: {e}")
        return False

def main():
    """主函数"""
    print("🧪 开始完整测试患者编号功能...\n")
    
    # 检查后端服务是否运行
    try:
        response = requests.get(f"{BASE_URL}/api/health", timeout=5)
        if response.status_code != 200:
            print("❌ 后端服务未正常运行，请先启动后端服务")
            return
    except:
        print("❌ 无法连接到后端服务，请确保后端服务在 http://localhost:5000 运行")
        return
    
    print("✅ 后端服务连接正常\n")
    
    try:
        # 1. 测试数据库迁移
        migration_passed = test_database_migration()
        
        # 2. 测试API接口
        api_passed = test_api_with_patient_id()
        
        # 3. 测试添加自定义编号患者
        add_passed = test_add_patient_with_custom_id()
        
        # 4. 测试编号验证
        validation_passed = test_patient_id_validation()
        
        # 5. 测试搜索功能
        search_passed = test_search_by_patient_id()
        
        print("\n" + "="*60)
        print("📊 患者编号功能完整测试结果:")
        print("="*60)
        print(f"数据库迁移测试: {'✅ 通过' if migration_passed else '❌ 失败'}")
        print(f"API接口测试: {'✅ 通过' if api_passed else '❌ 失败'}")
        print(f"添加自定义编号测试: {'✅ 通过' if add_passed else '❌ 失败'}")
        print(f"编号验证测试: {'✅ 通过' if validation_passed else '❌ 失败'}")
        print(f"搜索功能测试: {'✅ 通过' if search_passed else '❌ 失败'}")
        
        all_passed = all([migration_passed, api_passed, add_passed, validation_passed, search_passed])
        
        if all_passed:
            print("\n🎉 所有患者编号功能测试通过！")
            print("🎯 功能实现完整:")
            print("   ✅ 数据库迁移：为现有患者添加了编号")
            print("   ✅ 后端API：支持患者编号的增删改查")
            print("   ✅ 编号验证：确保唯一性和非空")
            print("   ✅ 医生关联：医生添加患者时自动分配")
            print("   ✅ 搜索功能：支持按编号搜索患者")
            print("\n💡 前端使用指南:")
            print("   - 添加患者时必须填写患者编号")
            print("   - 患者编号在整个系统中唯一")
            print("   - 可以使用任意格式的编号（如：P0001, EXP001, TEST-001等）")
            print("   - 医生添加的患者会自动分配给当前登录医生")
            print("   - 管理员添加的患者不会自动分配医生")
        else:
            print("\n❌ 部分功能测试失败，请检查实现")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
