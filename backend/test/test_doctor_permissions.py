#!/usr/bin/env python3
"""
测试医生权限控制功能
验证管理员和医生登录后看到的数据是否正确过滤
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from auth.auth import get_user_patient_filter, get_user_call_records_filter
from database import get_db

def test_admin_permissions():
    """测试管理员权限"""
    print("🔍 测试管理员权限...")
    
    admin_user = {'role': '管理员', 'username': 'admin'}
    
    # 测试患者过滤
    patient_filter = get_user_patient_filter(admin_user)
    print(f"   管理员患者过滤条件: {patient_filter}")
    
    db = get_db()
    
    # 查询患者数量
    total_patients = db.personnel.count_documents(patient_filter)
    print(f"   管理员可见患者数: {total_patients}")
    
    # 测试通话记录过滤
    call_filter = get_user_call_records_filter(admin_user, db)
    print(f"   管理员通话记录过滤条件: {call_filter}")
    
    total_calls = db.call_records.count_documents(call_filter)
    print(f"   管理员可见通话记录数: {total_calls}")
    
    return total_patients, total_calls

def test_doctor_permissions(doctor_username):
    """测试指定医生的权限"""
    print(f"\n🔍 测试医生权限: {doctor_username}")
    
    doctor_user = {'role': '医生', 'username': doctor_username}
    
    # 测试患者过滤
    patient_filter = get_user_patient_filter(doctor_user)
    print(f"   医生患者过滤条件: {patient_filter}")
    
    db = get_db()
    
    # 查询患者数量
    doctor_patients = db.personnel.count_documents(patient_filter)
    print(f"   医生可见患者数: {doctor_patients}")
    
    # 列出医生管理的患者
    patients = list(db.personnel.find(patient_filter, {'name': 1, 'phone': 1}))
    print(f"   医生管理的患者:")
    for patient in patients:
        print(f"     - {patient['name']} ({patient['phone']})")
    
    # 测试通话记录过滤
    call_filter = get_user_call_records_filter(doctor_user, db)
    print(f"   医生通话记录过滤条件: {call_filter}")
    
    doctor_calls = db.call_records.count_documents(call_filter)
    print(f"   医生可见通话记录数: {doctor_calls}")
    
    return doctor_patients, doctor_calls

def test_permission_isolation():
    """测试权限隔离"""
    print("\n🔒 测试权限隔离...")
    
    db = get_db()
    
    # 获取所有医生
    doctors = list(db.sys_info.find({
        'doc_type': 'user_account',
        'role': '医生',
        'is_active': True
    }, {'username': 1, 'full_name': 1}))
    
    print(f"   找到 {len(doctors)} 个医生账户")
    
    doctor_data = {}
    
    for doctor in doctors:
        username = doctor['username']
        doctor_user = {'role': '医生', 'username': username}
        
        # 获取该医生的患者
        patient_filter = get_user_patient_filter(doctor_user)
        patients = list(db.personnel.find(patient_filter, {'name': 1, 'phone': 1}))
        
        doctor_data[username] = {
            'name': doctor.get('full_name', username),
            'patients': patients
        }
    
    # 检查是否有患者重叠
    all_patient_phones = []
    for username, data in doctor_data.items():
        patient_phones = [p['phone'] for p in data['patients']]
        all_patient_phones.extend(patient_phones)
        
        print(f"   医生 {data['name']} ({username}): {len(patient_phones)} 个患者")
        for patient in data['patients']:
            print(f"     - {patient['name']} ({patient['phone']})")
    
    # 检查重复
    unique_phones = set(all_patient_phones)
    if len(all_patient_phones) == len(unique_phones):
        print("   ✅ 权限隔离正常：没有患者重叠")
    else:
        print("   ⚠️  发现患者重叠，可能存在权限问题")
        duplicates = [phone for phone in all_patient_phones if all_patient_phones.count(phone) > 1]
        print(f"   重复的患者手机号: {set(duplicates)}")

def test_unauthorized_access():
    """测试未授权访问"""
    print("\n🚫 测试未授权访问...")
    
    # 测试无效角色
    invalid_user = {'role': '护士', 'username': 'nurse01'}
    patient_filter = get_user_patient_filter(invalid_user)
    print(f"   无效角色患者过滤条件: {patient_filter}")
    
    db = get_db()
    invalid_patients = db.personnel.count_documents(patient_filter)
    print(f"   无效角色可见患者数: {invalid_patients}")
    
    if invalid_patients == 0:
        print("   ✅ 未授权访问控制正常")
    else:
        print("   ❌ 未授权访问控制失败")

def test_api_simulation():
    """模拟API调用测试"""
    print("\n🌐 模拟API调用测试...")
    
    db = get_db()
    
    # 模拟管理员调用患者列表API
    admin_user = {'role': '管理员', 'username': 'admin'}
    admin_filter = get_user_patient_filter(admin_user)
    admin_patients = list(db.personnel.find(admin_filter).limit(5))
    print(f"   管理员API调用 - 患者列表: {len(admin_patients)} 条记录")
    
    # 模拟医生调用患者列表API
    doctor_user = {'role': '医生', 'username': 'doctor01'}
    doctor_filter = get_user_patient_filter(doctor_user)
    doctor_patients = list(db.personnel.find(doctor_filter).limit(5))
    print(f"   医生API调用 - 患者列表: {len(doctor_patients)} 条记录")
    
    # 模拟医生调用通话记录API
    call_filter = get_user_call_records_filter(doctor_user, db)
    doctor_calls = list(db.call_records.find(call_filter).limit(5))
    print(f"   医生API调用 - 通话记录: {len(doctor_calls)} 条记录")
    
    # 验证医生只能看到自己的患者
    if doctor_patients:
        for patient in doctor_patients:
            assigned_doctor = patient.get('assigned_doctor')
            if assigned_doctor != doctor_user['username']:
                print(f"   ❌ 权限错误: 医生看到了不属于自己的患者 {patient['name']}")
                return False
        print("   ✅ 医生权限控制正常")
    
    return True

def main():
    """主函数"""
    print("🧪 开始测试医生权限控制功能...\n")
    
    try:
        # 测试管理员权限
        admin_patients, admin_calls = test_admin_permissions()
        
        # 测试医生权限
        doctor_patients, doctor_calls = test_doctor_permissions('doctor01')
        
        # 测试权限隔离
        test_permission_isolation()
        
        # 测试未授权访问
        test_unauthorized_access()
        
        # 模拟API调用
        api_test_passed = test_api_simulation()
        
        print("\n" + "="*60)
        print("📊 测试结果总结:")
        print("="*60)
        print(f"管理员可见患者数: {admin_patients}")
        print(f"管理员可见通话记录数: {admin_calls}")
        print(f"医生可见患者数: {doctor_patients}")
        print(f"医生可见通话记录数: {doctor_calls}")
        
        if api_test_passed:
            print("\n✅ 所有权限控制测试通过！")
            print("🎯 功能实现总结:")
            print("   - 管理员可以看到所有患者和通话记录")
            print("   - 医生只能看到自己管理的患者和相关通话记录")
            print("   - 权限隔离正常，没有数据泄露")
            print("   - 未授权访问被正确阻止")
        else:
            print("\n❌ 部分权限控制测试失败，请检查配置")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
