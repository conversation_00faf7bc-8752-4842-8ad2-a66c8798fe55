from flask import Blueprint, request, jsonify
from datetime import datetime
from bson import ObjectId
from database import get_db
from auth.auth import require_auth, require_doctor_filter, get_user_patient_filter
from utils.serializers import serialize_doc

personnel_bp = Blueprint('personnel', __name__, url_prefix='/api/personnel')

@personnel_bp.route('', methods=['GET'])
@require_auth
@require_doctor_filter
def get_personnel():
    """获取患者列表"""
    try:
        db = get_db()
        search = request.args.get('search', '')
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 50))

        # 构建查询条件
        query = {}
        if search:
            query['$or'] = [
                {'name': {'$regex': search, '$options': 'i'}},
                {'phone': {'$regex': search, '$options': 'i'}},
                {'patient_id': {'$regex': search, '$options': 'i'}}
            ]

        # 添加医生权限过滤
        patient_filter = get_user_patient_filter(request.current_user)
        query.update(patient_filter)

        # 获取总数
        total = db.personnel.count_documents(query)

        # 分页查询
        skip = (page - 1) * limit
        cursor = db.personnel.find(query).skip(skip).limit(limit)
        personnel = [serialize_doc(doc) for doc in cursor]

        return jsonify({
            'success': True,
            'data': personnel,
            'total': total,
            'page': page,
            'limit': limit
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@personnel_bp.route('/batch', methods=['DELETE'])
@require_auth
@require_doctor_filter
def batch_delete_personnel():
    """批量删除患者"""
    try:
        db = get_db()
        data = request.get_json()

        if not data or 'ids' not in data:
            return jsonify({
                'success': False,
                'error': '缺少患者ID列表'
            }), 400

        ids = data['ids']
        if not ids or not isinstance(ids, list):
            return jsonify({
                'success': False,
                'error': '患者ID列表格式错误'
            }), 400

        # 转换ID格式
        object_ids = []
        for id_str in ids:
            try:
                object_ids.append(ObjectId(id_str))
            except:
                return jsonify({
                    'success': False,
                    'error': f'无效的患者ID: {id_str}'
                }), 400

        # 添加医生权限过滤
        patient_filter = get_user_patient_filter(request.current_user)
        delete_query = {'_id': {'$in': object_ids}}
        delete_query.update(patient_filter)

        # 批量删除（只能删除自己管理的患者）
        result = db.personnel.delete_many(delete_query)

        return jsonify({
            'success': True,
            'message': f'成功删除 {result.deleted_count} 个患者',
            'deleted_count': result.deleted_count
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@personnel_bp.route('/batch', methods=['PUT'])
@require_auth
@require_doctor_filter
def batch_update_personnel():
    """批量更新患者训练状态"""
    try:
        db = get_db()
        data = request.get_json()

        if not data or 'ids' not in data or 'training_status' not in data:
            return jsonify({
                'success': False,
                'error': '缺少必要参数'
            }), 400

        ids = data['ids']
        training_status = data['training_status']

        if not ids or not isinstance(ids, list):
            return jsonify({
                'success': False,
                'error': '患者ID列表格式错误'
            }), 400

        # 验证训练状态值
        valid_statuses = ['未开始', '训练中', '暂停', '终止', '休息']
        if training_status not in valid_statuses:
            return jsonify({
                'success': False,
                'error': f'无效的训练状态: {training_status}'
            }), 400

        # 转换ID格式
        object_ids = []
        for id_str in ids:
            try:
                object_ids.append(ObjectId(id_str))
            except:
                return jsonify({
                    'success': False,
                    'error': f'无效的患者ID: {id_str}'
                }), 400

        # 添加医生权限过滤
        patient_filter = get_user_patient_filter(request.current_user)
        update_query = {'_id': {'$in': object_ids}}
        update_query.update(patient_filter)

        # 批量更新（只能更新自己管理的患者）
        result = db.personnel.update_many(
            update_query,
            {
                '$set': {
                    'training_status': training_status,
                    'updated_at': datetime.utcnow()
                }
            }
        )

        return jsonify({
            'success': True,
            'message': f'成功更新 {result.modified_count} 个患者的训练状态',
            'modified_count': result.modified_count
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@personnel_bp.route('/<personnel_id>', methods=['GET'])
@require_auth
@require_doctor_filter
def get_personnel_detail(personnel_id):
    """获取患者详情"""
    try:
        db = get_db()

        # 添加医生权限过滤
        patient_filter = get_user_patient_filter(request.current_user)
        query = {'_id': ObjectId(personnel_id)}
        query.update(patient_filter)

        # 获取患者基本信息（带权限过滤）
        personnel = db.personnel.find_one(query)
        if not personnel:
            return jsonify({
                'success': False,
                'error': '患者不存在或您没有权限访问'
            }), 404

        # 获取患者通话记录
        call_records = [serialize_doc(doc) for doc in db.call_records.find({
            '手机号': personnel['phone']
        }).sort([('创建时间', -1)])]

        return jsonify({
            'success': True,
            'data': {
                'personnel': serialize_doc(personnel),
                'call_records': call_records
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@personnel_bp.route('', methods=['POST'])
@require_auth
@require_doctor_filter
def add_personnel():
    """添加新患者"""
    try:
        db = get_db()
        data = request.get_json()

        # 验证必填字段
        required_fields = ['name', 'phone', 'age', 'gender', 'patient_id']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'缺少必填字段: {field}'
                }), 400

        # 验证锻炼计划是否存在（如果提供了）
        if 'exercise_plan_id' in data and data['exercise_plan_id']:
            exercise_plan = db.exercise_plans.find_one({'_id': ObjectId(data['exercise_plan_id'])})
            if not exercise_plan:
                return jsonify({
                    'success': False,
                    'error': '指定的锻炼计划不存在'
                }), 400
            # 添加锻炼计划名称（冗余字段，便于查询显示）
            data['exercise_plan_name'] = exercise_plan['plan_name']
        else:
            data['exercise_plan_id'] = None
            data['exercise_plan_name'] = None

        # 检查电话号码是否已存在
        existing = db.personnel.find_one({'phone': data['phone']})
        if existing:
            return jsonify({
                'success': False,
                'error': '该电话号码已存在'
            }), 400

        # 检查患者编号是否已存在
        existing_patient_id = db.personnel.find_one({'patient_id': data['patient_id']})
        if existing_patient_id:
            return jsonify({
                'success': False,
                'error': '该患者编号已存在'
            }), 400

        # 验证患者编号格式（可选：如果需要特定格式）
        patient_id = data['patient_id'].strip()
        if not patient_id:
            return jsonify({
                'success': False,
                'error': '患者编号不能为空'
            }), 400

        # 更新数据中的患者编号（去除空格）
        data['patient_id'] = patient_id

        # 根据用户角色处理医生关联
        current_user = request.current_user
        if current_user.get('role') == '医生':
            # 医生添加患者时，自动关联到当前医生
            doctor_username = current_user.get('username')

            # 获取医生的详细信息
            doctor_info = db.sys_info.find_one({
                'doc_type': 'user_account',
                'username': doctor_username,
                'role': '医生'
            })

            if doctor_info:
                data['assigned_doctor'] = doctor_username
                data['assigned_doctor_name'] = doctor_info.get('full_name', doctor_username)
                data['assignment_date'] = datetime.now().strftime('%Y-%m-%d')
                print(f"✅ 患者 {data['name']} 自动分配给医生 {doctor_info.get('full_name')} ({doctor_username})")
            else:
                print(f"⚠️  警告: 未找到医生信息 {doctor_username}")

        elif current_user.get('role') == '管理员':
            # 管理员添加患者时，不自动关联医生
            print(f"✅ 管理员添加患者 {data['name']}，不自动分配医生")

        # 添加时间戳
        data['created_at'] = datetime.utcnow()
        data['updated_at'] = datetime.utcnow()

        # 插入数据
        result = db.personnel.insert_one(data)

        # 获取插入的文档并序列化
        inserted_doc = db.personnel.find_one({'_id': result.inserted_id})

        return jsonify({
            'success': True,
            'data': serialize_doc(inserted_doc)
        })

    except Exception as e:
        print(f"❌ 添加患者时出错: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@personnel_bp.route('/<personnel_id>', methods=['PUT'])
@require_auth
@require_doctor_filter
def update_personnel(personnel_id):
    """更新患者信息"""
    try:
        db = get_db()
        data = request.get_json()

        # 添加医生权限过滤
        patient_filter = get_user_patient_filter(request.current_user)
        query = {'_id': ObjectId(personnel_id)}
        query.update(patient_filter)

        # 验证患者是否存在且有权限访问
        personnel = db.personnel.find_one(query)
        if not personnel:
            return jsonify({
                'success': False,
                'error': '患者不存在或您没有权限访问'
            }), 404

        # 如果更新电话号码，检查是否与其他患者重复
        if 'phone' in data and data['phone'] != personnel['phone']:
            existing = db.personnel.find_one({
                'phone': data['phone'],
                '_id': {'$ne': ObjectId(personnel_id)}
            })
            if existing:
                return jsonify({
                    'success': False,
                    'error': '该电话号码已被其他患者使用'
                }), 400

        # 如果更新患者编号，检查是否与其他患者重复
        if 'patient_id' in data and data['patient_id'] != personnel.get('patient_id'):
            # 验证患者编号格式
            patient_id = data['patient_id'].strip()
            if not patient_id:
                return jsonify({
                    'success': False,
                    'error': '患者编号不能为空'
                }), 400

            # 检查是否与其他患者重复
            existing_patient_id = db.personnel.find_one({
                'patient_id': patient_id,
                '_id': {'$ne': ObjectId(personnel_id)}
            })
            if existing_patient_id:
                return jsonify({
                    'success': False,
                    'error': '该患者编号已被其他患者使用'
                }), 400

            # 更新数据中的患者编号（去除空格）
            data['patient_id'] = patient_id

        # 验证锻炼计划是否存在（如果提供了）
        if 'exercise_plan_id' in data:
            if data['exercise_plan_id']:
                exercise_plan = db.exercise_plans.find_one({'_id': ObjectId(data['exercise_plan_id'])})
                if not exercise_plan:
                    return jsonify({
                        'success': False,
                        'error': '指定的锻炼计划不存在'
                    }), 400
                # 添加锻炼计划名称（冗余字段，便于查询显示）
                data['exercise_plan_name'] = exercise_plan['plan_name']
            else:
                # 如果传入空值，则清除锻炼计划关联
                data['exercise_plan_id'] = None
                data['exercise_plan_name'] = None

        # 添加更新时间戳
        data['updated_at'] = datetime.utcnow()

        # 更新数据（带权限过滤）
        result = db.personnel.update_one(
            query,
            {'$set': data}
        )

        if result.modified_count > 0:
            # 获取更新后的数据
            updated_personnel = db.personnel.find_one(query)
            return jsonify({
                'success': True,
                'data': serialize_doc(updated_personnel)
            })
        else:
            return jsonify({
                'success': True,
                'message': '没有数据被修改',
                'data': serialize_doc(personnel)
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@personnel_bp.route('/<personnel_id>', methods=['DELETE'])
@require_auth
@require_doctor_filter
def delete_personnel(personnel_id):
    """删除患者"""
    try:
        db = get_db()

        # 添加医生权限过滤
        patient_filter = get_user_patient_filter(request.current_user)
        query = {'_id': ObjectId(personnel_id)}
        query.update(patient_filter)

        # 验证患者是否存在且有权限访问
        personnel = db.personnel.find_one(query)
        if not personnel:
            return jsonify({
                'success': False,
                'error': '患者不存在或您没有权限访问'
            }), 404

        # 删除患者（带权限过滤）
        result = db.personnel.delete_one(query)

        if result.deleted_count > 0:
            return jsonify({
                'success': True,
                'message': '患者删除成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '删除失败'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@personnel_bp.route('/exercise-plans', methods=['GET'])
@require_auth
def get_available_exercise_plans():
    """获取可用的锻炼计划列表（用于下拉框）"""
    try:
        db = get_db()

        # 获取当前用户信息
        from flask import g
        current_user = getattr(g, 'current_user', {})
        current_username = current_user.get('username', '')
        current_role = current_user.get('role', '')

        # 构建查询条件
        query = {'status': {'$in': ['active', 'completed']}}  # 只显示活跃和已完成的计划

        # 权限控制：医生只能看到自己创建的计划，管理员可以看到所有计划
        if current_role != '管理员':
            query['created_by'] = current_username

        # 获取锻炼计划列表
        plans = list(db.exercise_plans.find(
            query,
            {'_id': 1, 'plan_name': 1, 'description': 1, 'status': 1}
        ).sort('created_at', -1))

        return jsonify({
            'success': True,
            'data': [serialize_doc(plan) for plan in plans]
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500