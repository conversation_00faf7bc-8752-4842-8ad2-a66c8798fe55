"""
锻炼计划管理路由
提供锻炼计划的CRUD操作API
"""

from flask import Blueprint, request, jsonify
from datetime import datetime, timedelta
from bson import ObjectId
from database import get_db
from auth.auth import require_auth
from utils.serializers import serialize_doc
import logging

logger = logging.getLogger(__name__)

exercise_plan_bp = Blueprint('exercise_plans', __name__, url_prefix='/api/exercise-plans')

def generate_plan_id():
    """生成计划编号"""
    today = datetime.now().strftime("%Y%m%d")
    db = get_db()
    
    # 查询今日最大序号
    today_plans = list(db.exercise_plans.find({
        "plan_id": {"$regex": f"^EP_{today}_"}
    }).sort("plan_id", -1).limit(1))
    
    if today_plans:
        last_id = today_plans[0]["plan_id"]
        last_seq = int(last_id.split("_")[-1])
        new_seq = last_seq + 1
    else:
        new_seq = 1
    
    return f"EP_{today}_{new_seq:03d}"

def calculate_end_date(start_date: str, total_weeks: int) -> str:
    """计算结束日期"""
    start = datetime.strptime(start_date, "%Y-%m-%d")
    end = start + timedelta(weeks=total_weeks)
    return end.strftime("%Y-%m-%d")

def calculate_weekly_target(week_number: int, initial_count: int, increment: int) -> int:
    """计算指定周的目标次数"""
    return initial_count + (week_number - 1) * increment

@exercise_plan_bp.route('', methods=['GET'])
@require_auth
def get_exercise_plans():
    """获取锻炼计划列表"""
    try:
        db = get_db()

        # 获取当前用户信息
        from flask import g
        current_user = getattr(g, 'current_user', {})
        current_username = current_user.get('username', '')
        current_role = current_user.get('role', '')

        # 获取查询参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        patient_id = request.args.get('patient_id')
        status = request.args.get('status')
        doctor_id = request.args.get('doctor_id')

        # 构建查询条件
        query = {}

        # 权限控制：医生只能看到自己创建的计划，管理员可以看到所有计划
        if current_role != '管理员':
            query['created_by'] = current_username

        if patient_id:
            query['patient_id'] = patient_id
        if status:
            query['status'] = status
        if doctor_id and current_role == '管理员':  # 只有管理员可以按医生筛选
            query['doctor_id'] = doctor_id
        
        # 分页查询
        skip = (page - 1) * page_size
        plans = list(db.exercise_plans.find(query)
                    .sort('created_at', -1)
                    .skip(skip)
                    .limit(page_size))
        
        # 获取总数
        total = db.exercise_plans.count_documents(query)
        
        return jsonify({
            'success': True,
            'data': [serialize_doc(plan) for plan in plans],
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total': total,
                'total_pages': (total + page_size - 1) // page_size
            }
        })
        
    except Exception as e:
        logger.error(f"获取锻炼计划列表失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@exercise_plan_bp.route('/<plan_id>', methods=['GET'])
@require_auth
def get_exercise_plan(plan_id):
    """获取单个锻炼计划详情"""
    try:
        db = get_db()

        # 获取当前用户信息
        from flask import g
        current_user = getattr(g, 'current_user', {})
        current_username = current_user.get('username', '')
        current_role = current_user.get('role', '')

        # 支持通过ObjectId或plan_id查询
        if ObjectId.is_valid(plan_id):
            query = {'_id': ObjectId(plan_id)}
        else:
            query = {'plan_id': plan_id}

        # 权限控制：医生只能查看自己创建的计划
        if current_role != '管理员':
            query['created_by'] = current_username

        plan = db.exercise_plans.find_one(query)
        
        if not plan:
            return jsonify({
                'success': False,
                'error': '锻炼计划不存在'
            }), 404
        
        return jsonify({
            'success': True,
            'data': serialize_doc(plan)
        })
        
    except Exception as e:
        logger.error(f"获取锻炼计划详情失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@exercise_plan_bp.route('', methods=['POST'])
@require_auth
def create_exercise_plan():
    """创建新的锻炼计划"""
    try:
        db = get_db()
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['patient_id', 'plan_name', 'total_weeks', 
                          'initial_daily_count', 'weekly_increment', 
                          'max_rest_days_per_week', 'start_date']
        
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'缺少必填字段: {field}'
                }), 400
        
        # 验证患者是否存在（如果不是模板计划）
        patient_name = "模板计划"
        if data['patient_id'] != 'TEMPLATE':
            patient = db.personnel.find_one({'patient_id': data['patient_id']})
            if not patient:
                return jsonify({
                    'success': False,
                    'error': '患者不存在'
                }), 400
            patient_name = patient['name']
        
        # 验证数据范围
        if not (1 <= data['total_weeks'] <= 52):
            return jsonify({
                'success': False,
                'error': '锻炼周数必须在1-52周之间'
            }), 400
        
        if not (1 <= data['initial_daily_count'] <= 2000):
            return jsonify({
                'success': False,
                'error': '初始每日次数必须在1-2000次之间'
            }), 400
        
        if not (0 <= data['weekly_increment'] <= 500):
            return jsonify({
                'success': False,
                'error': '每周递增次数必须在0-500次之间'
            }), 400
        
        if not (0 <= data['max_rest_days_per_week'] <= 7):
            return jsonify({
                'success': False,
                'error': '每周最大休息天数必须在0-7天之间'
            }), 400
        
        # 获取当前用户信息（从JWT token中）
        from flask import g
        current_user = getattr(g, 'current_user', {})
        
        # 构建锻炼计划数据
        plan_data = {
            'plan_id': generate_plan_id(),
            'patient_id': data['patient_id'],
            'patient_name': patient_name,
            'doctor_id': current_user.get('username', ''),
            'doctor_name': current_user.get('full_name', ''),
            
            'plan_name': data['plan_name'],
            'description': data.get('description', ''),
            'status': 'active',
            
            'total_weeks': data['total_weeks'],
            'initial_daily_count': data['initial_daily_count'],
            'weekly_increment': data['weekly_increment'],
            'max_rest_days_per_week': data['max_rest_days_per_week'],
            
            'start_date': data['start_date'],
            'end_date': calculate_end_date(data['start_date'], data['total_weeks']),
            'current_week': 1,
            'current_daily_target': data['initial_daily_count'],
            
            'weekly_records': [],
            
            'total_target_count': 0,
            'total_actual_count': 0,
            'overall_completion_rate': 0.0,
            'consecutive_days': 0,
            'total_rest_days': 0,
            
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow(),
            'created_by': current_user.get('username', ''),
            'last_modified_by': current_user.get('username', '')
        }
        
        # 插入数据库
        result = db.exercise_plans.insert_one(plan_data)
        
        # 返回创建的计划
        created_plan = db.exercise_plans.find_one({'_id': result.inserted_id})
        
        return jsonify({
            'success': True,
            'data': serialize_doc(created_plan),
            'message': '锻炼计划创建成功'
        }), 201
        
    except Exception as e:
        logger.error(f"创建锻炼计划失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@exercise_plan_bp.route('/<plan_id>', methods=['PUT'])
@require_auth
def update_exercise_plan(plan_id):
    """更新锻炼计划"""
    try:
        db = get_db()
        data = request.get_json()

        # 获取当前用户信息
        from flask import g
        current_user = getattr(g, 'current_user', {})
        current_username = current_user.get('username', '')
        current_role = current_user.get('role', '')

        # 查找计划
        if ObjectId.is_valid(plan_id):
            query = {'_id': ObjectId(plan_id)}
        else:
            query = {'plan_id': plan_id}

        # 权限控制：医生只能更新自己创建的计划
        if current_role != '管理员':
            query['created_by'] = current_username

        plan = db.exercise_plans.find_one(query)
        if not plan:
            return jsonify({
                'success': False,
                'error': '锻炼计划不存在或无权限访问'
            }), 404
        
        # 获取当前用户信息
        from flask import g
        current_user = getattr(g, 'current_user', {})
        
        # 准备更新数据
        update_data = {
            'updated_at': datetime.utcnow(),
            'last_modified_by': current_user.get('username', '')
        }
        
        # 可更新的字段
        allowed_fields = ['plan_name', 'description', 'status']
        for field in allowed_fields:
            if field in data:
                update_data[field] = data[field]
        
        # 更新数据库
        result = db.exercise_plans.update_one(query, {'$set': update_data})
        
        if result.modified_count > 0:
            # 返回更新后的计划
            updated_plan = db.exercise_plans.find_one(query)
            return jsonify({
                'success': True,
                'data': serialize_doc(updated_plan),
                'message': '锻炼计划更新成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '更新失败，没有数据被修改'
            }), 400
        
    except Exception as e:
        logger.error(f"更新锻炼计划失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@exercise_plan_bp.route('/<plan_id>', methods=['DELETE'])
@require_auth
def delete_exercise_plan(plan_id):
    """删除锻炼计划（软删除，设置状态为cancelled）"""
    try:
        db = get_db()

        # 获取当前用户信息
        from flask import g
        current_user = getattr(g, 'current_user', {})
        current_username = current_user.get('username', '')
        current_role = current_user.get('role', '')

        # 查找计划
        if ObjectId.is_valid(plan_id):
            query = {'_id': ObjectId(plan_id)}
        else:
            query = {'plan_id': plan_id}

        # 权限控制：医生只能删除自己创建的计划
        if current_role != '管理员':
            query['created_by'] = current_username

        plan = db.exercise_plans.find_one(query)
        if not plan:
            return jsonify({
                'success': False,
                'error': '锻炼计划不存在或无权限访问'
            }), 404
        
        # 获取当前用户信息
        from flask import g
        current_user = getattr(g, 'current_user', {})
        
        # 软删除：设置状态为cancelled
        result = db.exercise_plans.update_one(
            query,
            {'$set': {
                'status': 'cancelled',
                'updated_at': datetime.utcnow(),
                'last_modified_by': current_user.get('username', '')
            }}
        )
        
        if result.modified_count > 0:
            return jsonify({
                'success': True,
                'message': '锻炼计划已取消'
            })
        else:
            return jsonify({
                'success': False,
                'error': '删除失败'
            }), 500
        
    except Exception as e:
        logger.error(f"删除锻炼计划失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
