#!/usr/bin/env python3
"""
医生-患者关系API示例
展示如何在Flask后端中实现医生权限控制的API接口
"""

from flask import Flask, request, jsonify
from functools import wraps
import pymongo
from datetime import datetime, timedelta
import jwt

app = Flask(__name__)

# 数据库连接
def get_db():
    """获取数据库连接"""
    client = pymongo.MongoClient('mongodb://localhost:27017')
    return client['med_call_records']

# JWT认证装饰器
def token_required(f):
    """JWT token验证装饰器"""
    @wraps(f)
    def decorated(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'error': '缺少认证token'}), 401
        
        try:
            # 移除 "Bearer " 前缀
            if token.startswith('Bearer '):
                token = token[7:]
            
            # 这里应该使用实际的JWT密钥
            data = jwt.decode(token, 'your-secret-key', algorithms=['HS256'])
            current_user = data['username']
            current_role = data['role']
            
        except jwt.ExpiredSignatureError:
            return jsonify({'error': 'Token已过期'}), 401
        except jwt.InvalidTokenError:
            return jsonify({'error': '无效的token'}), 401
        
        return f(current_user, current_role, *args, **kwargs)
    
    return decorated

# 医生权限检查装饰器
def doctor_required(f):
    """医生权限检查装饰器"""
    @wraps(f)
    def decorated(current_user, current_role, *args, **kwargs):
        if current_role != '医生':
            return jsonify({'error': '需要医生权限'}), 403
        return f(current_user, current_role, *args, **kwargs)
    
    return decorated

@app.route('/api/doctor/patients', methods=['GET'])
@token_required
@doctor_required
def get_doctor_patients(current_user, current_role):
    """获取医生管理的患者列表"""
    try:
        db = get_db()
        
        # 查询该医生管理的患者
        patients = list(db.personnel.find({
            "assigned_doctor": current_user
        }).sort("name", 1))
        
        # 格式化返回数据
        patient_list = []
        for patient in patients:
            patient_data = {
                'id': str(patient['_id']),
                'name': patient['name'],
                'phone': patient['phone'],
                'age': patient['age'],
                'gender': patient['gender'],
                'enrollment_date': patient['enrollment_date'],
                'training_status': patient['training_status'],
                'assignment_date': patient.get('assignment_date'),
                'created_at': patient['created_at'].isoformat() if patient.get('created_at') else None,
                'updated_at': patient['updated_at'].isoformat() if patient.get('updated_at') else None
            }
            patient_list.append(patient_data)
        
        return jsonify({
            'success': True,
            'data': patient_list,
            'total': len(patient_list),
            'doctor': current_user
        })
    
    except Exception as e:
        return jsonify({'error': f'查询患者失败: {str(e)}'}), 500

@app.route('/api/doctor/patients/<patient_phone>/calls', methods=['GET'])
@token_required
@doctor_required
def get_patient_calls(current_user, current_role, patient_phone):
    """获取指定患者的通话记录"""
    try:
        db = get_db()
        
        # 首先验证该患者是否属于当前医生管理
        patient = db.personnel.find_one({
            "phone": patient_phone,
            "assigned_doctor": current_user
        })
        
        if not patient:
            return jsonify({'error': '患者不存在或不属于您管理'}), 404
        
        # 获取分页参数
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 20))
        skip = (page - 1) * limit
        
        # 查询通话记录
        calls = list(db.call_records.find({
            "手机号": patient_phone
        }).sort("创建时间", -1).skip(skip).limit(limit))
        
        # 获取总数
        total = db.call_records.count_documents({"手机号": patient_phone})
        
        # 格式化返回数据
        call_list = []
        for call in calls:
            call_data = {
                'id': str(call['_id']),
                'patient_name': call.get('患者名字'),
                'phone': call.get('手机号'),
                'call_time': call.get('通话时间'),
                'record_date': call.get('记录日期'),
                'call_status': call.get('拨号状态'),
                'training_completion': call.get('训练完成情况'),
                'training_count': call.get('训练次数'),
                'training_duration': call.get('训练时长'),
                'has_discomfort': call.get('是否有不适感'),
                'discomfort_content': call.get('不适感内容'),
                'need_doctor_contact': call.get('是否需要医生人工和患者联系'),
                'equipment_problem': call.get('锻炼辅助仪器是否有问题'),
                'equipment_problem_content': call.get('锻炼辅助仪器问题内容'),
                'created_at': call['创建时间'].isoformat() if call.get('创建时间') else None
            }
            call_list.append(call_data)
        
        return jsonify({
            'success': True,
            'data': call_list,
            'pagination': {
                'page': page,
                'limit': limit,
                'total': total,
                'pages': (total + limit - 1) // limit
            },
            'patient': {
                'name': patient['name'],
                'phone': patient['phone']
            }
        })
    
    except Exception as e:
        return jsonify({'error': f'查询通话记录失败: {str(e)}'}), 500

@app.route('/api/doctor/statistics', methods=['GET'])
@token_required
@doctor_required
def get_doctor_statistics(current_user, current_role):
    """获取医生的统计信息"""
    try:
        db = get_db()
        
        # 患者统计
        total_patients = db.personnel.count_documents({
            "assigned_doctor": current_user
        })
        
        training_patients = db.personnel.count_documents({
            "assigned_doctor": current_user,
            "training_status": "训练中"
        })
        
        # 获取患者手机号列表
        patients = list(db.personnel.find({
            "assigned_doctor": current_user
        }, {"phone": 1}))
        patient_phones = [p['phone'] for p in patients]
        
        # 通话统计
        total_calls = 0
        recent_calls = 0
        successful_calls = 0
        
        if patient_phones:
            total_calls = db.call_records.count_documents({
                "手机号": {"$in": patient_phones}
            })
            
            # 最近7天的通话
            seven_days_ago = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
            recent_calls = db.call_records.count_documents({
                "手机号": {"$in": patient_phones},
                "记录日期": {"$gte": seven_days_ago}
            })
            
            # 成功通话（通话结束状态）
            successful_calls = db.call_records.count_documents({
                "手机号": {"$in": patient_phones},
                "拨号状态": {"$regex": "通话结束"}
            })
        
        # 需要关注的患者
        need_attention = list(db.call_records.find({
            "手机号": {"$in": patient_phones},
            "是否需要医生人工和患者联系": "是"
        }).sort("创建时间", -1).limit(5))
        
        attention_list = []
        for record in need_attention:
            attention_list.append({
                'patient_name': record.get('患者名字'),
                'phone': record.get('手机号'),
                'call_time': record.get('通话时间'),
                'reason': record.get('不适感内容', '需要医生联系')
            })
        
        return jsonify({
            'success': True,
            'data': {
                'patient_statistics': {
                    'total_patients': total_patients,
                    'training_patients': training_patients,
                    'paused_patients': total_patients - training_patients
                },
                'call_statistics': {
                    'total_calls': total_calls,
                    'recent_calls': recent_calls,
                    'successful_calls': successful_calls,
                    'success_rate': round(successful_calls / total_calls * 100, 2) if total_calls > 0 else 0
                },
                'need_attention': attention_list
            },
            'doctor': current_user
        })
    
    except Exception as e:
        return jsonify({'error': f'获取统计信息失败: {str(e)}'}), 500

@app.route('/api/doctor/patients/<patient_phone>', methods=['PUT'])
@token_required
@doctor_required
def update_patient_status(current_user, current_role, patient_phone):
    """更新患者训练状态"""
    try:
        db = get_db()
        
        # 验证患者是否属于当前医生管理
        patient = db.personnel.find_one({
            "phone": patient_phone,
            "assigned_doctor": current_user
        })
        
        if not patient:
            return jsonify({'error': '患者不存在或不属于您管理'}), 404
        
        # 获取更新数据
        data = request.get_json()
        training_status = data.get('training_status')
        
        if not training_status:
            return jsonify({'error': '缺少训练状态参数'}), 400
        
        # 验证训练状态值
        valid_statuses = ['训练中', '未开始', '暂停', '终止', '休息']
        if training_status not in valid_statuses:
            return jsonify({'error': f'无效的训练状态，有效值: {valid_statuses}'}), 400
        
        # 更新患者状态
        result = db.personnel.update_one(
            {"phone": patient_phone, "assigned_doctor": current_user},
            {
                "$set": {
                    "training_status": training_status,
                    "updated_at": datetime.utcnow()
                }
            }
        )
        
        if result.modified_count > 0:
            return jsonify({
                'success': True,
                'message': f'患者 {patient["name"]} 的训练状态已更新为: {training_status}'
            })
        else:
            return jsonify({'error': '更新失败'}), 500
    
    except Exception as e:
        return jsonify({'error': f'更新患者状态失败: {str(e)}'}), 500

if __name__ == '__main__':
    print("🏥 医生-患者关系API示例")
    print("📋 可用的API接口:")
    print("   GET  /api/doctor/patients - 获取医生管理的患者列表")
    print("   GET  /api/doctor/patients/<phone>/calls - 获取患者通话记录")
    print("   GET  /api/doctor/statistics - 获取医生统计信息")
    print("   PUT  /api/doctor/patients/<phone> - 更新患者训练状态")
    print("\n💡 使用说明:")
    print("1. 所有接口都需要JWT认证")
    print("2. 医生只能查看和管理自己分配的患者")
    print("3. 支持分页查询和状态过滤")
    print("4. 提供详细的统计信息和需要关注的患者列表")
    
    # 注意：这只是示例代码，实际使用时需要集成到现有的Flask应用中
    # app.run(debug=True, port=5001)
