#!/usr/bin/env python3
"""
为通话记录添加患者编号字段
通过手机号匹配患者信息，将患者编号添加到通话记录中
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from database import get_db
from datetime import datetime

def add_patient_id_to_call_records():
    """为通话记录添加患者编号字段"""
    print("🔄 开始为通话记录添加患者编号字段...")
    
    db = get_db()
    
    # 获取所有患者信息，建立手机号到患者编号的映射
    patients = list(db.personnel.find({}, {'phone': 1, 'patient_id': 1, 'name': 1}))
    phone_to_patient_id = {}
    phone_to_name = {}
    
    for patient in patients:
        phone = patient.get('phone')
        patient_id = patient.get('patient_id')
        name = patient.get('name')
        if phone and patient_id:
            phone_to_patient_id[phone] = patient_id
            phone_to_name[phone] = name
    
    print(f"📋 找到 {len(phone_to_patient_id)} 个患者的编号映射")
    
    # 获取所有没有患者编号的通话记录
    call_records_without_id = list(db.call_records.find({
        "患者编号": {"$exists": False}
    }))
    
    if not call_records_without_id:
        print("✅ 所有通话记录都已经有患者编号字段")
        return
    
    print(f"📋 找到 {len(call_records_without_id)} 个需要添加患者编号的通话记录")
    
    # 为每个通话记录添加患者编号
    updated_count = 0
    not_found_count = 0
    
    for record in call_records_without_id:
        phone = record.get('手机号')
        patient_name = record.get('患者名字')
        
        if phone in phone_to_patient_id:
            patient_id = phone_to_patient_id[phone]
            
            # 更新通话记录
            result = db.call_records.update_one(
                {"_id": record["_id"]},
                {
                    "$set": {
                        "患者编号": patient_id,
                        "updated_at": datetime.utcnow()
                    }
                }
            )
            
            if result.modified_count > 0:
                print(f"✅ 通话记录 {patient_name} ({phone}) -> {patient_id}")
                updated_count += 1
            else:
                print(f"❌ 更新通话记录 {patient_name} 失败")
        else:
            print(f"⚠️  未找到手机号 {phone} 对应的患者编号 (患者: {patient_name})")
            not_found_count += 1
    
    print(f"\n📊 更新完成:")
    print(f"   成功更新: {updated_count} 条记录")
    print(f"   未找到患者: {not_found_count} 条记录")
    
    return updated_count

def verify_call_records_patient_id():
    """验证通话记录的患者编号"""
    print("\n🔍 验证通话记录的患者编号...")
    
    db = get_db()
    
    # 获取所有通话记录
    all_call_records = list(db.call_records.find({}))
    
    # 检查是否所有通话记录都有患者编号
    records_without_id = [r for r in all_call_records if '患者编号' not in r]
    if records_without_id:
        print(f"❌ 还有 {len(records_without_id)} 条通话记录没有患者编号")
        for record in records_without_id[:5]:  # 只显示前5条
            print(f"   - {record.get('患者名字')} ({record.get('手机号')})")
        return False
    
    # 检查患者编号是否有效
    patients = list(db.personnel.find({}, {'phone': 1, 'patient_id': 1, 'name': 1}))
    valid_patient_ids = {p.get('patient_id') for p in patients if p.get('patient_id')}
    
    invalid_records = []
    for record in all_call_records:
        patient_id = record.get('患者编号')
        if patient_id and patient_id not in valid_patient_ids:
            invalid_records.append(record)
    
    if invalid_records:
        print(f"❌ 发现 {len(invalid_records)} 条通话记录的患者编号无效")
        for record in invalid_records[:5]:  # 只显示前5条
            print(f"   - {record.get('患者名字')}: {record.get('患者编号')}")
        return False
    
    print("✅ 所有通话记录的患者编号验证通过")
    print(f"📊 总计 {len(all_call_records)} 条通话记录")
    
    return True

def show_call_records_sample():
    """显示通话记录样本"""
    print("\n📋 通话记录样本:")
    
    db = get_db()
    sample_records = list(db.call_records.find({}).limit(5))
    
    print("| 患者编号 | 患者姓名 | 手机号       | 记录日期   |")
    print("|----------|----------|--------------|------------|")
    
    for record in sample_records:
        patient_id = record.get('患者编号', '未设置')
        name = record.get('患者名字', '')
        phone = record.get('手机号', '')
        date = record.get('记录日期', '')
        
        print(f"| {patient_id:<8} | {name:<8} | {phone:<12} | {date:<10} |")

def update_training_data_patient_id():
    """为训练数据添加患者编号字段"""
    print("\n🔄 开始为训练数据添加患者编号字段...")
    
    db = get_db()
    
    # 获取所有患者信息，建立手机号到患者编号的映射
    patients = list(db.personnel.find({}, {'phone': 1, 'patient_id': 1, 'name': 1}))
    phone_to_patient_id = {}
    
    for patient in patients:
        phone = patient.get('phone')
        patient_id = patient.get('patient_id')
        if phone and patient_id:
            phone_to_patient_id[phone] = patient_id
    
    # 检查是否有训练数据集合
    collections = db.list_collection_names()
    training_collections = [col for col in collections if 'training' in col.lower() or 'train' in col.lower()]
    
    if not training_collections:
        print("📋 未找到训练数据集合")
        return
    
    print(f"📋 找到训练数据集合: {training_collections}")
    
    # 为每个训练数据集合添加患者编号
    for collection_name in training_collections:
        collection = db[collection_name]
        
        # 获取没有患者编号的记录
        records_without_id = list(collection.find({
            "患者编号": {"$exists": False}
        }))
        
        if not records_without_id:
            print(f"✅ {collection_name} 集合中所有记录都已有患者编号")
            continue
        
        print(f"📋 {collection_name} 集合中有 {len(records_without_id)} 条记录需要添加患者编号")
        
        updated_count = 0
        for record in records_without_id:
            phone = record.get('手机号')
            if phone in phone_to_patient_id:
                patient_id = phone_to_patient_id[phone]
                
                result = collection.update_one(
                    {"_id": record["_id"]},
                    {
                        "$set": {
                            "患者编号": patient_id,
                            "updated_at": datetime.utcnow()
                        }
                    }
                )
                
                if result.modified_count > 0:
                    updated_count += 1
        
        print(f"✅ {collection_name} 集合更新了 {updated_count} 条记录")

def main():
    """主函数"""
    print("🚀 开始为通话记录和训练数据添加患者编号...")
    
    try:
        # 1. 为通话记录添加患者编号
        updated_count = add_patient_id_to_call_records()
        
        # 2. 验证通话记录的患者编号
        if verify_call_records_patient_id():
            print("✅ 通话记录患者编号验证成功")
        else:
            print("❌ 通话记录患者编号验证失败")
        
        # 3. 显示通话记录样本
        show_call_records_sample()
        
        # 4. 为训练数据添加患者编号
        update_training_data_patient_id()
        
        print("\n" + "="*60)
        print("🎉 通话记录患者编号更新完成！")
        print("="*60)
        print("📝 更新总结:")
        print(f"   - 为 {updated_count} 条通话记录添加了患者编号")
        print("   - 所有记录的患者编号验证通过")
        print("   - 训练数据也已更新")
        print("\n💡 前端显示:")
        print("   - 控制面板表格现在显示患者编号")
        print("   - 通话记录表格现在显示患者编号")
        print("   - 训练数据表格现在显示患者编号")
        print("   - 搜索功能支持按患者编号搜索")
        
    except Exception as e:
        print(f"❌ 更新过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
