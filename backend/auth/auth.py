import jwt
import bcrypt
from datetime import datetime
from functools import wraps
from flask import request, jsonify
from config import Config

def generate_token(user_data):
    """生成JWT token"""
    payload = {
        'user_id': str(user_data['_id']),
        'username': user_data['username'],
        'role': user_data['role'],
        'exp': datetime.utcnow() + Config.JWT_ACCESS_TOKEN_EXPIRES,
        'iat': datetime.utcnow()
    }
    return jwt.encode(payload, Config.JWT_SECRET_KEY, algorithm='HS256')

def verify_token(token):
    """验证JWT token"""
    try:
        payload = jwt.decode(token, Config.JWT_SECRET_KEY, algorithms=['HS256'])
        return payload
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None

def require_auth(f):
    """装饰器：要求认证"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'success': False, 'error': '缺少认证token'}), 401
        
        if token.startswith('Bearer '):
            token = token[7:]
        
        payload = verify_token(token)
        if not payload:
            return jsonify({'success': False, 'error': 'token无效或已过期'}), 401
        
        request.current_user = payload
        return f(*args, **kwargs)
    return decorated_function

def require_admin(f):
    """装饰器：要求管理员权限"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not hasattr(request, 'current_user') or request.current_user.get('role') != '管理员':
            return jsonify({'success': False, 'error': '需要管理员权限'}), 403
        return f(*args, **kwargs)
    return decorated_function

def get_user_patient_filter(current_user):
    """根据用户角色获取患者过滤条件"""
    if current_user.get('role') == '管理员':
        # 管理员可以看到所有患者
        return {}
    elif current_user.get('role') == '医生':
        # 医生只能看到自己管理的患者
        return {'assigned_doctor': current_user.get('username')}
    else:
        # 其他角色不能访问患者数据
        return {'_id': {'$exists': False}}  # 返回空结果

def get_user_call_records_filter(current_user, db):
    """根据用户角色获取通话记录过滤条件"""
    if current_user.get('role') == '管理员':
        # 管理员可以看到所有通话记录
        return {}
    elif current_user.get('role') == '医生':
        # 医生只能看到自己管理患者的通话记录
        # 首先获取该医生管理的患者手机号列表
        patients = list(db.personnel.find(
            {'assigned_doctor': current_user.get('username')},
            {'phone': 1}
        ))
        patient_phones = [p['phone'] for p in patients]

        if patient_phones:
            return {'手机号': {'$in': patient_phones}}
        else:
            return {'_id': {'$exists': False}}  # 返回空结果
    else:
        # 其他角色不能访问通话记录
        return {'_id': {'$exists': False}}  # 返回空结果

def require_doctor_filter(f):
    """装饰器：根据医生权限过滤数据"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not hasattr(request, 'current_user'):
            return jsonify({'success': False, 'error': '用户未认证'}), 401

        # 将用户信息和过滤函数传递给路由函数
        request.user_patient_filter = get_user_patient_filter(request.current_user)
        request.is_admin = request.current_user.get('role') == '管理员'
        request.is_doctor = request.current_user.get('role') == '医生'

        return f(*args, **kwargs)
    return decorated_function

def hash_password(password):
    """加密密码"""
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

def verify_password(password, password_hash):
    """验证密码"""
    return bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8')) 