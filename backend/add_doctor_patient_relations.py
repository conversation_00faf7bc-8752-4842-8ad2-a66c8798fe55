#!/usr/bin/env python3
"""
添加医生-患者关系数据脚本
在personnel集合中添加assigned_doctor字段，建立医生和患者的对应关系
"""

import pymongo
from datetime import datetime
import sys
import os

def connect_to_database():
    """连接到MongoDB数据库"""
    try:
        # 使用默认的MongoDB连接
        mongo_url = 'mongodb://localhost:27017'
        client = pymongo.MongoClient(mongo_url, serverSelectionTimeoutMS=5000)
        
        # 测试连接
        client.server_info()
        
        # 连接到 med_call_records 数据库
        db = client['med_call_records']
        
        print(f"✅ 成功连接到数据库: {mongo_url}")
        print(f"✅ 数据库名称: med_call_records")
        
        return client, db
    
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None, None

def get_existing_doctors(db):
    """获取现有的医生用户"""
    doctors = list(db.sys_info.find({
        "doc_type": "user_account",
        "role": "医生",
        "is_active": True
    }))
    
    print(f"📋 找到 {len(doctors)} 个医生用户:")
    for doctor in doctors:
        print(f"  - {doctor.get('username')} ({doctor.get('full_name')})")
    
    return doctors

def get_existing_patients(db):
    """获取现有的患者"""
    patients = list(db.personnel.find({}))
    
    print(f"📋 找到 {len(patients)} 个患者:")
    for patient in patients:
        print(f"  - {patient.get('name')} ({patient.get('phone')})")
    
    return patients

def add_doctor_patient_relations(db):
    """添加医生-患者关系"""
    
    # 获取现有医生和患者
    doctors = get_existing_doctors(db)
    patients = get_existing_patients(db)
    
    if not doctors:
        print("❌ 没有找到医生用户，无法建立关系")
        return False
    
    if not patients:
        print("❌ 没有找到患者，无法建立关系")
        return False
    
    # 为每个患者分配医生（简单的轮询分配）
    assignment_date = datetime.now().strftime('%Y-%m-%d')
    updated_count = 0
    
    for i, patient in enumerate(patients):
        # 轮询分配医生
        doctor = doctors[i % len(doctors)]
        
        # 检查患者是否已经有分配的医生
        if 'assigned_doctor' in patient:
            print(f"⚠️  患者 {patient['name']} 已经分配给医生 {patient.get('assigned_doctor')}")
            continue
        
        # 更新患者记录，添加医生信息
        update_result = db.personnel.update_one(
            {"_id": patient["_id"]},
            {
                "$set": {
                    "assigned_doctor": doctor["username"],
                    "assigned_doctor_name": doctor["full_name"],
                    "assignment_date": assignment_date,
                    "updated_at": datetime.utcnow()
                }
            }
        )
        
        if update_result.modified_count > 0:
            print(f"✅ 患者 {patient['name']} 已分配给医生 {doctor['full_name']} ({doctor['username']})")
            updated_count += 1
        else:
            print(f"❌ 更新患者 {patient['name']} 失败")
    
    print(f"\n📊 总计更新了 {updated_count} 个患者的医生分配关系")
    return updated_count > 0

def create_sample_doctor_if_needed(db):
    """如果需要，创建示例医生账户"""
    
    # 检查是否有足够的医生
    doctor_count = db.sys_info.count_documents({
        "doc_type": "user_account",
        "role": "医生",
        "is_active": True
    })
    
    if doctor_count < 2:
        print("📝 创建额外的示例医生账户...")
        
        # 导入密码哈希函数
        try:
            sys.path.append(os.path.dirname(__file__))
            from auth.auth import hash_password
        except ImportError:
            print("❌ 无法导入密码哈希函数，跳过创建医生账户")
            return
        
        sample_doctors = [
            {
                'doc_type': 'user_account',
                'username': 'doctor02',
                'password_hash': hash_password('doctor123'),
                'role': '医生',
                'full_name': '王医生',
                'email': '<EMAIL>',
                'phone': '***********',
                'is_active': True,
                'login_count': 0,
                'last_login': None,
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow()
            },
            {
                'doc_type': 'user_account',
                'username': 'doctor03',
                'password_hash': hash_password('doctor123'),
                'role': '医生',
                'full_name': '李医生',
                'email': '<EMAIL>',
                'phone': '***********',
                'is_active': True,
                'login_count': 0,
                'last_login': None,
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow()
            }
        ]
        
        for doctor in sample_doctors:
            # 检查用户名是否已存在
            existing = db.sys_info.find_one({
                "doc_type": "user_account",
                "username": doctor["username"]
            })
            
            if not existing:
                result = db.sys_info.insert_one(doctor)
                print(f"✅ 创建医生账户: {doctor['username']} ({doctor['full_name']})")
            else:
                print(f"⚠️  医生账户 {doctor['username']} 已存在")

def verify_relations(db):
    """验证医生-患者关系"""
    print("\n🔍 验证医生-患者关系:")
    
    # 查询所有有分配医生的患者
    patients_with_doctors = list(db.personnel.find({
        "assigned_doctor": {"$exists": True}
    }))
    
    if not patients_with_doctors:
        print("❌ 没有找到任何医生-患者关系")
        return
    
    # 按医生分组显示
    doctor_patients = {}
    for patient in patients_with_doctors:
        doctor = patient.get('assigned_doctor')
        doctor_name = patient.get('assigned_doctor_name', doctor)
        
        if doctor not in doctor_patients:
            doctor_patients[doctor] = {
                'name': doctor_name,
                'patients': []
            }
        
        doctor_patients[doctor]['patients'].append({
            'name': patient['name'],
            'phone': patient['phone'],
            'assignment_date': patient.get('assignment_date', 'N/A')
        })
    
    for doctor_username, info in doctor_patients.items():
        print(f"\n👨‍⚕️ 医生: {info['name']} ({doctor_username})")
        print(f"   管理患者数: {len(info['patients'])}")
        for patient in info['patients']:
            print(f"   - {patient['name']} ({patient['phone']}) [分配日期: {patient['assignment_date']}]")

def main():
    """主函数"""
    print("🏥 开始添加医生-患者关系数据...")
    
    # 连接数据库
    client, db = connect_to_database()
    if client is None or db is None:
        print("❌ 无法连接到数据库，退出程序")
        sys.exit(1)
    
    try:
        # 创建示例医生账户（如果需要）
        create_sample_doctor_if_needed(db)
        
        # 添加医生-患者关系
        success = add_doctor_patient_relations(db)
        
        if success:
            # 验证关系
            verify_relations(db)
            print("\n✅ 医生-患者关系数据添加完成！")
        else:
            print("\n❌ 医生-患者关系数据添加失败")
    
    except Exception as e:
        print(f"❌ 处理过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭数据库连接
        if client is not None:
            client.close()
            print("🔒 数据库连接已关闭")

if __name__ == "__main__":
    main()
