#!/usr/bin/env python3
"""
创建数据库索引脚本
为医生-患者关系查询优化数据库性能
"""

import pymongo
import sys

def connect_to_database():
    """连接到MongoDB数据库"""
    try:
        mongo_url = 'mongodb://localhost:27017'
        client = pymongo.MongoClient(mongo_url, serverSelectionTimeoutMS=5000)
        client.server_info()
        db = client['med_call_records']
        print(f"✅ 成功连接到数据库: {mongo_url}")
        return client, db
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None, None

def create_indexes(db):
    """创建数据库索引"""
    
    indexes_to_create = [
        # personnel 集合索引
        {
            'collection': 'personnel',
            'indexes': [
                {'fields': [('phone', 1)], 'options': {'unique': True}, 'name': 'phone_unique'},
                {'fields': [('name', 1)], 'options': {}, 'name': 'name_index'},
                {'fields': [('training_status', 1)], 'options': {}, 'name': 'training_status_index'},
                {'fields': [('assigned_doctor', 1)], 'options': {}, 'name': 'assigned_doctor_index'},
                {'fields': [('assigned_doctor', 1), ('training_status', 1)], 'options': {}, 'name': 'doctor_status_compound'},
            ]
        },
        # call_records 集合索引
        {
            'collection': 'call_records',
            'indexes': [
                {'fields': [('手机号', 1)], 'options': {}, 'name': 'patient_phone_index'},
                {'fields': [('患者名字', 1)], 'options': {}, 'name': 'patient_name_index'},
                {'fields': [('记录日期', 1)], 'options': {}, 'name': 'record_date_index'},
                {'fields': [('通话时间', -1)], 'options': {}, 'name': 'call_time_desc_index'},
                {'fields': [('是否需要医生人工和患者联系', 1)], 'options': {}, 'name': 'need_doctor_contact_index'},
                {'fields': [('是否有不适感', 1)], 'options': {}, 'name': 'has_discomfort_index'},
                {'fields': [('手机号', 1), ('创建时间', -1)], 'options': {}, 'name': 'phone_time_compound'},
            ]
        },
        # sys_info 集合索引
        {
            'collection': 'sys_info',
            'indexes': [
                {'fields': [('doc_type', 1)], 'options': {}, 'name': 'doc_type_index'},
                {'fields': [('username', 1)], 'options': {'unique': True}, 'name': 'username_unique'},
                {'fields': [('role', 1)], 'options': {}, 'name': 'role_index'},
                {'fields': [('doc_type', 1), ('role', 1)], 'options': {}, 'name': 'doctype_role_compound'},
            ]
        }
    ]
    
    total_created = 0
    total_existing = 0
    
    for collection_info in indexes_to_create:
        collection_name = collection_info['collection']
        collection = db[collection_name]
        
        print(f"\n📋 处理集合: {collection_name}")
        
        # 获取现有索引
        existing_indexes = list(collection.list_indexes())
        existing_index_names = {idx['name'] for idx in existing_indexes}
        
        for index_info in collection_info['indexes']:
            index_name = index_info['name']
            
            if index_name in existing_index_names:
                print(f"   ⚠️  索引 {index_name} 已存在")
                total_existing += 1
                continue
            
            try:
                # 创建索引
                collection.create_index(
                    index_info['fields'],
                    name=index_name,
                    **index_info['options']
                )
                print(f"   ✅ 创建索引: {index_name}")
                total_created += 1
                
            except Exception as e:
                print(f"   ❌ 创建索引 {index_name} 失败: {e}")
    
    print(f"\n📊 索引创建总结:")
    print(f"   新创建索引: {total_created}")
    print(f"   已存在索引: {total_existing}")
    
    return total_created > 0

def list_all_indexes(db):
    """列出所有集合的索引"""
    print("\n🔍 当前数据库索引状态:")
    
    collections = ['personnel', 'call_records', 'sys_info']
    
    for collection_name in collections:
        collection = db[collection_name]
        indexes = list(collection.list_indexes())
        
        print(f"\n📋 集合: {collection_name}")
        print(f"   索引数量: {len(indexes)}")
        
        for idx in indexes:
            index_name = idx['name']
            keys = idx.get('key', {})
            unique = idx.get('unique', False)
            
            # 格式化索引键
            key_str = ', '.join([f"{k}:{v}" for k, v in keys.items()])
            unique_str = " (唯一)" if unique else ""
            
            print(f"   - {index_name}: {key_str}{unique_str}")

def analyze_query_performance(db):
    """分析查询性能"""
    print("\n⚡ 查询性能分析:")
    
    # 测试医生患者查询
    print("\n1. 医生患者查询测试:")
    try:
        explain_result = db.personnel.find({"assigned_doctor": "doctor01"}).explain()
        execution_stats = explain_result.get('executionStats', {})
        total_examined = execution_stats.get('totalDocsExamined', 0)
        total_returned = execution_stats.get('totalDocsReturned', 0)
        
        print(f"   查询: assigned_doctor = 'doctor01'")
        print(f"   检查文档数: {total_examined}")
        print(f"   返回文档数: {total_returned}")
        print(f"   效率: {'高效' if total_examined == total_returned else '需要优化'}")
        
    except Exception as e:
        print(f"   ❌ 性能分析失败: {e}")
    
    # 测试通话记录查询
    print("\n2. 通话记录查询测试:")
    try:
        explain_result = db.call_records.find({"手机号": "18971492577"}).explain()
        execution_stats = explain_result.get('executionStats', {})
        total_examined = execution_stats.get('totalDocsExamined', 0)
        total_returned = execution_stats.get('totalDocsReturned', 0)
        
        print(f"   查询: 手机号 = '18971492577'")
        print(f"   检查文档数: {total_examined}")
        print(f"   返回文档数: {total_returned}")
        print(f"   效率: {'高效' if total_examined <= total_returned * 2 else '需要优化'}")
        
    except Exception as e:
        print(f"   ❌ 性能分析失败: {e}")

def main():
    """主函数"""
    print("🚀 开始创建数据库索引...")
    
    # 连接数据库
    client, db = connect_to_database()
    if client is None or db is None:
        print("❌ 无法连接到数据库，退出程序")
        sys.exit(1)
    
    try:
        # 创建索引
        success = create_indexes(db)
        
        # 列出所有索引
        list_all_indexes(db)
        
        # 分析查询性能
        analyze_query_performance(db)
        
        if success:
            print("\n✅ 数据库索引优化完成！")
            print("\n💡 性能优化建议:")
            print("1. 医生查询患者时使用 assigned_doctor 索引")
            print("2. 患者通话记录查询使用 手机号 索引")
            print("3. 复合查询可以使用组合索引提高性能")
            print("4. 定期监控查询性能，根据需要调整索引策略")
        else:
            print("\n⚠️  所有索引都已存在，无需创建新索引")
    
    except Exception as e:
        print(f"❌ 处理过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭数据库连接
        if client is not None:
            client.close()
            print("\n🔒 数据库连接已关闭")

if __name__ == "__main__":
    main()
